{"__meta": {"id": "Xb370ad3e1b5f8ecb35dd5828aa22a3bc", "datetime": "2025-08-02 10:04:52", "utime": **********.549174, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:04:52] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.526663, "xdebug_link": null, "collector": "log"}, {"message": "[10:04:52] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.533353, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754129090.979949, "end": **********.549241, "duration": 1.5692920684814453, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1754129090.979949, "relative_start": 0, "end": **********.400311, "relative_end": **********.400311, "duration": 1.4203619956970215, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.400342, "relative_start": 1.4203929901123047, "end": **********.549243, "relative_end": 1.9073486328125e-06, "duration": 0.14890098571777344, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46530968, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=2998\" onclick=\"\">app/Http/Controllers/LeadController.php:2998-3054</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007580000000000001, "accumulated_duration_str": "7.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4901628, "duration": 0.0053, "duration_str": "5.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 69.921}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5191782, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 69.921, "width_percent": 20.317}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3025}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.527039, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:3025", "source": "app/Http/Controllers/LeadController.php:3025", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3025", "ajax": false, "filename": "LeadController.php", "line": "3025"}, "connection": "radhe_same", "start_percent": 90.237, "width_percent": 9.763}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-646238665 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-646238665\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1249753682 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249753682\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-633601270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-633601270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1340196895 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkdmM0t2ZUNkMmtlLzhlaFRzZXJjU0E9PSIsInZhbHVlIjoieEltRVg3cUhBOUZVVytnZjhmbXJ4eUUrcDlnMlMzOGNCZU04b0RkdDJBTEdHQm44V2RIbWVFZVkvQ3lSTVlnaVo3Q2I0WVczTWZxMTJGMFMvWUlMSm5CUEU5YWx1MzJ6V21zNHNJa2U5ekR3bE1BUXp4eERoUExUVWl6ZHdRcGhHYlhHYU1mTVYrcTVUM2N5NVg3MVZQd2g5VVQ5SVZnVTdCMzNCbmt5cGhIZ2h5QjVidjdmY2tHZ3VUUzd1VEw2K0tpdHFSNkt2eTZvUUR2WDVRR3h1WWNoLytFQmhVc0FKTE5QUVorV1E0TnExRGNWV1g5VmtMZW1ObVZtUjRYNjFYeDFnZWgwV3ppb2JiR2FLRGlHM0g0YUtKRDR1ZTlCN08zVTNPbGN0SEhrUFJRdEEvTWpNL0RxMDBOOUhNYnp2QTNNYUdxNndPUjI1Z1RnRm1Fa1Yyb25QU3RXOFhIZVRXRUFNT3doeUIxMGoxTkYyRHFUbEpxQllzODN1WmVKZlp3dEFNd1hPb2Y5TWRLZG5rMDZScUNDWXhLbHVKYnNIam5XWHpyR3lKVUJaMmhvRGxOYVBTU2pkd0dEbW1sdk04dzljaTFlQWtDUFZKcUpyTUh5SGRRdWZHcUJ2ejFFeEt4eUxpKzlRS2VYWVd0WUtpelpDOU1rOVNoaWVVdjEiLCJtYWMiOiI3ZmUwMmYyNWMzNWRkMDc4ZTIyYjU5NzAzMjA4ZGEyOTk4MzBjYTU0N2VjOWRkNGQyOTBmNDg3MWM2N2EyM2NmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InhJRHJBTDI4eVo4OFMrOURnVFkwUFE9PSIsInZhbHVlIjoiRU1DYjNaR3hLTEQrUDlMc3RZek9PZiszcXlDWjUyaHpGVXVUSTBnQkh5MG43TTVpNnhuNW9aZFhVQVJTNEhHaWswaXlIUEFTRXMrY0EyQ0dBZ1hCMG90Z2RBRmdZdUp0MVgyTEVKelJiZVdzd1dGSkFVWTRrUUp3TzlNQ2w5N0RaVHN6WXFyalZFRTR5TmZocnBEcTF4N202RWQ1aWVFd0dwd1p5WUVmVStPTTM0L0NwSkp6c29rRWFKUS9zdDAraVI0LzdNV3RGZncwTlZqKzBxR3E4Rmk2VEsrUVdRYXo4aGhiNkkyMVJzcUlVWDAzUTNFVHFyNk1YNUFXNVNNRnNMTkoxUVhiKzRIMGVCT0FVcXh6YkdjQnhZNnh0dTBsUWRvWExRVWppbjBwWmtrWm5VSTVYYmxKOUVCbWdlZVkrdmRFaVhEQ3Rmd3FEbURzQ0txYzNJRGhzYWhXS1dYd09BTVlQTVk1aDg1Zmx5WTlEUFo3eVljc3BTUHUzOG1pL0YzOW9UeURIdnVLcFRsMzFUK0VBMmRvU2Ywb2pzOVZyKzVOQjhqeFNCeHVBcm9QUDdhaGJrcEVBQWRXZjhpQmloWmp1QTFMVDJkSXFOR0VlZk91MEpQZytxcXBtdWhBazVKbjd5dE5obXk0bk1PSlFwUFVYREdmeTk4dWExb1QiLCJtYWMiOiJhZWM2MjllYzE1NWQzOTFhNDdiZDU5MzI3NmI3MjNmZjA4YzI1MDkzNTkyOGI1OWVmZDczOTQzM2RmMGE2ZjY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340196895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-19195261 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19195261\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1710397100 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:04:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImY2UWQzTEhEVFFpeXF5bTJQZVRDMGc9PSIsInZhbHVlIjoiU1ZDcVdtb0ZBUmZpMmd2YlhjVTZvWGxscnVLL2FiRzRJMEVFZTJFQzVQNzFDZ3AybHVoWEFKUlBiQS9tczlmWGNLZ1FFMzJVbFNtbnhsSk9QU215Mm90OW1KRGxaMDVXeXQzb1NKRitJWGdteVhCY3A1ZTZCNU9JamV0VUFObjBkVGwrOUREVGVFU2xaUGlrU0YzdVQyZFBtam9tQWRyZ1ViQzFmL3ZISFNSVEJ0VDJjNlA1eUNYQmtjZFd6MnRrcG92U0l3YlhMZ2krdEltVmVHTHg0ZlJpT2NkUHBGQmdNT1hONEp2dEQybGxoKzJiZTV5UjFTMlpJZVRWRXY5a0lQc3FCZU1ERVhQY0s1UFlOWHdwTnJIUDJWNDlrQ3VvU1kveG9GSUR5VnY0cHFVM2dhQkRTZEpFVWNGbE4ySjZUcy8wSTdBcEVjcnZEamR3bVBDU2FUSHI3Q2t4RCt1YmNKSmFTQ0xXUWZpWHFFVnMxSHk3NHBGdGcrc1ljMzVuUEhqV1lVcnJCWk1yL2Fxeng0K3JWdjkrTjFjWm8wK2s1MGtUU1pDbXpXTjRCRFEzZUtIemVLcnVET096RHRxTmxwQjF0TVgxMWY0bXYrUUJhbmJxbG81aHBZY29nK3lFajZ6WktoOHJsYmlyMEpjNWU4UmZCbkhTWkE0VXNtaEwiLCJtYWMiOiJlMWRjZWNlODFlZjQ0ZGY5ZmI0ODNmYmMxN2NhMjQwNTNmNzNmYWUyZTdmYzY3MmMxNDg0NTlkZmQ3OGRjYWI4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:04:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjJ1dlFkRCszK3ZqalV2UDN1b2RmdlE9PSIsInZhbHVlIjoiVXN5ZFE4VFpnYUg4U1ByNmhTcmVDRnlZamMrL3lyYlVuZEVPRExwd0ZDSEljZ015STZsUHB2YlhRaUNPdnM5MzR1MHJwRVFmOTNhNWNxZktUc2huWUs2UlF0Mko3TEVwaGc3TnA4eTR3SS93aklFczE4dURHa2piN0pXNi9YYTRsc3JTYmpDSFZsTlRWSXIzeVQ4eWtFcDNJMG5rU05uYVQzek5Ma0dSRTBhOS9meEtKNndXN3BOUk9PN283bHlBYUN0eWVPUFplVmxVUmpLam9tYkFnUHhIMlFPdzBjS1NCSEFxbm9WbHZ3ckM5dTBwbGtNN0RIdW4vU1NnVTg4N1pLUVc0ZU93emZ0eTRkNEhGVnEwUkxYQy9wcXByYlYweUpaaXY4NUk2UkQ1Tys5aXZ3emFnWU9nUGJUSXduVEtoZVNjM1l3RDhtalZXSkdOdC9NbTVMTmN4WGN6SHgwbkM3eU1LOEkveUlkcnBSTkNncjd4L2RjT2pBdHNUNHN0MHRuN1JBMk5qSmVhdmtGVXZiVkpOcjZSM0lzS3VGSXBNYzBoRWorL2JKVG9kWHZVVXliK2VmUW9yV3dPR1AvRXdKU21CTzdtRm9UOVJvWVdFU05xNGdNb2t2ZlVFMnBmNmRXZzFXeUZBTXllcTMvMGQweGJuZmFuMG5zU1BOU2kiLCJtYWMiOiIwZjRhOGI3MmJhMDcxNjEzYzJmMmIyZmU4MjhhYWZlNTQxYjljYzc5NmYxOTNjMGI5OTQ5YjQxMDcyMTY0NmQ5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:04:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImY2UWQzTEhEVFFpeXF5bTJQZVRDMGc9PSIsInZhbHVlIjoiU1ZDcVdtb0ZBUmZpMmd2YlhjVTZvWGxscnVLL2FiRzRJMEVFZTJFQzVQNzFDZ3AybHVoWEFKUlBiQS9tczlmWGNLZ1FFMzJVbFNtbnhsSk9QU215Mm90OW1KRGxaMDVXeXQzb1NKRitJWGdteVhCY3A1ZTZCNU9JamV0VUFObjBkVGwrOUREVGVFU2xaUGlrU0YzdVQyZFBtam9tQWRyZ1ViQzFmL3ZISFNSVEJ0VDJjNlA1eUNYQmtjZFd6MnRrcG92U0l3YlhMZ2krdEltVmVHTHg0ZlJpT2NkUHBGQmdNT1hONEp2dEQybGxoKzJiZTV5UjFTMlpJZVRWRXY5a0lQc3FCZU1ERVhQY0s1UFlOWHdwTnJIUDJWNDlrQ3VvU1kveG9GSUR5VnY0cHFVM2dhQkRTZEpFVWNGbE4ySjZUcy8wSTdBcEVjcnZEamR3bVBDU2FUSHI3Q2t4RCt1YmNKSmFTQ0xXUWZpWHFFVnMxSHk3NHBGdGcrc1ljMzVuUEhqV1lVcnJCWk1yL2Fxeng0K3JWdjkrTjFjWm8wK2s1MGtUU1pDbXpXTjRCRFEzZUtIemVLcnVET096RHRxTmxwQjF0TVgxMWY0bXYrUUJhbmJxbG81aHBZY29nK3lFajZ6WktoOHJsYmlyMEpjNWU4UmZCbkhTWkE0VXNtaEwiLCJtYWMiOiJlMWRjZWNlODFlZjQ0ZGY5ZmI0ODNmYmMxN2NhMjQwNTNmNzNmYWUyZTdmYzY3MmMxNDg0NTlkZmQ3OGRjYWI4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:04:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjJ1dlFkRCszK3ZqalV2UDN1b2RmdlE9PSIsInZhbHVlIjoiVXN5ZFE4VFpnYUg4U1ByNmhTcmVDRnlZamMrL3lyYlVuZEVPRExwd0ZDSEljZ015STZsUHB2YlhRaUNPdnM5MzR1MHJwRVFmOTNhNWNxZktUc2huWUs2UlF0Mko3TEVwaGc3TnA4eTR3SS93aklFczE4dURHa2piN0pXNi9YYTRsc3JTYmpDSFZsTlRWSXIzeVQ4eWtFcDNJMG5rU05uYVQzek5Ma0dSRTBhOS9meEtKNndXN3BOUk9PN283bHlBYUN0eWVPUFplVmxVUmpLam9tYkFnUHhIMlFPdzBjS1NCSEFxbm9WbHZ3ckM5dTBwbGtNN0RIdW4vU1NnVTg4N1pLUVc0ZU93emZ0eTRkNEhGVnEwUkxYQy9wcXByYlYweUpaaXY4NUk2UkQ1Tys5aXZ3emFnWU9nUGJUSXduVEtoZVNjM1l3RDhtalZXSkdOdC9NbTVMTmN4WGN6SHgwbkM3eU1LOEkveUlkcnBSTkNncjd4L2RjT2pBdHNUNHN0MHRuN1JBMk5qSmVhdmtGVXZiVkpOcjZSM0lzS3VGSXBNYzBoRWorL2JKVG9kWHZVVXliK2VmUW9yV3dPR1AvRXdKU21CTzdtRm9UOVJvWVdFU05xNGdNb2t2ZlVFMnBmNmRXZzFXeUZBTXllcTMvMGQweGJuZmFuMG5zU1BOU2kiLCJtYWMiOiIwZjRhOGI3MmJhMDcxNjEzYzJmMmIyZmU4MjhhYWZlNTQxYjljYzc5NmYxOTNjMGI5OTQ5YjQxMDcyMTY0NmQ5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:04:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710397100\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1630772339 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630772339\", {\"maxDepth\":0})</script>\n"}}