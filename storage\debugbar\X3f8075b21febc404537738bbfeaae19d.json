{"__meta": {"id": "X3f8075b21febc404537738bbfeaae19d", "datetime": "2025-08-02 09:16:40", "utime": **********.463787, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754126199.62316, "end": **********.463815, "duration": 0.8406550884246826, "duration_str": "841ms", "measures": [{"label": "Booting", "start": 1754126199.62316, "relative_start": 0, "end": **********.396664, "relative_end": **********.396664, "duration": 0.7735040187835693, "duration_str": "774ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.396678, "relative_start": 0.****************, "end": **********.463817, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "67.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A86A2LeLYcOdX1uuzscZG5s03wnU7PRL52pExs2I", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-947996838 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-947996838\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-947162328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-947162328\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-623085145 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-623085145\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-699485931 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-699485931\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-571372384 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-571372384\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-371331324 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:16:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjYyOTBsY0kwemNrd1Nrb05EVzVDUkE9PSIsInZhbHVlIjoidnRQMmRZUW5xdllTT3pEYlNEOURKL3FEc0Y3cUpjVVNpRDgyYUQvU1dtanEzTkZuTmNVcEFzOFVDeWtZZU5kbEJyNW1KUjFXK0xINm5TZUhpTFoyamt3aEgwNElGcUE4WStJQ0M3ZFRQVG1LMmc5TFBKQkZ1bmFPQ1RIZnl0dE42VG5SWlZUV082aDBsREpBeXR1eE91VW4wTjBBV1dkamZCc0RkQnNsSUFzQWRsdCtPdWtQNUl0ODFwb1p2Zm45VEtVTmRIUkVEMHdadWxwdURoNCt0TnQ3b1NXdmtRenBRSHpHZmE3SGFjeDBCcHo4MlJ1WEVDblE1TzFYZnBxWlU5NmVEOVQyUFJmQVZPK1JIcER4UFpCUlY5dWhXeHVXK0E4RytsY0RlcGgwUlJFNzZGOXBiT002TEJzOGZPM2RtS3QrR212RDRkK3h2OVhQenZ3OG1ENzdydWxJQWR5Q1RLblZxQ3Zmdy8yNjVlYWtaZ0kyNjl3dytHV2JycWx6Nnc5U2xCWk5EUTIzTW9tanhLbXdjNFEwendpaUVNZ0QyUkp2WnZrWVUxYXd0OGJLSkpLMWRRWWprZ1c4dmFlcUtDMkJQNmxncmZOZDdsVThrMTFVQzM0cVZUQW8rRVdZYjNmeEw4NEhseW1jZHdTYTMxQytZKzRLdXRsUWU0cHIiLCJtYWMiOiI0YTQ4YzQzM2Q3NjQwMzBiNmI2NzM3ZTQxYmVlZDk4NmQyZjllZWNiMjU0YzdmZmEyNTEzNDQzYTY1NDU4NzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:16:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImRzUWtDUkk1eTZRMnh3L2hqZklGWlE9PSIsInZhbHVlIjoiVWFOZytTU1lSQ2w4SFlidmxKRW5yc3FURy9lZnQrNlRPeExTMmFXSFN5N05kaGpCVzhYV1M1emVabEVhdjlmU0l4LzN1b0JvRU0yRk5uc3NxMzcxVnRPVU9qQUJFdDJjeHNIVXYwM01kL2I3dGV2b2RLRHNxKzE2bEs5akhvMGViM3hXWFd6cWtFbGVndjFHU2s4c3E4YjRNTkhFWFNhU0hPaStmS21ibmdaTUwyMEd3ZVNMaFFrVXNUbFUxbmV1dHFMa1IwK3FDcUNrd09xT0c0NjJJd2FFeFh1K0RsRXdqcFRqK2wyTEhqejBxb1V1dGJnait3bjV6WnlldmVzSTltd1Fxd0xITW9KM1NrVitwdXlPT1VzV3Zkc2RnNFhFdS8vZDBTNEZBcmxPanFXVy8vVUJzMU1KaGt1ODVNT2hVTFM4V3hxVVF2UXNYVWZhZnJPajQwSENWdXBkZk5mZXpzQXZEWUNmSWg2QXBmNURNWmJ3djJ1TndUWlU2bGM1SkdVYXg3ditIa1Y3Z1ExZ0ZYUHk3WE94aFdIVGhhcmNwZjN4YzR4OFJrQ081MksrSnZGK3FTNzBINnh4ZnllMndxSWdUWUZxem5tSzNKdVpSSzk2ZVhXQkIrWDdXRnptRXNHYklHakh3ZGtpZ3BhNHA3Yi9GWHlLSE96YU01ZVEiLCJtYWMiOiIwN2JiYWE1OWQwNmY4Y2IyZjQyMGJjMDA4YjM2MmVkMWExMTVjYTQ3NmRjZDliZjA0YmRhYWJlMjNjM2EzOGRlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:16:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjYyOTBsY0kwemNrd1Nrb05EVzVDUkE9PSIsInZhbHVlIjoidnRQMmRZUW5xdllTT3pEYlNEOURKL3FEc0Y3cUpjVVNpRDgyYUQvU1dtanEzTkZuTmNVcEFzOFVDeWtZZU5kbEJyNW1KUjFXK0xINm5TZUhpTFoyamt3aEgwNElGcUE4WStJQ0M3ZFRQVG1LMmc5TFBKQkZ1bmFPQ1RIZnl0dE42VG5SWlZUV082aDBsREpBeXR1eE91VW4wTjBBV1dkamZCc0RkQnNsSUFzQWRsdCtPdWtQNUl0ODFwb1p2Zm45VEtVTmRIUkVEMHdadWxwdURoNCt0TnQ3b1NXdmtRenBRSHpHZmE3SGFjeDBCcHo4MlJ1WEVDblE1TzFYZnBxWlU5NmVEOVQyUFJmQVZPK1JIcER4UFpCUlY5dWhXeHVXK0E4RytsY0RlcGgwUlJFNzZGOXBiT002TEJzOGZPM2RtS3QrR212RDRkK3h2OVhQenZ3OG1ENzdydWxJQWR5Q1RLblZxQ3Zmdy8yNjVlYWtaZ0kyNjl3dytHV2JycWx6Nnc5U2xCWk5EUTIzTW9tanhLbXdjNFEwendpaUVNZ0QyUkp2WnZrWVUxYXd0OGJLSkpLMWRRWWprZ1c4dmFlcUtDMkJQNmxncmZOZDdsVThrMTFVQzM0cVZUQW8rRVdZYjNmeEw4NEhseW1jZHdTYTMxQytZKzRLdXRsUWU0cHIiLCJtYWMiOiI0YTQ4YzQzM2Q3NjQwMzBiNmI2NzM3ZTQxYmVlZDk4NmQyZjllZWNiMjU0YzdmZmEyNTEzNDQzYTY1NDU4NzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:16:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImRzUWtDUkk1eTZRMnh3L2hqZklGWlE9PSIsInZhbHVlIjoiVWFOZytTU1lSQ2w4SFlidmxKRW5yc3FURy9lZnQrNlRPeExTMmFXSFN5N05kaGpCVzhYV1M1emVabEVhdjlmU0l4LzN1b0JvRU0yRk5uc3NxMzcxVnRPVU9qQUJFdDJjeHNIVXYwM01kL2I3dGV2b2RLRHNxKzE2bEs5akhvMGViM3hXWFd6cWtFbGVndjFHU2s4c3E4YjRNTkhFWFNhU0hPaStmS21ibmdaTUwyMEd3ZVNMaFFrVXNUbFUxbmV1dHFMa1IwK3FDcUNrd09xT0c0NjJJd2FFeFh1K0RsRXdqcFRqK2wyTEhqejBxb1V1dGJnait3bjV6WnlldmVzSTltd1Fxd0xITW9KM1NrVitwdXlPT1VzV3Zkc2RnNFhFdS8vZDBTNEZBcmxPanFXVy8vVUJzMU1KaGt1ODVNT2hVTFM4V3hxVVF2UXNYVWZhZnJPajQwSENWdXBkZk5mZXpzQXZEWUNmSWg2QXBmNURNWmJ3djJ1TndUWlU2bGM1SkdVYXg3ditIa1Y3Z1ExZ0ZYUHk3WE94aFdIVGhhcmNwZjN4YzR4OFJrQ081MksrSnZGK3FTNzBINnh4ZnllMndxSWdUWUZxem5tSzNKdVpSSzk2ZVhXQkIrWDdXRnptRXNHYklHakh3ZGtpZ3BhNHA3Yi9GWHlLSE96YU01ZVEiLCJtYWMiOiIwN2JiYWE1OWQwNmY4Y2IyZjQyMGJjMDA4YjM2MmVkMWExMTVjYTQ3NmRjZDliZjA0YmRhYWJlMjNjM2EzOGRlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:16:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371331324\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-768697686 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A86A2LeLYcOdX1uuzscZG5s03wnU7PRL52pExs2I</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768697686\", {\"maxDepth\":0})</script>\n"}}