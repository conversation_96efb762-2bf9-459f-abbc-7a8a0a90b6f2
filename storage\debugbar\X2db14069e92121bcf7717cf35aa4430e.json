{"__meta": {"id": "X2db14069e92121bcf7717cf35aa4430e", "datetime": "2025-08-02 10:06:41", "utime": **********.290473, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754129200.176491, "end": **********.290522, "duration": 1.1140310764312744, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1754129200.176491, "relative_start": 0, "end": **********.174819, "relative_end": **********.174819, "duration": 0.9983279705047607, "duration_str": "998ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.174862, "relative_start": 0.***************, "end": **********.290527, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3036\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1883 to 1889\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1883\" onclick=\"\">routes/web.php:1883-1889</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Igc23luW32NgmMNeVyJtcX9NC6cFMYciztal9rLU", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-214941949 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-214941949\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1778179216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1778179216\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-376093089 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-376093089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-375732706 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375732706\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-53043792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-53043792\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1481318675 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:06:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxnUGdOUXZqUTlXTDRBYnN2blg3UEE9PSIsInZhbHVlIjoiWGJ4czM1UFc1aGo5bDF5cWkxT016Q2p6RlBMMXJGZFBRYWQrOW54NmlXZkJGeVc3UXR2RXQ0MkwrZkx1UTNWTWpXQTRqYnYxV1ZCZzBOZHpQK3p1QllzUlV2UW9DUHhzdE01M05na1FET1Z6ZFVVaTdJNk9TU01ET0l2bjhYM25vQTRRK0ZxYStDTVcra3JxdVVVNnpLQ2ZvWEFodU1DczlpeTlzcXAzYmpXNUV5RGsydXEzTHBCN3NjUHV1MWNXOEhDdGh4cXNjd0JnQnU0OUxPOFlCT0VKc3lGUTBSNmhGU2pGRmp4MllUb1B2WUZSSmx4SDJycnk4ZVBSaFJnM0ZITDc4UmRuUmpyU05pa2dYeUQ3dmwyOWl5WnlNRnQ3NE9PY2Y4a3dlZXZTSjJyenVZUmZRejNvd0lCdHYxZUtvVFdmZXd1T3ZndFVjb3A5d2hJM3VJWFNGbGYyQkRQTGhIMmUyVHFsbjdabGl6aUJTaTFVWnVndUVpcEp4WmY1cXdlRjRBcnM1eENxWktjMld0Nk81MzBvOVF5dDlEWnJuQlg4dnA3Y0ZNdVRJYmNNc3pLL3YrQ1J0Mnpya1E0YVVOWEJnSlljZVRjQ3Jvb1lrL3pFU3U1RmFBdjFCcUlSWVZVUWRObVYzWDhKVlAzUHhudW1lWFFVaXJ5aWVtYWciLCJtYWMiOiI0ODgyYjdhODVjNDViOTQ5ZTI4ZmI1NGUxYzNkMWY5YjNkZDI5Mjg1MDlmNDkxOTc1MzAzMzI3ODAyYmM5ZTM2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:06:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InJuc01jVDBJZjJLK2U2MTZ2ckk0Z0E9PSIsInZhbHVlIjoicnFvVkQ5S3orYWVKZkFJaEFxclgrZSs3UWd0aTdwS3YyZUZ1ZDI3dDBPOGZ3RWZnOCtQNzZFZWFSbGNHQUoxR1RydDEwT3hOT0hhVTBXYnMyazkxUWk3T2YvL1BZblNsb3RpcmpnWmxmdERpSVVPQ0VGY3NGcFZmV1MrdGh0ZHphYWdqNnFEd09OdXN4SExpUmNNU1N3Z1crSHBtQ3VLTE92cng0bjI2SXRwendrZEVtVzkwcUlRblY0dUJUaW1DRHR0Q0M3d1hZYWxqZUE2SHc1c0d3WlVhYjRiaWlSZi9ybGd6NERHZTZoTjlITG9yVWQyWmJNdU1ma2t0UDNLMXlKZzBtUnlNaGxRRVNkMXExcFE4Z2ZUR3V1d2R1OU9nS25lSXpiYzRPdU9LMnAzeVRTWUVaOFVyZko0dExHN2JWdkwwTTJWNXFWMC9JRjdmRk9VQUNVSmE4WlRWUGxFNXBaVXl3SmpZOW15a2Fsc2ErMVk5WDlNeFdNRndpaUdFbmEyYjdlZjJ4OHJENDBjVFVuSlN2M2puZnVDMUhPbHpoY0RIWSs2REhBcnNBeEoyampUbFBldVRteE9zb3gzVGs1WGtlVitCQnU4Yy9iZmsvK2J5Nyt5NzBjek9JZm9nRGhqUVB3U1pUdGQ3SitYTGdEZlZDVmNJWkJxM2xNUHciLCJtYWMiOiJhMWZhNDY1MGVkMjliNDE1YzhmZTNlYzdlNTI5NTM3Y2ZmM2ZhOTlkN2ZmZTk5MmYxY2U1NTYwNmY4OGJjNTI1IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:06:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxnUGdOUXZqUTlXTDRBYnN2blg3UEE9PSIsInZhbHVlIjoiWGJ4czM1UFc1aGo5bDF5cWkxT016Q2p6RlBMMXJGZFBRYWQrOW54NmlXZkJGeVc3UXR2RXQ0MkwrZkx1UTNWTWpXQTRqYnYxV1ZCZzBOZHpQK3p1QllzUlV2UW9DUHhzdE01M05na1FET1Z6ZFVVaTdJNk9TU01ET0l2bjhYM25vQTRRK0ZxYStDTVcra3JxdVVVNnpLQ2ZvWEFodU1DczlpeTlzcXAzYmpXNUV5RGsydXEzTHBCN3NjUHV1MWNXOEhDdGh4cXNjd0JnQnU0OUxPOFlCT0VKc3lGUTBSNmhGU2pGRmp4MllUb1B2WUZSSmx4SDJycnk4ZVBSaFJnM0ZITDc4UmRuUmpyU05pa2dYeUQ3dmwyOWl5WnlNRnQ3NE9PY2Y4a3dlZXZTSjJyenVZUmZRejNvd0lCdHYxZUtvVFdmZXd1T3ZndFVjb3A5d2hJM3VJWFNGbGYyQkRQTGhIMmUyVHFsbjdabGl6aUJTaTFVWnVndUVpcEp4WmY1cXdlRjRBcnM1eENxWktjMld0Nk81MzBvOVF5dDlEWnJuQlg4dnA3Y0ZNdVRJYmNNc3pLL3YrQ1J0Mnpya1E0YVVOWEJnSlljZVRjQ3Jvb1lrL3pFU3U1RmFBdjFCcUlSWVZVUWRObVYzWDhKVlAzUHhudW1lWFFVaXJ5aWVtYWciLCJtYWMiOiI0ODgyYjdhODVjNDViOTQ5ZTI4ZmI1NGUxYzNkMWY5YjNkZDI5Mjg1MDlmNDkxOTc1MzAzMzI3ODAyYmM5ZTM2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:06:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InJuc01jVDBJZjJLK2U2MTZ2ckk0Z0E9PSIsInZhbHVlIjoicnFvVkQ5S3orYWVKZkFJaEFxclgrZSs3UWd0aTdwS3YyZUZ1ZDI3dDBPOGZ3RWZnOCtQNzZFZWFSbGNHQUoxR1RydDEwT3hOT0hhVTBXYnMyazkxUWk3T2YvL1BZblNsb3RpcmpnWmxmdERpSVVPQ0VGY3NGcFZmV1MrdGh0ZHphYWdqNnFEd09OdXN4SExpUmNNU1N3Z1crSHBtQ3VLTE92cng0bjI2SXRwendrZEVtVzkwcUlRblY0dUJUaW1DRHR0Q0M3d1hZYWxqZUE2SHc1c0d3WlVhYjRiaWlSZi9ybGd6NERHZTZoTjlITG9yVWQyWmJNdU1ma2t0UDNLMXlKZzBtUnlNaGxRRVNkMXExcFE4Z2ZUR3V1d2R1OU9nS25lSXpiYzRPdU9LMnAzeVRTWUVaOFVyZko0dExHN2JWdkwwTTJWNXFWMC9JRjdmRk9VQUNVSmE4WlRWUGxFNXBaVXl3SmpZOW15a2Fsc2ErMVk5WDlNeFdNRndpaUdFbmEyYjdlZjJ4OHJENDBjVFVuSlN2M2puZnVDMUhPbHpoY0RIWSs2REhBcnNBeEoyampUbFBldVRteE9zb3gzVGs1WGtlVitCQnU4Yy9iZmsvK2J5Nyt5NzBjek9JZm9nRGhqUVB3U1pUdGQ3SitYTGdEZlZDVmNJWkJxM2xNUHciLCJtYWMiOiJhMWZhNDY1MGVkMjliNDE1YzhmZTNlYzdlNTI5NTM3Y2ZmM2ZhOTlkN2ZmZTk5MmYxY2U1NTYwNmY4OGJjNTI1IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:06:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481318675\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1320778768 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Igc23luW32NgmMNeVyJtcX9NC6cFMYciztal9rLU</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320778768\", {\"maxDepth\":0})</script>\n"}}