{"__meta": {"id": "Xee24364eae6e004ae183d096e0088f0c", "datetime": "2025-08-02 09:56:25", "utime": **********.217582, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754128584.241564, "end": **********.217617, "duration": 0.97605299949646, "duration_str": "976ms", "measures": [{"label": "Booting", "start": 1754128584.241564, "relative_start": 0, "end": **********.150759, "relative_end": **********.150759, "duration": 0.9091949462890625, "duration_str": "909ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.150796, "relative_start": 0.****************, "end": **********.21762, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "66.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xPRsyoUJlzIx0mF8VYdEfeQNPKKLPiVfCZ3amu0G", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-576111194 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-576111194\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-584603791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-584603791\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-580835089 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-580835089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-518169410 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518169410\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1383806601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1383806601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-549936141 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:56:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik44eDhacS9EK0szbWRwSGF0dXB3bHc9PSIsInZhbHVlIjoiRWx4dloyMTFYMmhQamd4cGU1RVRiQlNSTC90OXY2U0ZVK01odzloVWlSa1VGbGpYZFYyRnlEMDZJUHlrWHBZeXlaMm4xcEJKTXRDNnp6eTRPUDVTZllMTExTNGFBSlEwNnFpakRMTHd0TTNYdkkraFFoZzRWUFFpbm9kbFlkWXB0aksvaFJwNWJpZjRIbFJtZ2VFazlTSGpOTjB0T2xtbVpqQkh4UWUwQ1ExOGV0ajJZMVkvUmp2NXNlVTI4SG8vSk03U3cxbG8zK0VBMmhXYVl4ZWQ1TDRjVVozQ1pHL0szeWVFK0NoRUVSVFBxM3hURHRyUWJ0TTI0aWdvekdCK1ViZVhINzRKOFcvUmhXeXE2WmVUMU42VjFCS3BoMlVicTBLK0Q5K0ovazR5RkRlZFhpWlhUMlk2UnhBZ1gyVjFzaXplZWlHZ2o2Y1RjazV3enA3RWVHdHVvUTZXYnJPZ2xmZkxsS2lMSi9QTjVKT0hvSUJSV2JzZWpVdkR6VXo4M0orVzJBcVJQWW5rQ0x0bW5GZlRYZUMwNThEeS9EcEZad2trYTNHaDRVWVYzMFc4V2pKMW91YWJ0VUpXT1VsSXppTEdvWkMycy9tSGpSNWtSZUIyZ0hSaU9YTGxJTmhiZjgrTzNpRi9GS3ZNSmEyWjNKeExSNDZ2bVVUMmxaT1oiLCJtYWMiOiJhOWFlZDY3ZjlhZmI1MDdmY2Y5ZjMxNTAyMjViZGJiNjU3MmEyMjg3NjExM2M0NmI4MWJiY2Q2ZjhlN2MwMzMyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:56:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkFxVVh2c2Zuc1hTVmxqb3kzMklvQkE9PSIsInZhbHVlIjoiM2JTK1dBTWx5RlZVVHhzZ1RXNG1JN3UvRkxiSkxBR3ZFOGxsdFo5SDY2L0UvUHJVQW94TTlKWDBsKzhrdHMyRGZKWGxzV2l4aEwvdENqUmZLL1NqUzJXTFlQQ1Vkcy82SFRmY3habW9tZ2VGeWp0S3pPVU9VbEVlbUJrdXFydzZGdmRIakMvWjVBRnFuRDJINi9EcXhGTzdDbHI4Y2Jxbjd1MDdFVzc4Z1gwZS9oRG1PeHF2L2JyOGR6eHl6ZGJId0hiUzVZUk5qTkMvTG1ibGJlSDJHRjAzUWxTQitZSkk4K3lnWGJ1eWVMU2ppK2I3bGVHak9oMXVIOTd6VldLS3pCU1R4Zzk5VVZpOHFidDNPaDY1dFgxQjJCaFIremdIbm85NTF0OTRZeDZzcG91UGgwYUFrQklSams5eUkvSzlPN29pSERhaVpJZnRWWEtvcXdIaURzdFJLa09WNmF2QURyYTE0QU9sMHBWa0pDZWkvcFpZRXNNT3lpVjRPQWx6L3VzSTk3ZEpnQmhhbkRaVkpiaGovUXFqNmxVR0NTKy9jS2ZPVjhvSlY0dzV2SVNHUTRHTE9pekhLS1dOWnVYRU5MaVdMU0ZKeThNTVNLMmZBODdBOEtKOHFyLzBVZFV2eUZJTFRxZUJXb0F5T1ZZQ0RSVW9WRnZOd3BkTGwvVFEiLCJtYWMiOiIwMDk1ZjRlYzRjYTg0NTllNjBiZDc1NGVhMDdmYWYzYzk5YjJlMmU5ZDE1NDBlZTdhNzdmMDhhNWUyMzdjNTY4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:56:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik44eDhacS9EK0szbWRwSGF0dXB3bHc9PSIsInZhbHVlIjoiRWx4dloyMTFYMmhQamd4cGU1RVRiQlNSTC90OXY2U0ZVK01odzloVWlSa1VGbGpYZFYyRnlEMDZJUHlrWHBZeXlaMm4xcEJKTXRDNnp6eTRPUDVTZllMTExTNGFBSlEwNnFpakRMTHd0TTNYdkkraFFoZzRWUFFpbm9kbFlkWXB0aksvaFJwNWJpZjRIbFJtZ2VFazlTSGpOTjB0T2xtbVpqQkh4UWUwQ1ExOGV0ajJZMVkvUmp2NXNlVTI4SG8vSk03U3cxbG8zK0VBMmhXYVl4ZWQ1TDRjVVozQ1pHL0szeWVFK0NoRUVSVFBxM3hURHRyUWJ0TTI0aWdvekdCK1ViZVhINzRKOFcvUmhXeXE2WmVUMU42VjFCS3BoMlVicTBLK0Q5K0ovazR5RkRlZFhpWlhUMlk2UnhBZ1gyVjFzaXplZWlHZ2o2Y1RjazV3enA3RWVHdHVvUTZXYnJPZ2xmZkxsS2lMSi9QTjVKT0hvSUJSV2JzZWpVdkR6VXo4M0orVzJBcVJQWW5rQ0x0bW5GZlRYZUMwNThEeS9EcEZad2trYTNHaDRVWVYzMFc4V2pKMW91YWJ0VUpXT1VsSXppTEdvWkMycy9tSGpSNWtSZUIyZ0hSaU9YTGxJTmhiZjgrTzNpRi9GS3ZNSmEyWjNKeExSNDZ2bVVUMmxaT1oiLCJtYWMiOiJhOWFlZDY3ZjlhZmI1MDdmY2Y5ZjMxNTAyMjViZGJiNjU3MmEyMjg3NjExM2M0NmI4MWJiY2Q2ZjhlN2MwMzMyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:56:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkFxVVh2c2Zuc1hTVmxqb3kzMklvQkE9PSIsInZhbHVlIjoiM2JTK1dBTWx5RlZVVHhzZ1RXNG1JN3UvRkxiSkxBR3ZFOGxsdFo5SDY2L0UvUHJVQW94TTlKWDBsKzhrdHMyRGZKWGxzV2l4aEwvdENqUmZLL1NqUzJXTFlQQ1Vkcy82SFRmY3habW9tZ2VGeWp0S3pPVU9VbEVlbUJrdXFydzZGdmRIakMvWjVBRnFuRDJINi9EcXhGTzdDbHI4Y2Jxbjd1MDdFVzc4Z1gwZS9oRG1PeHF2L2JyOGR6eHl6ZGJId0hiUzVZUk5qTkMvTG1ibGJlSDJHRjAzUWxTQitZSkk4K3lnWGJ1eWVMU2ppK2I3bGVHak9oMXVIOTd6VldLS3pCU1R4Zzk5VVZpOHFidDNPaDY1dFgxQjJCaFIremdIbm85NTF0OTRZeDZzcG91UGgwYUFrQklSams5eUkvSzlPN29pSERhaVpJZnRWWEtvcXdIaURzdFJLa09WNmF2QURyYTE0QU9sMHBWa0pDZWkvcFpZRXNNT3lpVjRPQWx6L3VzSTk3ZEpnQmhhbkRaVkpiaGovUXFqNmxVR0NTKy9jS2ZPVjhvSlY0dzV2SVNHUTRHTE9pekhLS1dOWnVYRU5MaVdMU0ZKeThNTVNLMmZBODdBOEtKOHFyLzBVZFV2eUZJTFRxZUJXb0F5T1ZZQ0RSVW9WRnZOd3BkTGwvVFEiLCJtYWMiOiIwMDk1ZjRlYzRjYTg0NTllNjBiZDc1NGVhMDdmYWYzYzk5YjJlMmU5ZDE1NDBlZTdhNzdmMDhhNWUyMzdjNTY4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:56:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549936141\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1955446565 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xPRsyoUJlzIx0mF8VYdEfeQNPKKLPiVfCZ3amu0G</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955446565\", {\"maxDepth\":0})</script>\n"}}