{"__meta": {"id": "X2f9d91f7967afb95af3a98fd4768f76d", "datetime": "2025-08-02 10:11:34", "utime": **********.893375, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.007211, "end": **********.893432, "duration": 0.8862209320068359, "duration_str": "886ms", "measures": [{"label": "Booting", "start": **********.007211, "relative_start": 0, "end": **********.774438, "relative_end": **********.774438, "duration": 0.7672269344329834, "duration_str": "767ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.774456, "relative_start": 0.****************, "end": **********.893437, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UB7ZFTt1EnIO1rnbsGwAhGN5xDpqMbx7DH16fBOs", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-85575684 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-85575684\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1527620699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1527620699\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111200320 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111200320\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-289281722 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289281722\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1462289522 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1462289522\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1676561610 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:11:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkY2NDZUa3A5TXZTNDY1RmNpdGkwOGc9PSIsInZhbHVlIjoiQndRRzBxQk1IcS9tbEFFL1VtS2hXdnFJRU1QbzBxMlVVeCtDSU4wY3hSemRmb3FwdWJlNDN4SE5zajhnTSt4aUtwSFY1eEU1S1NXNmRVcjJIYWhkcm9HL2tCVUN2SGUwQUxkd1YvTHJpRnNleFBycUtnOXlWeXlvR05HaC9JczBCcldHb2V5ZXhzK2RCMUF2bGx1b1o3TXI4UFpJVnM0T2VKVnN2YW1pR2JEZlVsS1dnRGJYVWhEQmhod2Y1NThxTnRhTXJjTkxhZVRXLyt6eHNwejE5L0ZGSGE0U2pERFhUbUErbHlMRlMwK1JSNWthRUVyY1VEK01NalA0L3M3alpaSkpDc2JqUWF4eHdnVys1MUlsTFNicHYrZ1Z6WGJ0U09PaEM2SGVOcGZRaHpyUGY0Rm1UQkgvM3hmYm5TQSsyTTRMVXVqVmdTcWg2TW5tWml4VmJBdnllTTZaMExpRCs5ejg4bG5OcFFDQk9XNmZpNlpHVHhPV2M4L3VnN1NpZ3lWeUN5MXBoNlhpWng5Yy9rN1QwN0NhUXRhNFhXaHY0R0svK0w3ZVRnMFcyaEZKUWRhVEJ5M0RsYVd3c2xzd2hWQWU3WU84eHlFbnZDTzMxcnYyWXAyNk5RU2RNOHBzZkRPMUo1R3JwRmVacDkyb3l3RUFSdkw5V3BZMjBOMGgiLCJtYWMiOiI2ZDUzNGUzZWE0YjI4YTY3MGEyODJlNGViMTZjYThmZmQxNDA1YTg2Y2Y0NzlkYjhlZTQ5ZDdjYTg3NWEzNjkzIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:11:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im9wSEF3SjZFOXpPWVduLy9Cd1VCclE9PSIsInZhbHVlIjoiRm1tUmdUMUxkN1duSGtUZ0dMbE5CZHhFUEgyRlJhZWJMR25KTXVnakQ3cmZTWUEwWHY4RHU1QThMVlN2SnRlUk9Xc3RqL3lxdWQ0M0szeVBhSGQ5cG9DNzBoU3RTcEhxWWRrS1NYM2J5OXVHbUVZR2VhNGxTZ1BpYlVUa3h1SUZCaVUxTFBwSldyTllDenpWVHNPMHFpQ2dYNXRSeTAzblpKd0JjdFNlV3JnZHhIVjIxbS9KbWJDajhOTlFScU1lVDlHdE91SC9KMjVyUWowbUpvSThFaVkwekl4SWJGTGVack1kNExLdUwzSEZVNjBRQlJ1ZFZvQjFrM3kwRVQ1RFN3dFpwU0MwbU11cTZkMlV2Ukd2c3F3bWp2VXBIelMvL1Izblp2ekk3eWdxQU9zTTZob1FSLzljakMzOEEzL0tHcWJUZDBNblloeVBEbFBWOVNjUlNrb3luSnNGK1RHKzFuQW4xSzBtVWowYWh2clRsWWRlN1h6b3Jwc2FpTThGUFFIc295ajhsbzdPQklEZjVPTVhFS0pzYk5BckVsdXgvbi9CMkV0UVh0UC9mOXpVc25tTm52WWJuMEd4bXBUV21RUmlpQm5SanZyZkh5VmdrV0dNeWZVTjIzS0FUYjdDbHlhOERyOW96MW04UmtoWWh1UlRLeXhzemFHVEkzdnkiLCJtYWMiOiI3MmJjZDZiMTBmYjQ3M2U1NmY1MTI2NDQ0OWY3ODYxYzZmZTMyZmQyZGM2MDVkMTBmNTg3MWIyOTQ1MzRmNjM0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:11:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkY2NDZUa3A5TXZTNDY1RmNpdGkwOGc9PSIsInZhbHVlIjoiQndRRzBxQk1IcS9tbEFFL1VtS2hXdnFJRU1QbzBxMlVVeCtDSU4wY3hSemRmb3FwdWJlNDN4SE5zajhnTSt4aUtwSFY1eEU1S1NXNmRVcjJIYWhkcm9HL2tCVUN2SGUwQUxkd1YvTHJpRnNleFBycUtnOXlWeXlvR05HaC9JczBCcldHb2V5ZXhzK2RCMUF2bGx1b1o3TXI4UFpJVnM0T2VKVnN2YW1pR2JEZlVsS1dnRGJYVWhEQmhod2Y1NThxTnRhTXJjTkxhZVRXLyt6eHNwejE5L0ZGSGE0U2pERFhUbUErbHlMRlMwK1JSNWthRUVyY1VEK01NalA0L3M3alpaSkpDc2JqUWF4eHdnVys1MUlsTFNicHYrZ1Z6WGJ0U09PaEM2SGVOcGZRaHpyUGY0Rm1UQkgvM3hmYm5TQSsyTTRMVXVqVmdTcWg2TW5tWml4VmJBdnllTTZaMExpRCs5ejg4bG5OcFFDQk9XNmZpNlpHVHhPV2M4L3VnN1NpZ3lWeUN5MXBoNlhpWng5Yy9rN1QwN0NhUXRhNFhXaHY0R0svK0w3ZVRnMFcyaEZKUWRhVEJ5M0RsYVd3c2xzd2hWQWU3WU84eHlFbnZDTzMxcnYyWXAyNk5RU2RNOHBzZkRPMUo1R3JwRmVacDkyb3l3RUFSdkw5V3BZMjBOMGgiLCJtYWMiOiI2ZDUzNGUzZWE0YjI4YTY3MGEyODJlNGViMTZjYThmZmQxNDA1YTg2Y2Y0NzlkYjhlZTQ5ZDdjYTg3NWEzNjkzIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:11:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im9wSEF3SjZFOXpPWVduLy9Cd1VCclE9PSIsInZhbHVlIjoiRm1tUmdUMUxkN1duSGtUZ0dMbE5CZHhFUEgyRlJhZWJMR25KTXVnakQ3cmZTWUEwWHY4RHU1QThMVlN2SnRlUk9Xc3RqL3lxdWQ0M0szeVBhSGQ5cG9DNzBoU3RTcEhxWWRrS1NYM2J5OXVHbUVZR2VhNGxTZ1BpYlVUa3h1SUZCaVUxTFBwSldyTllDenpWVHNPMHFpQ2dYNXRSeTAzblpKd0JjdFNlV3JnZHhIVjIxbS9KbWJDajhOTlFScU1lVDlHdE91SC9KMjVyUWowbUpvSThFaVkwekl4SWJGTGVack1kNExLdUwzSEZVNjBRQlJ1ZFZvQjFrM3kwRVQ1RFN3dFpwU0MwbU11cTZkMlV2Ukd2c3F3bWp2VXBIelMvL1Izblp2ekk3eWdxQU9zTTZob1FSLzljakMzOEEzL0tHcWJUZDBNblloeVBEbFBWOVNjUlNrb3luSnNGK1RHKzFuQW4xSzBtVWowYWh2clRsWWRlN1h6b3Jwc2FpTThGUFFIc295ajhsbzdPQklEZjVPTVhFS0pzYk5BckVsdXgvbi9CMkV0UVh0UC9mOXpVc25tTm52WWJuMEd4bXBUV21RUmlpQm5SanZyZkh5VmdrV0dNeWZVTjIzS0FUYjdDbHlhOERyOW96MW04UmtoWWh1UlRLeXhzemFHVEkzdnkiLCJtYWMiOiI3MmJjZDZiMTBmYjQ3M2U1NmY1MTI2NDQ0OWY3ODYxYzZmZTMyZmQyZGM2MDVkMTBmNTg3MWIyOTQ1MzRmNjM0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:11:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676561610\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-775848499 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UB7ZFTt1EnIO1rnbsGwAhGN5xDpqMbx7DH16fBOs</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775848499\", {\"maxDepth\":0})</script>\n"}}