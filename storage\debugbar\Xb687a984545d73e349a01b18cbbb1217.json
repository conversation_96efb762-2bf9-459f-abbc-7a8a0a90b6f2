{"__meta": {"id": "Xb687a984545d73e349a01b18cbbb1217", "datetime": "2025-08-02 08:39:46", "utime": **********.7049, "method": "POST", "uri": "/leads", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 21, "messages": [{"message": "[08:39:43] LOG.info: Lead creation started {\n    \"user_id\": 79,\n    \"request_data\": {\n        \"_token\": \"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL\",\n        \"name\": \"Mr <PERSON><PERSON><PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"+************\",\n        \"subject\": null,\n        \"user_id\": \"81\",\n        \"date_of_birth\": \"2025-07-30\",\n        \"next_follow_up_date\": \"2025-08-23\",\n        \"type\": \"lead\",\n        \"pipeline_id\": \"23\",\n        \"stage_id\": \"87\",\n        \"status\": \"hot\",\n        \"opportunity_info\": null,\n        \"opportunity_source\": \"phone\",\n        \"lead_value\": \"8900\",\n        \"opportunity_description\": \"ok\",\n        \"labels\": [\n            \"89\",\n            \"90\",\n            \"91\",\n            \"92\",\n            \"93\",\n            \"new_Parichay AI\",\n            \"new_ROCK\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.524307, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Creating lead with data {\n    \"pipeline_id\": 23,\n    \"stage_id\": 87,\n    \"user_id\": \"81\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.1089, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Processing tags {\n    \"tagsInput\": [\n        \"89\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"new_Parichay AI\",\n        \"new_ROCK\"\n    ],\n    \"has_labels\": true,\n    \"has_tags\": false,\n    \"labels_value\": [\n        \"89\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"new_Parichay AI\",\n        \"new_ROCK\"\n    ],\n    \"tags_value\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.112843, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Using existing tag ID {\n    \"tag_id\": \"89\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.113408, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Using existing tag ID {\n    \"tag_id\": \"90\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.113952, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Using existing tag ID {\n    \"tag_id\": \"91\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.114344, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Using existing tag ID {\n    \"tag_id\": \"92\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.114782, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Using existing tag ID {\n    \"tag_id\": \"93\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.115154, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.error: Error processing tag {\n    \"tag\": \"new_Parichay AI\",\n    \"error\": \"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Parichay AI, primary, 79, 1, 2025-08-02 08:39:44, 2025-08-02 08:39:44))\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.126924, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.error: Error processing tag {\n    \"tag\": \"new_ROCK\",\n    \"error\": \"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (ROCK, primary, 79, 1, 2025-08-02 08:39:44, 2025-08-02 08:39:44))\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.13572, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Set lead tags {\n    \"tag_ids\": [\n        \"89\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\"\n    ],\n    \"tags_string\": [\n        {\n            \"id\": 89,\n            \"name\": \"Cold Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 92,\n            \"name\": \"Follow Up\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 93,\n            \"name\": \"New Customer\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 91,\n            \"name\": \"VIP\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Warm Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.155692, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Lead saved successfully {\n    \"lead_id\": 17,\n    \"tags_field\": [\n        {\n            \"id\": 89,\n            \"name\": \"Cold Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 92,\n            \"name\": \"Follow Up\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 93,\n            \"name\": \"New Customer\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 91,\n            \"name\": \"VIP\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Warm Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        }\n    ],\n    \"tags_method_count\": 5,\n    \"tags_method_data\": [\n        \"Cold Lead\",\n        \"Follow Up\",\n        \"New Customer\",\n        \"VIP\",\n        \"Warm Lead\"\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.216059, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: UserLead relationships created successfully", "message_html": null, "is_string": false, "label": "info", "time": **********.236779, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Starting webhook dispatch for action: crm.lead_created {\n    \"timestamp\": \"2025-08-02T08:39:44.379782Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 79,\n    \"entity_type\": \"Lead\",\n    \"entity_id\": 17,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.387154, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created", "message_html": null, "is_string": false, "label": "info", "time": **********.388146, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Found 1 enabled integrations for webhook action: crm.lead_created", "message_html": null, "is_string": false, "label": "info", "time": **********.407233, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:44] LOG.info: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/", "message_html": null, "is_string": false, "label": "info", "time": **********.408206, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:46] LOG.error: <PERSON>ho<PERSON> failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-08-02T08:39:46.608982Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 2200,\n    \"user_id\": 79,\n    \"entity_id\": 17,\n    \"entity_type\": \"Lead\",\n    \"request_payload\": {\n        \"action\": \"crm.lead_created\",\n        \"timestamp\": \"2025-08-02T08:39:44.424590Z\",\n        \"data\": {\n            \"id\": 17,\n            \"name\": \"<PERSON> <PERSON><PERSON><PERSON>\",\n            \"contact_type\": \"Lead\",\n            \"tags\": [\n                {\n                    \"id\": 89,\n                    \"name\": \"Cold Lead\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 92,\n                    \"name\": \"Follow Up\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 93,\n                    \"name\": \"New Customer\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 91,\n                    \"name\": \"VIP\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 90,\n                    \"name\": \"Warm Lead\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                }\n            ],\n            \"postal_code\": null,\n            \"city\": null,\n            \"state\": null,\n            \"country\": null,\n            \"business_name\": null,\n            \"business_gst\": null,\n            \"business_state\": null,\n            \"business_postal_code\": null,\n            \"business_address\": null,\n            \"dnd_settings\": null,\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"+************\",\n            \"date_of_birth\": \"2025-07-30\",\n            \"type\": \"\",\n            \"status\": \"\",\n            \"opportunity_info\": null,\n            \"opportunity_description\": \"ok\",\n            \"opportunity_source\": \"phone\",\n            \"lead_value\": \"8900.00\",\n            \"subject\": \"Lead from Mr Mukharji\",\n            \"user_id\": 81,\n            \"pipeline_id\": 23,\n            \"stage_id\": 87,\n            \"contact_group_id\": null,\n            \"sources\": [],\n            \"products\": [],\n            \"notes\": null,\n            \"labels\": [],\n            \"custom_fields\": null,\n            \"order\": 0,\n            \"created_by\": 79,\n            \"is_deleted\": 0,\n            \"is_active\": 1,\n            \"is_converted\": 0,\n            \"date\": \"2025-08-02\",\n            \"next_follow_up_date\": \"2025-08-23\",\n            \"created_at\": \"2025-08-02T08:39:44.000000Z\",\n            \"updated_at\": \"2025-08-02T08:39:44.000000Z\",\n            \"stage\": {\n                \"id\": 87,\n                \"name\": \"Qualified\",\n                \"pipeline_id\": 23,\n                \"created_by\": 79,\n                \"order\": 1,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:03.000000Z\"\n            },\n            \"pipeline\": {\n                \"id\": 23,\n                \"name\": \"OMX Digital Bot\",\n                \"created_by\": 79,\n                \"is_deleted\": 0,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:42.000000Z\"\n            },\n            \"triggered_by\": {\n                \"user_id\": 79,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"Parichay Singha AI\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 79,\n        \"triggered_by\": {\n            \"user_id\": 79,\n            \"email\": \"<EMAIL>\",\n            \"name\": \"Parichay Singha AI\",\n            \"type\": \"company\"\n        },\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.610248, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:46] LOG.info: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created", "message_html": null, "is_string": false, "label": "info", "time": **********.611289, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:46] LOG.warning: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-08-02T08:39:46.611633Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 79,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.612242, "xdebug_link": null, "collector": "log"}, {"message": "[08:39:46] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.688824, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754123981.835306, "end": **********.705113, "duration": 4.869807004928589, "duration_str": "4.87s", "measures": [{"label": "Booting", "start": 1754123981.835306, "relative_start": 0, "end": **********.339085, "relative_end": **********.339085, "duration": 1.5037791728973389, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339114, "relative_start": 1.5038080215454102, "end": **********.705119, "relative_end": 5.9604644775390625e-06, "duration": 3.3660049438476562, "duration_str": "3.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 57444568, "peak_usage_str": "55MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads", "middleware": "web, verified, auth, XSS", "as": "leads.store", "controller": "App\\Http\\Controllers\\LeadController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=256\" onclick=\"\">app/Http/Controllers/LeadController.php:256-678</a>"}, "queries": {"nb_statements": 36, "nb_failed_statements": 0, "accumulated_duration": 0.08589000000000001, "accumulated_duration_str": "85.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.470048, "duration": 0.008320000000000001, "duration_str": "8.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 9.687}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.51469, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 9.687, "width_percent": 1.898}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.541465, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 11.585, "width_percent": 1.339}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.557188, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 12.924, "width_percent": 1.409}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.56687, "duration": 0.00591, "duration_str": "5.91ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 14.332, "width_percent": 6.881}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.64553, "duration": 0.004719999999999999, "duration_str": "4.72ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 21.213, "width_percent": 5.495}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.7014012, "duration": 0.01466, "duration_str": "14.66ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 26.709, "width_percent": 17.068}, {"sql": "select * from `pipelines` where `created_by` = 79 and `id` = '23' limit 1", "type": "query", "params": [], "bindings": ["79", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0933568, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:324", "source": "app/Http/Controllers/LeadController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=324", "ajax": false, "filename": "LeadController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 43.777, "width_percent": 1.269}, {"sql": "select * from `lead_stages` where `id` = '87' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["87", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 325}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.102125, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:325", "source": "app/Http/Controllers/LeadController.php:325", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=325", "ajax": false, "filename": "LeadController.php", "line": "325"}, "connection": "radhe_same", "start_percent": 45.046, "width_percent": 1.025}, {"sql": "select * from `tags` where `name` = 'Parichay AI' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["Parichay AI", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 445}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.116843, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:445", "source": "app/Http/Controllers/LeadController.php:445", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=445", "ajax": false, "filename": "LeadController.php", "line": "445"}, "connection": "radhe_same", "start_percent": 46.071, "width_percent": 1.176}, {"sql": "select * from `tags` where `name` = 'ROCK' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["ROCK", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 445}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1278338, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:445", "source": "app/Http/Controllers/LeadController.php:445", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=445", "ajax": false, "filename": "LeadController.php", "line": "445"}, "connection": "radhe_same", "start_percent": 47.246, "width_percent": 1.269}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 479}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.136456, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Lead.php:183", "source": "app/Models/Lead.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=183", "ajax": false, "filename": "Lead.php", "line": "183"}, "connection": "radhe_same", "start_percent": 48.516, "width_percent": 1.409}, {"sql": "insert into `leads` (`name`, `email`, `phone`, `subject`, `user_id`, `pipeline_id`, `stage_id`, `created_by`, `date`, `date_of_birth`, `type`, `status`, `opportunity_info`, `opportunity_description`, `opportunity_source`, `lead_value`, `next_follow_up_date`, `contact_type`, `postal_code`, `city`, `state`, `country`, `business_name`, `business_gst`, `business_state`, `business_postal_code`, `business_address`, `dnd_settings`, `tags`, `updated_at`, `created_at`) values ('Mr <PERSON>', '<EMAIL>', '+************', 'Lead from Mr <PERSON>', '81', 23, 87, 79, '2025-08-02', '2025-07-30', 'lead', 'hot', '', 'ok', 'phone', '8900', '2025-08-23', 'Lead', '', '', '', '', '', '', '', '', '', '', '89,90,91,92,93', '2025-08-02 08:39:44', '2025-08-02 08:39:44')", "type": "query", "params": [], "bindings": ["Mr <PERSON><PERSON><PERSON>", "<EMAIL>", "+************", "Lead from Mr <PERSON><PERSON><PERSON>", "81", "23", "87", "79", "2025-08-02", "2025-07-30", "lead", "hot", "", "ok", "phone", "8900", "2025-08-23", "Lead", "", "", "", "", "", "", "", "", "", "", "89,90,91,92,93", "2025-08-02 08:39:44", "2025-08-02 08:39:44"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 490}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1568382, "duration": 0.00624, "duration_str": "6.24ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:490", "source": "app/Http/Controllers/LeadController.php:490", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=490", "ajax": false, "filename": "LeadController.php", "line": "490"}, "connection": "radhe_same", "start_percent": 49.924, "width_percent": 7.265}, {"sql": "select * from `leads` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 493}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.171092, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:493", "source": "app/Http/Controllers/LeadController.php:493", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=493", "ajax": false, "filename": "LeadController.php", "line": "493"}, "connection": "radhe_same", "start_percent": 57.189, "width_percent": 1.409}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 500}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.177839, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Lead.php:183", "source": "app/Models/Lead.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=183", "ajax": false, "filename": "Lead.php", "line": "183"}, "connection": "radhe_same", "start_percent": 58.598, "width_percent": 1.188}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 501}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.184039, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Lead.php:163", "source": "app/Models/Lead.php:163", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=163", "ajax": false, "filename": "Lead.php", "line": "163"}, "connection": "radhe_same", "start_percent": 59.786, "width_percent": 1.141}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 501}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.191558, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Lead.php:163", "source": "app/Models/Lead.php:163", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=163", "ajax": false, "filename": "Lead.php", "line": "163"}, "connection": "radhe_same", "start_percent": 60.927, "width_percent": 1.304}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 502}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1986032, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Lead.php:163", "source": "app/Models/Lead.php:163", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=163", "ajax": false, "filename": "Lead.php", "line": "163"}, "connection": "radhe_same", "start_percent": 62.231, "width_percent": 1.141}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 502}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.205734, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Lead.php:163", "source": "app/Models/Lead.php:163", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=163", "ajax": false, "filename": "Lead.php", "line": "163"}, "connection": "radhe_same", "start_percent": 63.372, "width_percent": 1.188}, {"sql": "insert into `user_leads` (`user_id`, `lead_id`, `updated_at`, `created_at`) values (79, 17, '2025-08-02 08:39:44', '2025-08-02 08:39:44')", "type": "query", "params": [], "bindings": ["79", "17", "2025-08-02 08:39:44", "2025-08-02 08:39:44"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 527}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.218862, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:527", "source": "app/Http/Controllers/LeadController.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=527", "ajax": false, "filename": "LeadController.php", "line": "527"}, "connection": "radhe_same", "start_percent": 64.559, "width_percent": 3.854}, {"sql": "insert into `user_leads` (`user_id`, `lead_id`, `updated_at`, `created_at`) values ('81', 17, '2025-08-02 08:39:44', '2025-08-02 08:39:44')", "type": "query", "params": [], "bindings": ["81", "17", "2025-08-02 08:39:44", "2025-08-02 08:39:44"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 527}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2291791, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:527", "source": "app/Http/Controllers/LeadController.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=527", "ajax": false, "filename": "LeadController.php", "line": "527"}, "connection": "radhe_same", "start_percent": 68.413, "width_percent": 3.027}, {"sql": "select * from `users` where `users`.`id` = '81' limit 1", "type": "query", "params": [], "bindings": ["81"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 557}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.237524, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:557", "source": "app/Http/Controllers/LeadController.php:557", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=557", "ajax": false, "filename": "LeadController.php", "line": "557"}, "connection": "radhe_same", "start_percent": 71.44, "width_percent": 1.246}, {"sql": "select * from `users` where `users`.`id` = '81' limit 1", "type": "query", "params": [], "bindings": ["81"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 563}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.243962, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:563", "source": "app/Http/Controllers/LeadController.php:563", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=563", "ajax": false, "filename": "LeadController.php", "line": "563"}, "connection": "radhe_same", "start_percent": 72.686, "width_percent": 1.141}, {"sql": "select * from `email_templates` where `name` LIKE 'lead_assigned' limit 1", "type": "query", "params": [], "bindings": ["lead_assigned"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2418}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 572}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2524679, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2418", "source": "app/Models/Utility.php:2418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2418", "ajax": false, "filename": "Utility.php", "line": "2418"}, "connection": "radhe_same", "start_percent": 73.827, "width_percent": 1.269}, {"sql": "select * from `user_email_templates` where `template_id` = 4 and `user_id` = 79 limit 1", "type": "query", "params": [], "bindings": ["4", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2422}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 572}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.266863, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2422", "source": "app/Models/Utility.php:2422", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2422", "ajax": false, "filename": "Utility.php", "line": "2422"}, "connection": "radhe_same", "start_percent": 75.096, "width_percent": 3.609}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 353}, {"index": 25, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 26, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 598}], "start": **********.300886, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Lead.php:183", "source": "app/Models/Lead.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=183", "ajax": false, "filename": "Lead.php", "line": "183"}, "connection": "radhe_same", "start_percent": 78.705, "width_percent": 2.026}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = 87 and `lead_stages`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["87"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 358}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 598}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.324001, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:358", "source": "app/Services/CrmWebhookDispatcher.php:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=358", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "358"}, "connection": "radhe_same", "start_percent": 80.731, "width_percent": 1.77}, {"sql": "select * from `pipelines` where `pipelines`.`id` = 23 and `pipelines`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 365}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 598}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.334733, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:365", "source": "app/Services/CrmWebhookDispatcher.php:365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=365", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "365"}, "connection": "radhe_same", "start_percent": 82.501, "width_percent": 1.514}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 107}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 598}], "start": **********.346883, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Lead.php:107", "source": "app/Models/Lead.php:107", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=107", "ajax": false, "filename": "Lead.php", "line": "107"}, "connection": "radhe_same", "start_percent": 84.014, "width_percent": 1.374}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 97}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 390}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 598}], "start": **********.359159, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Lead.php:97", "source": "app/Models/Lead.php:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=97", "ajax": false, "filename": "Lead.php", "line": "97"}, "connection": "radhe_same", "start_percent": 85.388, "width_percent": 1.292}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 598}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3702211, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": "app/Services/CrmWebhookDispatcher.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "radhe_same", "start_percent": 86.681, "width_percent": 1.956}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 63}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 598}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.391372, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:63", "source": "app/Services/ModuleWebhookService.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=63", "ajax": false, "filename": "ModuleWebhookService.php", "line": "63"}, "connection": "radhe_same", "start_percent": 88.637, "width_percent": 2.003}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 227}, {"index": 21, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 97}, {"index": 22, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 76}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}], "start": **********.4094791, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:227", "source": "app/Services/ModuleWebhookService.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=227", "ajax": false, "filename": "ModuleWebhookService.php", "line": "227"}, "connection": "radhe_same", "start_percent": 90.639, "width_percent": 2.585}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 634}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.624907, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "Lead.php:163", "source": "app/Models/Lead.php:163", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=163", "ajax": false, "filename": "Lead.php", "line": "163"}, "connection": "radhe_same", "start_percent": 93.224, "width_percent": 2.969}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 163}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 634}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.637594, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "Lead.php:163", "source": "app/Models/Lead.php:163", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=163", "ajax": false, "filename": "Lead.php", "line": "163"}, "connection": "radhe_same", "start_percent": 96.193, "width_percent": 1.909}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.671373, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 98.102, "width_percent": 1.898}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Tag": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2836, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2001122095 data-indent-pad=\"  \"><span class=sf-dump-note>create lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001122095\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025649, "xdebug_link": null}]}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads", "status_code": "<pre class=sf-dump id=sf-dump-1588647809 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1588647809\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-704313088 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-704313088\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1331388872 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Mr <PERSON>ji</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+************</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-30</span>\"\n  \"<span class=sf-dump-key>next_follow_up_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-23</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">lead</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>stage_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">87</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"3 characters\">hot</span>\"\n  \"<span class=sf-dump-key>opportunity_info</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>opportunity_source</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n  \"<span class=sf-dump-key>lead_value</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8900</span>\"\n  \"<span class=sf-dump-key>opportunity_description</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ok</span>\"\n  \"<span class=sf-dump-key>labels</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">89</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">91</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">93</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"15 characters\">new_Parichay AI</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">new_ROCK</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331388872\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-548867278 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2427</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryMBLD9u5jpBw5f9de</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IllrZFhZVDNBV3preUdzYTFsTXBPOVE9PSIsInZhbHVlIjoiUVpGam1idHpzMmR4Yy9wTER3YmNCWWgxYmNkaTVTMExJanlmRzQzc2FVRmVNWmlpakpibFE4T0Nicm5SZjlZTU43Y2I0NkVPbHIxUE9VMmRVL1ZkUGR1Q0FKbFk2RE43ZGtvdDlQOUVXU2Z2dVpqb1Rvb1pXQy8vUDE0RDJPb1YyOXZmZklaQlBYRENvTUNZeHZONXV0NFltY1lEMTdvTlZkU2dEdEFZWnpSRFU5TWlKS1hJSW96dlVmeVBVK1pWRTI0ckZsOVpaTzJSN2RuVklWTHpna1hrUXBHSzB2ZGxBMkpBVnpqa0MySUh1NGcrNmQxNkNIMWNNSHRIMHVaeUsxMmFKbnAyS0FYZDJ1MFBzYWtSb3JaeG5mR2pscTc2TWxOTE1BMTJCN0REUlB2ZTRkSFJIZklXak93cnU3UHFQOEo5a2E0UThoUkdIb0Y0eklaaHZkZkJad2czNWJhNDFicnVtZ2Q0V2k4OEh6aGkxU3prdlF4TVE2SjR6MWx1dDFXd2hSNWxDT2Zva1BTeW55WGZGUmc5MzBKbHpZZEV0SEZ6TE1oVTZ0R0hvZlZzYnUydnE4bUNxd2pRSDlOSjlsZkcxbWtpZWZtbmNtenpoMW9YWWtYaUpHSklJRnRUaHltNkJMYkFuQ2xhSkxxSEU2eUtYaFRCUXhJNldQT28iLCJtYWMiOiJiMzVmMDAwNGUzYzc0ZWE3OTdjNGI1NDI5OWE4OTlhMWUyYjU0OWQ3OTA0ODU5ZDM2YTFmYzM5YmVmMWUzMzkyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ii9uVE1LdHhoVyttU3V2UFBLb2hMdmc9PSIsInZhbHVlIjoiOHUxdllOQTVKQnROTFZoSCs2QjM0UElwQVFUaWhSSE5MWXIvcFZ6eWYwUDk0NjZxUXlxSTJ4Y2RUZSt6R3plMFhiNWVWaGtyWTd6MWY4dXJ1SS9iM2tnWnFtUys2VEk5b1VUNEd5WjU5ZXdGNXpOdDZYSGpodHZPcnREUWhOWHB1ZFJjNm1DbUd3N1RFeFRTOElTa2paclJRb2tEaGVVWjVEUDYyaVNJRUEwK3cxLzVmZlFSTUIzT2VnVEQ5RWV2ZGtOS2wvM0diMXZOU05mc1JwamVDbHMrQS8zMi9wV2FwZysvVnZkTWlUTkVLYlBLZXg3Tm1oK0VHT1RLMDNrcXU4N1I1bFRFd3VmWXFMVWVOdWJST2h1N3pkN1IyM0FUcmY0MWRlbzY5V2NxeEU0bDBYbjQ2NlBJazJ0c1RpUUorNzZuRCs4b21NaWtyWWVSZThBMHBDWWROSjAzZjJ3U0JhbW9vUktud1ZJMnFSVTVlUVlRck5ILzJ5R2xxdUl3NXdGRkhWOTMrUkhMNHEzaFhBWm5FS3lGc3puZUdRMkt1ZjlsZUt3VUdPaHdUTUpTSDFwbk04WmxobmFYNmJIUlBMRUtMNVVTK1UvZU52WnluMU5lWm5WNFZ2N25RTWVZVlhMTXN5b3ViSEdHOWNtZ01KOGphZmJpcmc3OWQ4cFIiLCJtYWMiOiIxNGJhZmU0M2NiYjM5NmE0MTAzOTcyMjNlZTQ0OTAwNTI1NjQ5NWU3YmRhZDdlMDk2YzY0NGNiNzM3MTIyOTdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548867278\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1793996676 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793996676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1672616564 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:39:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNRWVdGdGpuT1o4QWx4TzQvRjhSTnc9PSIsInZhbHVlIjoiRk1ScHdWNjl4QkVmYUxVVGMwSUZmMElFay91QWRpR1B5SUZtRmFxb2VIK245NTduRmlKRTN0K3NRNTNCS0lwMHNvejZncmJBckkxUVZOWVkzZUhxTGxNTWlRMVhoTkNwNTJTd2lmNklNOCtYeHFjaWZEREFUVG9ZTUtaelBkcVFrNlhRZG1qQzZMWmVMVVVFYzhoUnUwVFh6aGdlU2xiR2dSbU9PN2dZVHZQTGpkVjVxOXBSZzFBNFJJUWRZcDFGeW8rbmpseTIyVkRBc2ZQVzNBZnVhYjdWWFYycDBSc3JrZys3V083bTBObnVjOHUrMTlGZDhLR1F1RzdrbmQ5WTgvTmxtbnFBaDBnY2NNSkZSZ1NVWXJWMkNVRk90MDhJKzRmVlYwdXorTmNMK1FyU1lFZUFkMGNLdjlpQ3Vvejk4eTM2Mzc0N0dtTjJJblFvYmtocTA0eWNEMGdxTDR4MHdxY0pRVjFKQ3RFUkpGcVlwS1pMS2ZSVCtuME50QkVKZUpoVDhTMnVqbDlheHVJY1UrWG15VFpHeEdLNUNiUkhad0V2VVp1VmFZV3c4bzFpUHY3WDFsNGZjdy91SWJ5ZkxiMjl4cE9Xa0x6L3ViYkd3S2pCN2FmeHVRZ1pYbUpPRElXNWJxYXMrdWxBQmRsa3dWTGdCMXVOdWtzanorL0oiLCJtYWMiOiJjYjdkYjQxYzQyNzRiMTM3YmNhNDZiOWY4YjcxNGNhYzUxZTkwN2IyYTNiMjgyYzEzODA4MTdjMGYxOTBiOWQ4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:39:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjM2OE5hVWtKTC9aM1FncXQ5UFdJVnc9PSIsInZhbHVlIjoiWUpHMWptK09jbFA4QkYxcStMcldqMUFadjVYTnBzV2dTazN3cEVBazU3QnVraU1ZK3l2aGxRaHp4bzExSmJxck5Zc1hET2YwUFo3eFhCRytZclZMV1dVaDVQZWovWXJxWnlQQmttTCs4YlRldm4vOUw4Z0wrbGE5L01BNHhodTZ0TmZQMmd4ak9DTUhUSlJNQlJEbkFCT2V5SktURy9sb3VIMEZIWkRLZ2FIUFZiUzJJTVh4VWFiK0dZK1BscE9XYnAweUhEYTYvNGJDWGErd0dvVWlNMHZrL3RFaUxXVmhlZlVac0lPK2UxZXg4V0V6K05KOXZGTUw1VWdwbzBtTmlPN1NUd1p6WmUyTHBIak5RUjNxRTZVZ1BwZlNwbTdJTXRtME9aSC8yT0ovdjRGV2xWWkRKU2l0RzZ5SE9rN0RlckpsVjhTdGo5UW9zNWE2VXBCYmJwb3lYSlZjVmYwTlpjZHRPT2xrSjhHMnhQY0lZeXBoYlVmK3l1YUNhbnA3b0ZadzJ4S0dnbStRdC9pT2pZb0ZVbDZNdlBqMktZM1lOSFFkT0N5eGluUnhKOFRVVXpvV2hLRXlIQ1B5a1VKdEplUEx1WUJ1aHdGVEdnYzFXUHBHT1llV0p0VE5OTWhZK0VWM2w1LzJ3b0I4R2JZamJhT2FRZ0FwalR4bkxTdE4iLCJtYWMiOiJlMjUwNGNmNzEzNjU4MTYzMDdmMjI4MjMwNWViZWQ0ZWVkYWMxYjI1OWRmOTg5ZjFmMmI5NjUzYWU1YzQ5MTlmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:39:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNRWVdGdGpuT1o4QWx4TzQvRjhSTnc9PSIsInZhbHVlIjoiRk1ScHdWNjl4QkVmYUxVVGMwSUZmMElFay91QWRpR1B5SUZtRmFxb2VIK245NTduRmlKRTN0K3NRNTNCS0lwMHNvejZncmJBckkxUVZOWVkzZUhxTGxNTWlRMVhoTkNwNTJTd2lmNklNOCtYeHFjaWZEREFUVG9ZTUtaelBkcVFrNlhRZG1qQzZMWmVMVVVFYzhoUnUwVFh6aGdlU2xiR2dSbU9PN2dZVHZQTGpkVjVxOXBSZzFBNFJJUWRZcDFGeW8rbmpseTIyVkRBc2ZQVzNBZnVhYjdWWFYycDBSc3JrZys3V083bTBObnVjOHUrMTlGZDhLR1F1RzdrbmQ5WTgvTmxtbnFBaDBnY2NNSkZSZ1NVWXJWMkNVRk90MDhJKzRmVlYwdXorTmNMK1FyU1lFZUFkMGNLdjlpQ3Vvejk4eTM2Mzc0N0dtTjJJblFvYmtocTA0eWNEMGdxTDR4MHdxY0pRVjFKQ3RFUkpGcVlwS1pMS2ZSVCtuME50QkVKZUpoVDhTMnVqbDlheHVJY1UrWG15VFpHeEdLNUNiUkhad0V2VVp1VmFZV3c4bzFpUHY3WDFsNGZjdy91SWJ5ZkxiMjl4cE9Xa0x6L3ViYkd3S2pCN2FmeHVRZ1pYbUpPRElXNWJxYXMrdWxBQmRsa3dWTGdCMXVOdWtzanorL0oiLCJtYWMiOiJjYjdkYjQxYzQyNzRiMTM3YmNhNDZiOWY4YjcxNGNhYzUxZTkwN2IyYTNiMjgyYzEzODA4MTdjMGYxOTBiOWQ4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:39:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjM2OE5hVWtKTC9aM1FncXQ5UFdJVnc9PSIsInZhbHVlIjoiWUpHMWptK09jbFA4QkYxcStMcldqMUFadjVYTnBzV2dTazN3cEVBazU3QnVraU1ZK3l2aGxRaHp4bzExSmJxck5Zc1hET2YwUFo3eFhCRytZclZMV1dVaDVQZWovWXJxWnlQQmttTCs4YlRldm4vOUw4Z0wrbGE5L01BNHhodTZ0TmZQMmd4ak9DTUhUSlJNQlJEbkFCT2V5SktURy9sb3VIMEZIWkRLZ2FIUFZiUzJJTVh4VWFiK0dZK1BscE9XYnAweUhEYTYvNGJDWGErd0dvVWlNMHZrL3RFaUxXVmhlZlVac0lPK2UxZXg4V0V6K05KOXZGTUw1VWdwbzBtTmlPN1NUd1p6WmUyTHBIak5RUjNxRTZVZ1BwZlNwbTdJTXRtME9aSC8yT0ovdjRGV2xWWkRKU2l0RzZ5SE9rN0RlckpsVjhTdGo5UW9zNWE2VXBCYmJwb3lYSlZjVmYwTlpjZHRPT2xrSjhHMnhQY0lZeXBoYlVmK3l1YUNhbnA3b0ZadzJ4S0dnbStRdC9pT2pZb0ZVbDZNdlBqMktZM1lOSFFkT0N5eGluUnhKOFRVVXpvV2hLRXlIQ1B5a1VKdEplUEx1WUJ1aHdGVEdnYzFXUHBHT1llV0p0VE5OTWhZK0VWM2w1LzJ3b0I4R2JZamJhT2FRZ0FwalR4bkxTdE4iLCJtYWMiOiJlMjUwNGNmNzEzNjU4MTYzMDdmMjI4MjMwNWViZWQ0ZWVkYWMxYjI1OWRmOTg5ZjFmMmI5NjUzYWU1YzQ5MTlmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:39:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672616564\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1518267993 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518267993\", {\"maxDepth\":0})</script>\n"}}