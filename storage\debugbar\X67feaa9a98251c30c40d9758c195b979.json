{"__meta": {"id": "X67feaa9a98251c30c40d9758c195b979", "datetime": "2025-08-02 09:34:48", "utime": **********.365311, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[09:34:48] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.358174, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754127286.358628, "end": **********.365345, "duration": 2.0067169666290283, "duration_str": "2.01s", "measures": [{"label": "Booting", "start": 1754127286.358628, "relative_start": 0, "end": **********.017869, "relative_end": **********.017869, "duration": 1.659240961074829, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.017901, "relative_start": 1.6592729091644287, "end": **********.365348, "relative_end": 3.0994415283203125e-06, "duration": 0.34744715690612793, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51803288, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.242596, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.04262, "accumulated_duration_str": "42.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1413162, "duration": 0.00641, "duration_str": "6.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 15.04}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.1829531, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 15.04, "width_percent": 2.839}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 79 or `ch_messages`.`to_id` = 79 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["79", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1910698, "duration": 0.02427, "duration_str": "24.27ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 17.879, "width_percent": 56.945}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 79", "type": "query", "params": [], "bindings": ["client", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.21956, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 74.824, "width_percent": 2.44}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.2609682, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 77.264, "width_percent": 2.276}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.275398, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 79.54, "width_percent": 2.487}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2827141, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 82.027, "width_percent": 2.018}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.290226, "duration": 0.0068, "duration_str": "6.8ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 84.045, "width_percent": 15.955}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 551, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1340893878 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1340893878\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-551591064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-551591064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1210518070 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210518070\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1530717054 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlVRSk02NGlCTXIvRkNaaFRYVmZ3VWc9PSIsInZhbHVlIjoiWWRBWGJ5U3FKTU9pdFdnZXhERm5UbnJCY3B6K0hZVW8xRk1TR2NyU1FjbXdIeHdKZkJmMFZLdnlaZDF3cTdpSkFrTEtiZXdhYXJnVWExTmJ4c2xTcnRIWGdIYlJlWWVoNVVvbFIrK0E5dUhja2ZlM3ppRnFzaUIzL2R0WDZzWmxNUUt2VVV5YjJBcDEyQ0pvZE5hdEpJaWhxVmE0QnM2UzloK1pxVzVSSkhHZE41MGZBUkUwcnpzNkNWRHA5QnJtcmFTMDBsbWE3UDlaTklBNEVxZTV6dytYRGJ0YU5TMmRSem9KOVJDWGRxVk9ISU5tTnhCc2RaM0Rua2xITmNub2hDN2hpVmt2cTY5aFZIV2diMGtkRVhWQm5WeXJmalNUdm40TlRNVVBBOHIwS09xSi9CR0QvVDhRVCtYZ25QVHlQSUMzQlgvbDBlNDU5ZkhhaksyS3ErMEFrVE15R3hsYmppZW8vODhPaGNIeGZXNk5ZdE4wNjE1Q2xnaVVVZUdWT2h3T0hFOTFXeDNHdHN6Q3IxRnFIMzJ5aFRIMGlFR2xRbnJiejhxOTVVS2FzLzJhZEtiQUMvM0hyQ3g2TXdITjc2dG8zMHB6S0hEb3NEQmRQK0QvVTNFK2w4eE5QK2JCdGY5bEI2UVZ4bUpDUlZxRjZPZGpEMVIzOUcxSS9GUEEiLCJtYWMiOiJlZTc0NDQxYTQ3MDYwMDlkYmYzOTJiZDE4MThkZmM0MjUyMzkxNmZkYjA5NTA4ZWMxZTEyYmY2YzQzNDEwNmRhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ino3RnczZWhwLzZkbkJ3bXJzV01McVE9PSIsInZhbHVlIjoic3poQWRnM2xTVlcrMUxsb2JpMXFSQUhqZXBrWlhacHRXWlhGRWJncjJONW04RTdQWEcrMTVZU2RZajNkN0phNkR4dHFqaVcvcEdGZzUzVUlFSmFnVUxjYkRwOGk2NVIzSm1SUjBHWlVWSnBKSXVHRkFmbUtJVitPSkE1ZVdOYTMvZ3hmdllsOUhYWkZIbHNMelMyUUNnSXlUdS9FS0FnZE9yUnlFbU42YWQycHdLQlNZSE9GcEJ4MmpjZnNISXQ1cWN2T3JMLys1WXZrVFdNMjlzbmc4TnBsQlZXN3ZxYmNnQlJRd3pIZW5HZ1VKbXBsVXJ3bDhUSEZWNUphWkxJRk1Bc2FCWi9HajdZenJpWDFoWDR4b0o0NlFVZEhOVnZOemN0ZjVqTzdiU0R5YndsZ2ZKTGE3TU12S21TVWRSQ3U2aWFvdTVCOVhndW9seDVYVlN6T21rNjhIRXI3ZzluVkN2eC9VOHA5bVdMQ1ZLcXR1WVFiMTNmTmhERE81aENRUXNIcVg4WjRYY0NRL2swVVp5Sm5ubGUxWWYxUitQNXpQTVd5Qm05MFIvQVdzUmJKQW9mL3ZZL0FPWjNzWm9jSWRFbmxUVHcvMUN5WE93dnVYdkRQODJNSnliRGlGeU94MjhmSGc2T0dOQTU4S2JYczREekR0ZUErZEErMVdJbDMiLCJtYWMiOiI1ZjZhOTYzMTUwODg3YzQ0YjYzMWExMDNmOTRjM2RkZjc3NThjYjE5OTk1NmQ2N2RmNzkyMzYwZjkwMzU4ZjhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530717054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1052496814 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052496814\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1604220472 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:34:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjU5WlFlbnZxK29uZzdKeWVac0gyVkE9PSIsInZhbHVlIjoiRkRYeWhGNUtvMEtBdnptNU1jWnp6aFZTcWtad2JCY3laWkRET2dQY0h2TmNlY01rd2x6c25TUGZGOXBzV0FvY1NHaVJBeGNBYzVFN3NSY0ZOaXQrNitVRUlHYThtV3VPRjVkTm1VRkZxd0NJWHRHdlhMbnVTeUQrQVZkMm4vRmxrY1E1L0VkbmQwcWdZdkQ3SUVQL2VrTm1XMmUvM01QbGtEUW5TTmt6bThHVThyb1Irdmhvam5YQS9CTERzSnRRWDBUeTVkWks4SkpoY3ZlT0FBcS9ZVkFJVzBrNlFQa1UzVkxIUk9pTEw2bWZmNlRyd3UzeEJZUUd2WHZVdHBRSXN4OXR1WnBteWxFcDIzVXpPVGpOVytWV1BuSW5seElISWdrK1NWcjRlbjV2WUNJZ0dNUXdLQ2pyMEdweWNRT01pbWYrdUNYbGFwVHdjVGg3aWVtN0cxZ0cwS2tOZ0RqemxLSEpKUGVLWERDSlBlb3N4dit4V3FLRUE5NlI5eFlTeHVnZ1pGQXFYbTNNRE51MVQxY295ZWxENy9RbldNY1RCYjFHUDh3aVZCbFFuOVpGdms5U01lMlVDZ096VUpTR2dGVlRnb3RzNTRmMERZYmF4UkNJL3NpSHNhalhZUnZ4MHU3MDRLU3BVZ09RZzAybWs4WnBkUE9BemNYemszTnoiLCJtYWMiOiI1MGRlNmExMzAyOTkxYzk2ZmZjMWQ1NzhhZTUwNjlmMmY5YmMwZmQ0MDIyMzBmY2U4ZTkyODNkODAyZDA1ODZmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:34:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkR3cEVmZnJrWTM5b1k0Q3lnbWFTRXc9PSIsInZhbHVlIjoiRHd4QktMcDdQbVNNM1BUckt6UGxrdmx2QWFOOGo3SG54dXFkOHFEQk82dlhPZms5aVpFTG5NNE0vSnR2dHZCWmdQRFhJSThuWGJPTVAvTzFzclBENUgyaUdZaEpuUHR0NG1sdW9iM3paVCtlNmFIV09FWFJpRDRkd2MwZWIrSDB0N1lydnkrd3ZYajJQTFd3empLSEZ4Z2FQMUlrRkxuVHJDTnZySHVUdkNXeDZ5S1J4M2FWZEZvZC84ZVRvVU8vbGZmUWhNNGpoS0hBVHFWSmsxZUljamdzTzdnVXdCUW5yaXdESVVqNkRHU0czcFB0b1RvQVBaY1Bja3FHWExVeEYyYXJacHVWY2JYRlRVeHkwYlhTR3lWelJ5QTNyUGdYc2gyUEFGZ1paWTNRR0cvUXFwckp4b3hvVk5vZ0ovamZyNzNuNGxEdHJLZ2dNU2lkVWJiUjZFOVJJT0QxZWJQWmJydzJIN291UWV5WnJsWkdORUxHWG43WGtwTjFEcEdFc0hmbWJCWVlSWStYNjl6TFMxUDc5REk2bXd5S1BqRHN3bWFYdE9SZG1TUDgrWXRBUFVFVEtaU2FvOUY5Mm1vYTZJRDdMUVZQWWdPa0lQK083VVpzZ1UvWWtOcjMrb1hjNU9aWFFkREtpcFRKcTN6VmtFMUdWZVM3TTlOYVhUM1AiLCJtYWMiOiI3YWVkY2RkMmY5YjBjODYyYTU1N2YxN2U3Yzc5MTliOTU0MWQ3N2Y0OGMyNjZkNjM5YTkzZjY0ZjhhOTdhMDJmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:34:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjU5WlFlbnZxK29uZzdKeWVac0gyVkE9PSIsInZhbHVlIjoiRkRYeWhGNUtvMEtBdnptNU1jWnp6aFZTcWtad2JCY3laWkRET2dQY0h2TmNlY01rd2x6c25TUGZGOXBzV0FvY1NHaVJBeGNBYzVFN3NSY0ZOaXQrNitVRUlHYThtV3VPRjVkTm1VRkZxd0NJWHRHdlhMbnVTeUQrQVZkMm4vRmxrY1E1L0VkbmQwcWdZdkQ3SUVQL2VrTm1XMmUvM01QbGtEUW5TTmt6bThHVThyb1Irdmhvam5YQS9CTERzSnRRWDBUeTVkWks4SkpoY3ZlT0FBcS9ZVkFJVzBrNlFQa1UzVkxIUk9pTEw2bWZmNlRyd3UzeEJZUUd2WHZVdHBRSXN4OXR1WnBteWxFcDIzVXpPVGpOVytWV1BuSW5seElISWdrK1NWcjRlbjV2WUNJZ0dNUXdLQ2pyMEdweWNRT01pbWYrdUNYbGFwVHdjVGg3aWVtN0cxZ0cwS2tOZ0RqemxLSEpKUGVLWERDSlBlb3N4dit4V3FLRUE5NlI5eFlTeHVnZ1pGQXFYbTNNRE51MVQxY295ZWxENy9RbldNY1RCYjFHUDh3aVZCbFFuOVpGdms5U01lMlVDZ096VUpTR2dGVlRnb3RzNTRmMERZYmF4UkNJL3NpSHNhalhZUnZ4MHU3MDRLU3BVZ09RZzAybWs4WnBkUE9BemNYemszTnoiLCJtYWMiOiI1MGRlNmExMzAyOTkxYzk2ZmZjMWQ1NzhhZTUwNjlmMmY5YmMwZmQ0MDIyMzBmY2U4ZTkyODNkODAyZDA1ODZmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:34:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkR3cEVmZnJrWTM5b1k0Q3lnbWFTRXc9PSIsInZhbHVlIjoiRHd4QktMcDdQbVNNM1BUckt6UGxrdmx2QWFOOGo3SG54dXFkOHFEQk82dlhPZms5aVpFTG5NNE0vSnR2dHZCWmdQRFhJSThuWGJPTVAvTzFzclBENUgyaUdZaEpuUHR0NG1sdW9iM3paVCtlNmFIV09FWFJpRDRkd2MwZWIrSDB0N1lydnkrd3ZYajJQTFd3empLSEZ4Z2FQMUlrRkxuVHJDTnZySHVUdkNXeDZ5S1J4M2FWZEZvZC84ZVRvVU8vbGZmUWhNNGpoS0hBVHFWSmsxZUljamdzTzdnVXdCUW5yaXdESVVqNkRHU0czcFB0b1RvQVBaY1Bja3FHWExVeEYyYXJacHVWY2JYRlRVeHkwYlhTR3lWelJ5QTNyUGdYc2gyUEFGZ1paWTNRR0cvUXFwckp4b3hvVk5vZ0ovamZyNzNuNGxEdHJLZ2dNU2lkVWJiUjZFOVJJT0QxZWJQWmJydzJIN291UWV5WnJsWkdORUxHWG43WGtwTjFEcEdFc0hmbWJCWVlSWStYNjl6TFMxUDc5REk2bXd5S1BqRHN3bWFYdE9SZG1TUDgrWXRBUFVFVEtaU2FvOUY5Mm1vYTZJRDdMUVZQWWdPa0lQK083VVpzZ1UvWWtOcjMrb1hjNU9aWFFkREtpcFRKcTN6VmtFMUdWZVM3TTlOYVhUM1AiLCJtYWMiOiI3YWVkY2RkMmY5YjBjODYyYTU1N2YxN2U3Yzc5MTliOTU0MWQ3N2Y0OGMyNjZkNjM5YTkzZjY0ZjhhOTdhMDJmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:34:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604220472\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-729095822 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729095822\", {\"maxDepth\":0})</script>\n"}}