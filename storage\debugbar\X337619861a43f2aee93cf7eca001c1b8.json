{"__meta": {"id": "X337619861a43f2aee93cf7eca001c1b8", "datetime": "2025-08-02 08:40:15", "utime": **********.627022, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754124013.66911, "end": **********.627081, "duration": 1.9579708576202393, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1754124013.66911, "relative_start": 0, "end": **********.469385, "relative_end": **********.469385, "duration": 1.8002748489379883, "duration_str": "1.8s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.469444, "relative_start": 1.****************, "end": **********.627086, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OFk4gAI9TSE4FKA0898ofDXhPbYjOdxDtUBzhMCq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1777932149 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1777932149\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-5778215 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-5778215\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-467370685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-467370685\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2025034458 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025034458\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-479690299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-479690299\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-11658841 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:40:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkYrNEVoRTUxWkRRMy9jTzJMMit4RlE9PSIsInZhbHVlIjoiQ3VWNmlQcmVtT3lTcW05WHczYzEwZW4zUUsxdm1TTVhaSVVqNGtIZSsrTk10bHRQQUUxLzdmekFyTkJvVG93eStkMzFlTW5pNzZTNUNrdzZOOUppWXBSbFplNTNZUk1nc25BaHR5QXoyQTdhaFlKWUY5R3ZrRlIveHZCNVhpMDB4WDc2MmdDdXBXS1kweWZDT21JSzJmRW5MWEQ2UVZnc0ZJUDRBSEptL2FWeGF3bUcvemRQMEZJOS94amVIaUdNQTlVU05mVCtLem1raHpSQXhFN1BSMzFONVRGdjdjRStsc2R3bk1MeStvMHNXWVI0bjhXdk0wZ3dGeDJ2c0JESE14Y0ExeTNHVGtCTGZGMlZtaXdySWNUV0Q5VnNlYjYwNDhyZnJTaUNLOXRCV0d3dG5ScmdkRjdPbUdidXhBbU5SZmhwblNlcGVuZHIvK2l1ZnVhOEU5UXZjeFQrRXVqeWVHQ3JNYlZmZFdOWDFrRTRRY3hzdzQ2ZEZrT0oyeU5MRDVvS0RmeTFXbU9sT1RYYUhRb3hVa3UwUEdxL1ZkYldxdUxzdDRDaDVQWVZCNkU4REJlc0twZHFLNmJObk9RczlvTjgxUVgrYUNRVW0rZ05GTlVGSVVWQmp3RUE1MEM4VVNnT3QrNGRteGFIdDFFSGViYmNrdzdwYUd5dldtdW8iLCJtYWMiOiJjNjc3ZTE4NGYwZTEzNWEyYjQ3ZjY0OWJmMDYxZDVkZWMzN2JmNDcyNmE4MGQ2MTg3NjY3OGJjMTVkMzNjMDYzIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:40:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InU5d0RQUFgxUm1iaE4vZ1Vad1VtVkE9PSIsInZhbHVlIjoiNlUzQzhqbTd1bjBVNTVDaVhjRnUwSlkraXNZQ1NZbFVxTGl1RHRxNTJ0cmhuSHR3T1ZPM29UZ3JVRGg4M29JWGJFenU1UkZKZTdESVlDL3ZHNG9seDduNmJNTmJJWXdZMlM5TzY5MHBVbGdUaGVPWVc0ekdOamRxN1F6eUVBRmRlNy9xTm5FYVZVNS90b2tmUk9EdjV2eUxoYkZNV3d2ZmI4dlJoTWJUOXlGemdtQ2lqUUlXb2Z0a3V5bjM4Mk9PUytleDFoVXk0VHBBL0ZvT2ZXUW0vdXpDRUZMKzNvb2NpVDZGU0JBWWpMY29XN0IrZmFVM2YrYmVCTnNYRnpHTmd5eWorRWMwYVg4Mm8rNS9NMmxUMTA3enpSMTN0Q2JnVTVVZDlrUmhSZXpkNmV6VUEzeDBWYXg1VUI0TnNkVU9VbmlBSjBMUVZ0dzl3eVVSU0sxMGdOUm9CemozMGY3RWFBZnFhUS8rdCszUklvVmxNbHJmaFhlTDA0a1dCd1d3dnM5Nm5VZjgzK0JDZTRtVklyd0lJSjJYUDRuR2ZDS0VDeXFYUHd4QUx3QmgvY1JOUDdUQmhkS2tQdzlDTUlUd2RaaTJRZGhTdzBRdG5maWVFb1NPWGpEbE0zMzFoR2xUUXYxT2oxVCtrczhvWnp3aVBuNUdNRE4wRTA1NTN0Qk8iLCJtYWMiOiIzZWExYzIzZDExODUzN2I4MjVkMDU2NTJhZTM5ZmY0NDgyYmVhOTQyNWY4Mzg0MmY5NTM4MTYzZDQ3ODk3MDI3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:40:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkYrNEVoRTUxWkRRMy9jTzJMMit4RlE9PSIsInZhbHVlIjoiQ3VWNmlQcmVtT3lTcW05WHczYzEwZW4zUUsxdm1TTVhaSVVqNGtIZSsrTk10bHRQQUUxLzdmekFyTkJvVG93eStkMzFlTW5pNzZTNUNrdzZOOUppWXBSbFplNTNZUk1nc25BaHR5QXoyQTdhaFlKWUY5R3ZrRlIveHZCNVhpMDB4WDc2MmdDdXBXS1kweWZDT21JSzJmRW5MWEQ2UVZnc0ZJUDRBSEptL2FWeGF3bUcvemRQMEZJOS94amVIaUdNQTlVU05mVCtLem1raHpSQXhFN1BSMzFONVRGdjdjRStsc2R3bk1MeStvMHNXWVI0bjhXdk0wZ3dGeDJ2c0JESE14Y0ExeTNHVGtCTGZGMlZtaXdySWNUV0Q5VnNlYjYwNDhyZnJTaUNLOXRCV0d3dG5ScmdkRjdPbUdidXhBbU5SZmhwblNlcGVuZHIvK2l1ZnVhOEU5UXZjeFQrRXVqeWVHQ3JNYlZmZFdOWDFrRTRRY3hzdzQ2ZEZrT0oyeU5MRDVvS0RmeTFXbU9sT1RYYUhRb3hVa3UwUEdxL1ZkYldxdUxzdDRDaDVQWVZCNkU4REJlc0twZHFLNmJObk9RczlvTjgxUVgrYUNRVW0rZ05GTlVGSVVWQmp3RUE1MEM4VVNnT3QrNGRteGFIdDFFSGViYmNrdzdwYUd5dldtdW8iLCJtYWMiOiJjNjc3ZTE4NGYwZTEzNWEyYjQ3ZjY0OWJmMDYxZDVkZWMzN2JmNDcyNmE4MGQ2MTg3NjY3OGJjMTVkMzNjMDYzIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:40:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InU5d0RQUFgxUm1iaE4vZ1Vad1VtVkE9PSIsInZhbHVlIjoiNlUzQzhqbTd1bjBVNTVDaVhjRnUwSlkraXNZQ1NZbFVxTGl1RHRxNTJ0cmhuSHR3T1ZPM29UZ3JVRGg4M29JWGJFenU1UkZKZTdESVlDL3ZHNG9seDduNmJNTmJJWXdZMlM5TzY5MHBVbGdUaGVPWVc0ekdOamRxN1F6eUVBRmRlNy9xTm5FYVZVNS90b2tmUk9EdjV2eUxoYkZNV3d2ZmI4dlJoTWJUOXlGemdtQ2lqUUlXb2Z0a3V5bjM4Mk9PUytleDFoVXk0VHBBL0ZvT2ZXUW0vdXpDRUZMKzNvb2NpVDZGU0JBWWpMY29XN0IrZmFVM2YrYmVCTnNYRnpHTmd5eWorRWMwYVg4Mm8rNS9NMmxUMTA3enpSMTN0Q2JnVTVVZDlrUmhSZXpkNmV6VUEzeDBWYXg1VUI0TnNkVU9VbmlBSjBMUVZ0dzl3eVVSU0sxMGdOUm9CemozMGY3RWFBZnFhUS8rdCszUklvVmxNbHJmaFhlTDA0a1dCd1d3dnM5Nm5VZjgzK0JDZTRtVklyd0lJSjJYUDRuR2ZDS0VDeXFYUHd4QUx3QmgvY1JOUDdUQmhkS2tQdzlDTUlUd2RaaTJRZGhTdzBRdG5maWVFb1NPWGpEbE0zMzFoR2xUUXYxT2oxVCtrczhvWnp3aVBuNUdNRE4wRTA1NTN0Qk8iLCJtYWMiOiIzZWExYzIzZDExODUzN2I4MjVkMDU2NTJhZTM5ZmY0NDgyYmVhOTQyNWY4Mzg0MmY5NTM4MTYzZDQ3ODk3MDI3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:40:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11658841\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1668497370 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OFk4gAI9TSE4FKA0898ofDXhPbYjOdxDtUBzhMCq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668497370\", {\"maxDepth\":0})</script>\n"}}