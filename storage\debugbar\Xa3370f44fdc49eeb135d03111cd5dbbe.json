{"__meta": {"id": "Xa3370f44fdc49eeb135d03111cd5dbbe", "datetime": "2025-08-02 08:26:48", "utime": 1754123208.099734, "method": "POST", "uri": "/leads/json", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:26:48] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\\/json\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1754123208.089291, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754123206.338254, "end": 1754123208.099788, "duration": 1.7615339756011963, "duration_str": "1.76s", "measures": [{"label": "Booting", "start": 1754123206.338254, "relative_start": 0, "end": **********.734876, "relative_end": **********.734876, "duration": 1.3966219425201416, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.734905, "relative_start": 1.396651029586792, "end": 1754123208.099793, "relative_end": 5.0067901611328125e-06, "duration": 0.36488795280456543, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47727768, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads/json", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\LeadController@json", "namespace": null, "prefix": "", "where": [], "as": "leads.json", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=969\" onclick=\"\">app/Http/Controllers/LeadController.php:969-985</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03541, "accumulated_duration_str": "35.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8488781, "duration": 0.02069, "duration_str": "20.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 58.43}, {"sql": "select * from `lead_stages` where `pipeline_id` = '23'", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.884843, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:977", "source": "app/Http/Controllers/LeadController.php:977", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=977", "ajax": false, "filename": "LeadController.php", "line": "977"}, "connection": "radhe_same", "start_percent": 58.43, "width_percent": 4.744}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.930906, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 63.174, "width_percent": 4.095}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.960983, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 67.269, "width_percent": 4.829}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.974654, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 72.098, "width_percent": 4.575}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.988584, "duration": 0.00826, "duration_str": "8.26ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 76.673, "width_percent": 23.327}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 555, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/json", "status_code": "<pre class=sf-dump id=sf-dump-579993489 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-579993489\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1805581925 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1805581925\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1831633854 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831633854\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-349890309 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1ja2ZHczhjWGNSSHF2eUNxMWszVGc9PSIsInZhbHVlIjoidm5RRUlmMnkwNUlpbXEvWm00bjR5T2FKOElnVHlidlJNNGZha1Bjc0svY093YzhPMFNlZXdsR2dSUkd4dHl4YzBKdnFmdkp4RU5NM2xXWWdNOWtqdkNyeThualR6SnRsdDJRTW4rblMzdE1MbEJKRWRjTTJHVnpGZGVtMGRJdlFlZjF1aWZFdDRBb1c0aW4wTXRQd3pkb1JQMTdCajl4QXowOWd3NFJyQVRVVEtkK2dZVUx1bElVZUZycDYwb3lEbW81UFh6a1o0RVpadXdUbXRONjRtaUtZeGZTUG1pK0ZqL24wY0lpL1dUSDdEUU5wc0JVMmNibWVWYVdRNDlKcGtLQkE5UW0yQ25GdVczOG1HMVAwNkg4b3cxd05YSHFickpPM0JrMk5mZXpFTHBjNHg4VlZMQW0za25GZHQvN3UvclVHRS9xSFpCbGU0K2Y5Z3VzV3FFalVBSHhpRk9XZEp6SEo0bkQzV0FKazRYTHllRDNVNXN0c2Z4YktaQ2JiUEExbDJBVzMvY0hJeExSWitTUVZWcEhuK3lTYnVIeW5CbUUrakJPQkFnZFJ5QU1UUlZsM25JQzZUNnp3Q3RBcCtydEpWcVIzeDJadmNoVnlxa3AxdmN0VDB6ZUNFQmFtTndtdG1XMUNnWDZnY1hlWlJrTnMwRmE1eDEvL3JadTciLCJtYWMiOiJlYjkyNDBmZmI5MTQzYmEyYzBhYmE4N2MxZWRlNDc2YjBjY2VlYjgxOTFiMzAxNzc1OTk2MzNmNmQwMjIwNDYyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlNSVnV4S1FqNzJJbndSVWJ1bW56dHc9PSIsInZhbHVlIjoiUFB4N1lqdVRkSDRDVnB2STQ1VUlTZ0VRa1BoZHJlcnVOLzlsUFVTb0hnV09vWGEvM2dMMnNoRzdYSkJ3VjhIQ0V1ZldqMmlqL2V5V1d6UEtRdC9tR05nUGRtcjNaakZhRys5RnE3a2tJSlJONWloVDVsclpQV0FoQjgyMG9uSUw1eU84dVo2Qm9tYldXbS92VEJpcUhMNDBOdndYUDJZSHdRYmx1aGNSeTdUZDFzM3IzWHY2eXZNbTgzQnZ2RjhoalZUVlk0U2JFNlJxdU5DckJDMEVnWDk2cjJHbHNWbzk0aDNpaG5ZUkgvajRud2thQU9SZkF5T2VxQjdkOENIVE9GN29mL3hLRkxESGdzaWlJbVFsR3AzdEM5a0ZVT1pQbHlIWjE1bHdwZUlYRUt5ZHJOS2dYclA1MlQ5cERaRTR1WDMxTVozbElzck1GTXllYnBjdVRyNTRUUnRPV1NOekJDUVRlRWIreUk1eEI5dEFPOERGT1J6M0J4amJWVjFQdmlkS1JEVTRLSUF0YjMycUhXSEYrSFRvejZqWVpIMkFrOVZpY2J3MXlLcG14aGJjVGpRanMzRWlMMHNzem5TOVNicVI0K2Q5UFRXNjdwQ1JyZzBTT2xKZU54TUpRYng2eUpZYzAwMlVCcytFWFFLWTBxVDBTYVNZeWEwbXJUNEsiLCJtYWMiOiJlOGMzYWUzZGUyNTdiMzg5ZTljN2EyMTVjN2I3MGM3NzA5ZTE0NDc5NDFjMDQ1ODQ5ZTcxOWQwYWNjZDkxZGIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349890309\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-376771684 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376771684\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:26:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhYNFdDbngwSFNTVk5kTkd3YWRpT1E9PSIsInZhbHVlIjoiZlhSR00xTVhaeGpyZGdTbG1lUFNpYWgyczdNQzNCS0RXQXluZi9ia1Uza2RsVFNRSkl0WXJoWjAxTW80cVR2dFlJTG10NnFwRDV1N0ZZYTZFcGZBcjFIelVSRUEzcWdhV2ZJSUFtSE5VNjgxZSt4dTYvblBJRG5xUXJkL0FuR2NZNVRNSGpsNEY2NmxmWS9QODdWNFUxWkNlMkhIVlhPeFVEMGViWGdtZS9MdHRXaTdwR0MzMjBwSzRrYUxaa2hoUStoL05WVmFpNFVRRlJhenBiUk5rblBxeTV3ckNFcGF6Y1Y0OGJrZUVHVFFoRWJTdGNJWVVRUEhIZ1NDVXRUaTlYUWVOZWRGMC95bU5maTdIU1pHMUgzK3Jzd1lkUmhWYURUTXk3eVFSZjVsN1JtZitKVWxuNUEySTl3ZjVXVWpFbnRnUEpaZFhoS1dmbkVML0RxL3pON2ZzOGJBUkhkUmtZaUxXK0J5TDh4aDhmTENWMjE1ZDZveGptT0k2c3liVzF6bUhsV0RnMkNDeHVOZkxQa0xiMTJaRlFxVVlSSVZrQTdybVpJaHU0MkVXRmlEeXd6eVUxaWFnRHBTdWZrQ0RPaEs3dEhjR1dkREFLNWIyOXJZemFIeWVmNDR2bmpLSFVRbUtuUGV5NURpeWpPajFaNTlkRkI2aGRWN1pMQzYiLCJtYWMiOiIyZjJlZjA4MWU4YmQwODA1MTFjMmFhMjlkMzM1NDY4ZGUwYWNkNTE3NDQ5YmU1YjA1M2MzY2ViODVlMDJkODIyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:26:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InpoRDI5cldPQTFlNzQ5b3lDUEdFVFE9PSIsInZhbHVlIjoiZ1pPbWdMTG05UkVrb0VuUEZoM2hvOWVRUVV0cTNPOTc5RGFaUmZJcUZwcGVrY1NrTlRGaXE3OGlqNUFFWFdmeWFLZU9ON3Urakc5aklKR2RJdnRyYi9uTFZVNldyWGxsdUtuT2pubmF0Yy9Kb0tEV09VUHByUzFabE1ZM0pwcVBPVFdwRmVzU3daYWM3WDBDUEZRYUxtZkJvcjFDTFp6dHF2YUNPL3N6TnluYmZyQ0twc1NZVHA4VHZ4cXRWcXZSbHZHblg4aUd4QzdzQkxjaHY3WUxybG5KNnIyR2E2cDIrcStDanU2by9pWnp6VDNBT3hsUk9yaHFQVHBPOVlIZGlZNXNiVjdMcEp4ZjNLUEFKK0VFaWRqZXU5M3Bub3F1RVVlb3BtUXEyOFhHTUdqT0dJdkhIdm9DcldHeTZQRVIybFNmNVAxR2VmQzBJdElyUjRCaDI1eU1GOEU1YSsrbTN0NmRsYTlaMk16ODExUzAwbFlYY0RacnRmbTQ5TFFzOEJ4L3U5OWdVVytRL3RJRVB0MmhxQmNUOXNqTVhUYVVlenFlMjJ6c25VUS9STUhtSjA0KzhCT2FVd0xZc3VHbmtDWjNPaDB0cTU3Mk9VaFp4Y2t5Z2Jra2RsSDJRdDJlcldhaG91MnJ6UklIYmE2NHM4ZWk3OXA3Z1ZBRUFUYkUiLCJtYWMiOiIyMGZhNmRiZmI1YzY0ZDQxYjNhNWQ1MTUxNjMzZmUyYzQ3MWFjNTdiNzg2ZWM1YWRhNGQxYTdmMmNmODVhZDNmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:26:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhYNFdDbngwSFNTVk5kTkd3YWRpT1E9PSIsInZhbHVlIjoiZlhSR00xTVhaeGpyZGdTbG1lUFNpYWgyczdNQzNCS0RXQXluZi9ia1Uza2RsVFNRSkl0WXJoWjAxTW80cVR2dFlJTG10NnFwRDV1N0ZZYTZFcGZBcjFIelVSRUEzcWdhV2ZJSUFtSE5VNjgxZSt4dTYvblBJRG5xUXJkL0FuR2NZNVRNSGpsNEY2NmxmWS9QODdWNFUxWkNlMkhIVlhPeFVEMGViWGdtZS9MdHRXaTdwR0MzMjBwSzRrYUxaa2hoUStoL05WVmFpNFVRRlJhenBiUk5rblBxeTV3ckNFcGF6Y1Y0OGJrZUVHVFFoRWJTdGNJWVVRUEhIZ1NDVXRUaTlYUWVOZWRGMC95bU5maTdIU1pHMUgzK3Jzd1lkUmhWYURUTXk3eVFSZjVsN1JtZitKVWxuNUEySTl3ZjVXVWpFbnRnUEpaZFhoS1dmbkVML0RxL3pON2ZzOGJBUkhkUmtZaUxXK0J5TDh4aDhmTENWMjE1ZDZveGptT0k2c3liVzF6bUhsV0RnMkNDeHVOZkxQa0xiMTJaRlFxVVlSSVZrQTdybVpJaHU0MkVXRmlEeXd6eVUxaWFnRHBTdWZrQ0RPaEs3dEhjR1dkREFLNWIyOXJZemFIeWVmNDR2bmpLSFVRbUtuUGV5NURpeWpPajFaNTlkRkI2aGRWN1pMQzYiLCJtYWMiOiIyZjJlZjA4MWU4YmQwODA1MTFjMmFhMjlkMzM1NDY4ZGUwYWNkNTE3NDQ5YmU1YjA1M2MzY2ViODVlMDJkODIyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:26:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InpoRDI5cldPQTFlNzQ5b3lDUEdFVFE9PSIsInZhbHVlIjoiZ1pPbWdMTG05UkVrb0VuUEZoM2hvOWVRUVV0cTNPOTc5RGFaUmZJcUZwcGVrY1NrTlRGaXE3OGlqNUFFWFdmeWFLZU9ON3Urakc5aklKR2RJdnRyYi9uTFZVNldyWGxsdUtuT2pubmF0Yy9Kb0tEV09VUHByUzFabE1ZM0pwcVBPVFdwRmVzU3daYWM3WDBDUEZRYUxtZkJvcjFDTFp6dHF2YUNPL3N6TnluYmZyQ0twc1NZVHA4VHZ4cXRWcXZSbHZHblg4aUd4QzdzQkxjaHY3WUxybG5KNnIyR2E2cDIrcStDanU2by9pWnp6VDNBT3hsUk9yaHFQVHBPOVlIZGlZNXNiVjdMcEp4ZjNLUEFKK0VFaWRqZXU5M3Bub3F1RVVlb3BtUXEyOFhHTUdqT0dJdkhIdm9DcldHeTZQRVIybFNmNVAxR2VmQzBJdElyUjRCaDI1eU1GOEU1YSsrbTN0NmRsYTlaMk16ODExUzAwbFlYY0RacnRmbTQ5TFFzOEJ4L3U5OWdVVytRL3RJRVB0MmhxQmNUOXNqTVhUYVVlenFlMjJ6c25VUS9STUhtSjA0KzhCT2FVd0xZc3VHbmtDWjNPaDB0cTU3Mk9VaFp4Y2t5Z2Jra2RsSDJRdDJlcldhaG91MnJ6UklIYmE2NHM4ZWk3OXA3Z1ZBRUFUYkUiLCJtYWMiOiIyMGZhNmRiZmI1YzY0ZDQxYjNhNWQ1MTUxNjMzZmUyYzQ3MWFjNTdiNzg2ZWM1YWRhNGQxYTdmMmNmODVhZDNmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:26:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}