{"__meta": {"id": "X51807a4c5959bc0dd1805352000be025", "datetime": "2025-08-02 09:34:36", "utime": **********.284317, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754127275.207822, "end": **********.284349, "duration": 1.0765268802642822, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1754127275.207822, "relative_start": 0, "end": **********.176512, "relative_end": **********.176512, "duration": 0.9686899185180664, "duration_str": "969ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.17653, "relative_start": 0.***************, "end": **********.284353, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "e9jIOpnUtD4dJQMOhJhe65CCt9WWlMJ6DS7Eq2DR", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1325428198 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1325428198\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-228172717 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-228172717\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1846522032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1846522032\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-245114182 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-245114182\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-608575126 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-608575126\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1702462572 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:34:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxxR21uM3VkYjNmcjZFTkRKTVhxdGc9PSIsInZhbHVlIjoidDN4Tjlja21SVWZHSlllMXJrMTJNZXRqclIyanN6MDA0NmtCU084dlJGMTE0Mms5N1p5d05NS3ZCZmRtMU9UcjU1NW9ocVBmZjdHS0VRTlVnT1N2Wi8ydVdkbWR2ZDR2djlVTlZnNTlTTWxCcVlQVmR4UkcrYk1DOStXcW1iNlN2aVNIMlZnMDNVV09tUEhqcDMyV3k2eitaV2pqSFRCMjlrMDNndC9QRVpteHR3UGJvMGZKLzdMakM4R1FzSFE0UVVmb3gxYmZvamdYYSt6cTF1YjlBM2poNXM0cS9ON0VWa05EeVJVOUR1SVV2UHJLZ2pWY1dKUG0rejNCYkF3dTRZYnloa3RObnMrWldOYkdxaGRjR1ZEeDNLUmg1R0Qva3c3UXNWVE1VczJSc0g4SDBDY01GVEttUFA2WGVoNnNSTlBYRUZZbUtPRVNzRE54UkVtU3NGU2xIbG9BQU1ONFBZekdQR0ZDRTZLeDZUQXFFbm9VWnBFVDQ5ZERFM1NIMHA4NjdYQ3JnK1o5aWVjdnhxWWt0TE1vaWF5c2laUjJaYmgrS3BBTzVJc3pLdkRpbmhpUGJMcmVZc2k4RFRUZnNpVHR4a29NWU50ZWJaTkhha0dBbWxFdDB4M0Y0bVh3SExsdjhmTllJd1VaeEsvbUxlbjNhY3FRcHJZeXl6MngiLCJtYWMiOiI2M2JiNGEzN2VmZDExNzM3MDNmMmJhMTk5OTFjOWJkMjA0NjI5ZDE2NTUyZTczZDUwMzNjZTg5MjlhMjEyNzkxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:34:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZaUi9ucUJyWllyT0hIRm1tSjByMXc9PSIsInZhbHVlIjoieDZJZW10ZE1YQmxwTzQybHlmZC94M2dhRFIrdVR0bmlKdlZVV1k1WFBDZzlVZlNDTnhTbjd5WHlEQUxubnA4MTNISnpXZ0gzdTBYSnVkeVV4T3V5UXhvZTErS1NJbnhuRkJSTHNTQWZnbGNLK3Y4bVo0bGsvWHdyV2xhbGx3alQwU2pYVVhUL3VQQU1VUmQvclBqYlIyMkNpamZnQ0ZUZXU1K3pzVExSanVzUkdXbUdIbk0rS1dnU1UwMkJCZHNvSU1ZWDJUSmg1MnlTTE9oQkFqeUlheFJSb1Bmc29uWFFRV29nUy8zVVpONzl3bFY5RXNOMjJQYlVoZjhMY1M1cERtL2NlcWYwSTB6bm9SaWt2bnUvblJuSEpyQU56WHZKemkrSkp6YmhxdDNXNlZhT1dod1UzZUs4bDRZZE1DcnNMZmhuWDczNTBkYmVzelNmRm9udUZhNWQzdEtGbktlUUc1MmhzTDlMQURNNEhXR21KaDVaOVQvOVVhYldlZUlnNkFKeDJaMXV5Yi9zVUFaaWJnZS9GTmd4bS85UEY0UFNJTFd6dWZSMUdPcVdGaW1LMnUwSG01RGpHOTdaTnNuTjV3NGJTczl3bzAwME1wT1AveWJBT2xKdWVTcS9GT0N0bjdmWERyYTIwS1N5cklwaFFpVkNxd1R6Y2ltVVNrWGciLCJtYWMiOiJlZmE3YWE3MDUyY2QyZTA0YjRhOTM4OTI4MTM0YTU5YWRjZGY1MDcyN2I4OGY5NzMwZWNjYmRiZjlmZDcwYmFiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:34:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxxR21uM3VkYjNmcjZFTkRKTVhxdGc9PSIsInZhbHVlIjoidDN4Tjlja21SVWZHSlllMXJrMTJNZXRqclIyanN6MDA0NmtCU084dlJGMTE0Mms5N1p5d05NS3ZCZmRtMU9UcjU1NW9ocVBmZjdHS0VRTlVnT1N2Wi8ydVdkbWR2ZDR2djlVTlZnNTlTTWxCcVlQVmR4UkcrYk1DOStXcW1iNlN2aVNIMlZnMDNVV09tUEhqcDMyV3k2eitaV2pqSFRCMjlrMDNndC9QRVpteHR3UGJvMGZKLzdMakM4R1FzSFE0UVVmb3gxYmZvamdYYSt6cTF1YjlBM2poNXM0cS9ON0VWa05EeVJVOUR1SVV2UHJLZ2pWY1dKUG0rejNCYkF3dTRZYnloa3RObnMrWldOYkdxaGRjR1ZEeDNLUmg1R0Qva3c3UXNWVE1VczJSc0g4SDBDY01GVEttUFA2WGVoNnNSTlBYRUZZbUtPRVNzRE54UkVtU3NGU2xIbG9BQU1ONFBZekdQR0ZDRTZLeDZUQXFFbm9VWnBFVDQ5ZERFM1NIMHA4NjdYQ3JnK1o5aWVjdnhxWWt0TE1vaWF5c2laUjJaYmgrS3BBTzVJc3pLdkRpbmhpUGJMcmVZc2k4RFRUZnNpVHR4a29NWU50ZWJaTkhha0dBbWxFdDB4M0Y0bVh3SExsdjhmTllJd1VaeEsvbUxlbjNhY3FRcHJZeXl6MngiLCJtYWMiOiI2M2JiNGEzN2VmZDExNzM3MDNmMmJhMTk5OTFjOWJkMjA0NjI5ZDE2NTUyZTczZDUwMzNjZTg5MjlhMjEyNzkxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:34:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZaUi9ucUJyWllyT0hIRm1tSjByMXc9PSIsInZhbHVlIjoieDZJZW10ZE1YQmxwTzQybHlmZC94M2dhRFIrdVR0bmlKdlZVV1k1WFBDZzlVZlNDTnhTbjd5WHlEQUxubnA4MTNISnpXZ0gzdTBYSnVkeVV4T3V5UXhvZTErS1NJbnhuRkJSTHNTQWZnbGNLK3Y4bVo0bGsvWHdyV2xhbGx3alQwU2pYVVhUL3VQQU1VUmQvclBqYlIyMkNpamZnQ0ZUZXU1K3pzVExSanVzUkdXbUdIbk0rS1dnU1UwMkJCZHNvSU1ZWDJUSmg1MnlTTE9oQkFqeUlheFJSb1Bmc29uWFFRV29nUy8zVVpONzl3bFY5RXNOMjJQYlVoZjhMY1M1cERtL2NlcWYwSTB6bm9SaWt2bnUvblJuSEpyQU56WHZKemkrSkp6YmhxdDNXNlZhT1dod1UzZUs4bDRZZE1DcnNMZmhuWDczNTBkYmVzelNmRm9udUZhNWQzdEtGbktlUUc1MmhzTDlMQURNNEhXR21KaDVaOVQvOVVhYldlZUlnNkFKeDJaMXV5Yi9zVUFaaWJnZS9GTmd4bS85UEY0UFNJTFd6dWZSMUdPcVdGaW1LMnUwSG01RGpHOTdaTnNuTjV3NGJTczl3bzAwME1wT1AveWJBT2xKdWVTcS9GT0N0bjdmWERyYTIwS1N5cklwaFFpVkNxd1R6Y2ltVVNrWGciLCJtYWMiOiJlZmE3YWE3MDUyY2QyZTA0YjRhOTM4OTI4MTM0YTU5YWRjZGY1MDcyN2I4OGY5NzMwZWNjYmRiZjlmZDcwYmFiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:34:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702462572\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1965199734 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e9jIOpnUtD4dJQMOhJhe65CCt9WWlMJ6DS7Eq2DR</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965199734\", {\"maxDepth\":0})</script>\n"}}