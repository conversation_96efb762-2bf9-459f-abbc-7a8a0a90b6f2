{"__meta": {"id": "X0e32f57466a62f9c15ba57e84d60220c", "datetime": "2025-08-02 08:21:44", "utime": **********.955548, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754122903.757118, "end": **********.955578, "duration": 1.1984601020812988, "duration_str": "1.2s", "measures": [{"label": "Booting", "start": 1754122903.757118, "relative_start": 0, "end": **********.877607, "relative_end": **********.877607, "duration": 1.1204891204833984, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.877627, "relative_start": 1.****************, "end": **********.955581, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "77.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "C6Aql5Bn7BRHxHdJ9ts43aJAJGp52XSet04pj843", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-122864107 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-122864107\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-762352273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-762352273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-612164624 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-612164624\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1072868252 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1072868252\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2124213736 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2124213736\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-603458404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:21:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhlbXN1emVESUVYaFNhUE9NaTB0L0E9PSIsInZhbHVlIjoib3p2QWRWY1BXNTBNMDRhQnpJUlowZWp2T1hWMWxJUWQ2QjEyS0NRSzlJVjFpUlgrZFl6RjZ0WkVQSkZTbHlRRW9RcHYzZG94R1JqNUJjZHBWeUlpVlliMHlZek94QmtzNXBlanM0ZmR6NHhXVGxMVXUyelpsbUUyZW9IMkxZWVlnTUVxYkNhR2FKRkwzKzMrMHhzMm1XcWV6Y2ZNdFE1NjhVSGhWalljcmF6UkxqTktnLzBpZEYzTlFVRytyR1hoUlBJRjVDVDJjRk1hcUs5OWxoQWp4ekNGUXF0MlQrWlozSzBhd05CQklObjNCdGNvUWpzS045Q2k5a2lSczFqSGNsZUFxSS9iMHc2bFVxYnhOMkJDWDZhOVFEY3RtYUplOWxRUEdXbUlVWUd1Ulk0dmNOL3ZWMThNcTJQQ2tIOTMrMVp3bEFxMVZZVlhOMlVlT0hUc1Q3Sk5Eam92Wmh6SnhTV0RHR0UzN0tFM0kwUm1jZ0R4djNvbGgvS3pJSlJ6T3lQaHN6K3lmNk9VM1BZMlpHLzl1UnBzUHdOckRXeXk3cm1rK3hRclBaOGthZEIxclVmTkxXNVI0OEZnNE11RmNQS281aFZmWWlaanFJVkx3U1pQOHhaam1JWDBGaFIyRHZyK1gyOHMzc2ZoWFJXU1lBVXhCREZ5dHpiM3hsNzEiLCJtYWMiOiI4MDQ4ZjQ1YTg5NDI1NzUxOWQwOWNjODM4ODU2NmQ1ZDhkNmEyMTg4MjE2NzZlNTUxMmI3MzMxOTYxMzA4NDJhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:21:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRzV0RGcmZtdmtNRGdOVlRGaHVITlE9PSIsInZhbHVlIjoiM0N3bjdxV3MrV25qOVJaeG10dUU2WVY0Uk9hN3hPVnY5Zm9BV3dTdVZVeEhVQSt1a1g4TUNvUnpHWmxOVloyNTZKbmhTY0pXb1FiaEtMQmhUY0plYWZWM20vOXFTVXFaeTFHTGVnWXRyS2tYcGxLeWNPWG5vSXl2a1NGMVpNSFRiKzRZMWFKTTlaYzNJazdwNVQ5blE2UTR0MDl5RFNGaUdXQUFTbk1ybmdTcytrVzJBbG4xWi9mbTBNNHY5ZVZvYzlveHFDZFJGYloxVlJyVE9MNWREZldQQ3NNc1BnVUJoNFNTSDBtQ0FhcmRUSkF6alJmaHV2alVLejBTdytFU3RWV043cTFpOUVsdWlmUXZUYk8vY2IrTTBkdEpNT3M1WFU1WVVQYnV6T3BxZC9JUjRwRnlxOVJGTkpZeXd5UVY4b3FpZit1VllQKytsbnE1dGR6YXJaRXY1U1FCWkNVYys1cWlxTDlLNHl3R1RVQVFpRjUrM1N1RnBFeTBmcVdnc3pnYmg1cVFuazB3MWpJV0dSUXVQZWFuOUp2TkdGUkJSN1d2TThYTFRFWm5zVjZBcWdXN0U5Q2FZM2Y5aDY1bWVaS0doOXJkZWxSb0xJUmRneEtlTkNGZlo1U25hZGpUdUEvUFNVMWV0aCsxTC8vLzFUNkV6YTRTTHdWUjdnOUsiLCJtYWMiOiIwOWQ4NWZmODZjNjdlMTQ0NzFiODZhNDg5MjQ5ZjQ1NDkwMWU0OGM2NDNhYTg3OTM3OTJjYmQ3ZTY5MGI1ZWEyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:21:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhlbXN1emVESUVYaFNhUE9NaTB0L0E9PSIsInZhbHVlIjoib3p2QWRWY1BXNTBNMDRhQnpJUlowZWp2T1hWMWxJUWQ2QjEyS0NRSzlJVjFpUlgrZFl6RjZ0WkVQSkZTbHlRRW9RcHYzZG94R1JqNUJjZHBWeUlpVlliMHlZek94QmtzNXBlanM0ZmR6NHhXVGxMVXUyelpsbUUyZW9IMkxZWVlnTUVxYkNhR2FKRkwzKzMrMHhzMm1XcWV6Y2ZNdFE1NjhVSGhWalljcmF6UkxqTktnLzBpZEYzTlFVRytyR1hoUlBJRjVDVDJjRk1hcUs5OWxoQWp4ekNGUXF0MlQrWlozSzBhd05CQklObjNCdGNvUWpzS045Q2k5a2lSczFqSGNsZUFxSS9iMHc2bFVxYnhOMkJDWDZhOVFEY3RtYUplOWxRUEdXbUlVWUd1Ulk0dmNOL3ZWMThNcTJQQ2tIOTMrMVp3bEFxMVZZVlhOMlVlT0hUc1Q3Sk5Eam92Wmh6SnhTV0RHR0UzN0tFM0kwUm1jZ0R4djNvbGgvS3pJSlJ6T3lQaHN6K3lmNk9VM1BZMlpHLzl1UnBzUHdOckRXeXk3cm1rK3hRclBaOGthZEIxclVmTkxXNVI0OEZnNE11RmNQS281aFZmWWlaanFJVkx3U1pQOHhaam1JWDBGaFIyRHZyK1gyOHMzc2ZoWFJXU1lBVXhCREZ5dHpiM3hsNzEiLCJtYWMiOiI4MDQ4ZjQ1YTg5NDI1NzUxOWQwOWNjODM4ODU2NmQ1ZDhkNmEyMTg4MjE2NzZlNTUxMmI3MzMxOTYxMzA4NDJhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:21:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRzV0RGcmZtdmtNRGdOVlRGaHVITlE9PSIsInZhbHVlIjoiM0N3bjdxV3MrV25qOVJaeG10dUU2WVY0Uk9hN3hPVnY5Zm9BV3dTdVZVeEhVQSt1a1g4TUNvUnpHWmxOVloyNTZKbmhTY0pXb1FiaEtMQmhUY0plYWZWM20vOXFTVXFaeTFHTGVnWXRyS2tYcGxLeWNPWG5vSXl2a1NGMVpNSFRiKzRZMWFKTTlaYzNJazdwNVQ5blE2UTR0MDl5RFNGaUdXQUFTbk1ybmdTcytrVzJBbG4xWi9mbTBNNHY5ZVZvYzlveHFDZFJGYloxVlJyVE9MNWREZldQQ3NNc1BnVUJoNFNTSDBtQ0FhcmRUSkF6alJmaHV2alVLejBTdytFU3RWV043cTFpOUVsdWlmUXZUYk8vY2IrTTBkdEpNT3M1WFU1WVVQYnV6T3BxZC9JUjRwRnlxOVJGTkpZeXd5UVY4b3FpZit1VllQKytsbnE1dGR6YXJaRXY1U1FCWkNVYys1cWlxTDlLNHl3R1RVQVFpRjUrM1N1RnBFeTBmcVdnc3pnYmg1cVFuazB3MWpJV0dSUXVQZWFuOUp2TkdGUkJSN1d2TThYTFRFWm5zVjZBcWdXN0U5Q2FZM2Y5aDY1bWVaS0doOXJkZWxSb0xJUmRneEtlTkNGZlo1U25hZGpUdUEvUFNVMWV0aCsxTC8vLzFUNkV6YTRTTHdWUjdnOUsiLCJtYWMiOiIwOWQ4NWZmODZjNjdlMTQ0NzFiODZhNDg5MjQ5ZjQ1NDkwMWU0OGM2NDNhYTg3OTM3OTJjYmQ3ZTY5MGI1ZWEyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:21:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603458404\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-34162606 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C6Aql5Bn7BRHxHdJ9ts43aJAJGp52XSet04pj843</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34162606\", {\"maxDepth\":0})</script>\n"}}