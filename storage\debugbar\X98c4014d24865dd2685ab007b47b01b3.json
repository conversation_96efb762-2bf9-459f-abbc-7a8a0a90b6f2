{"__meta": {"id": "X98c4014d24865dd2685ab007b47b01b3", "datetime": "2025-08-02 10:32:45", "utime": **********.710573, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130764.469946, "end": **********.710601, "duration": 1.2406551837921143, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1754130764.469946, "relative_start": 0, "end": **********.634709, "relative_end": **********.634709, "duration": 1.1647629737854004, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.634728, "relative_start": 1.****************, "end": **********.710604, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "75.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JCMWeV0OAgidGhyKj9jBvtaNpKRE8NvO4i6GwMbL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1276137978 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1276137978\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-924040866 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-924040866\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1512836237 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1512836237\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2019186813 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019186813\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1037262182 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1037262182\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1293492731 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:32:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpEbjJHdmhJNHQ5MUNmSitkRExBcHc9PSIsInZhbHVlIjoiQlUwRnhpdWl5V2krN0JxYjVodGtFS3VIYzZJUmpTOEYvUE13c015UVpxRlZNVElLQlZYOGs2aWt0empXcmI0Rk5wRS96MVE2ZHpkQU1laG9DRWQrWkppUUVQdHIwcGVnR0FSOUFRT1JyYmdnN0NBWHRLcEt6WG9wTjNsZ1FTWlpaWXpqL25hc1kzVlJQVTNqYkthS2tpSU0yeXh4UHhmaGxjRThaZllVTjltTStTUmQ2RHJjRGZQUlZTL3ZzMjhzYVVBampKaDV4VzFwS0FJWGFua1JYalkyeGFpbnFNZHpJV21YeVBIL3Njb0xrcmdjOFZpVGUwK3NSWjRDOWhjeDdoaEFNT0MxT0Q4U0pCamhEYzdBNlJXNWFZQ2hTMkRqd1IyUno3NW1uZU1PMEdNMnU4UFIwMy83cURzWWhMUitlNmpyUVJFVk5ERVZseXY1U1puc0NvbVJBRXgrRGpiWjBxVm9KZTh4RjhsNUxSazdzRU5FbFdtb0lmVFZwTmVHZ05GNHpjR081RkVocVgyTXh6T0JYSThLM0hNOFR6Z0o3MjV6M2tMV2N6QW1ma3dib3ZVNGtxMlZDdy9zV2xCMFB5aDl0ZFlVVnhFWFlObW5Ec243NGR6TUhBek0yWWU3RUNXdWIrS2ZJTXdwekxDQ0JHYmdpc1NKKzltQ2g1K2IiLCJtYWMiOiIwYjcwYzM5OGZkZGEyZDZjMWFkODE1ZTllNGE3OTEwOTk4OWE0MTg0YjY2NWYxMzI3ZWY2MjMyZWQ5OThiNGIxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:32:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjltRmRxOE9YSE9WcTN2bmFtVmVPK0E9PSIsInZhbHVlIjoidng0TTIySWMwbXZWdEErams0UkNFODVZYS9HeVl4Z3JOTS9nZElzY29LSnVmOHNUQmFyalBFRWZZS3QyMWJCdlM1WUUvWTlmQWdrKzJIMitiU0xmUXh6NGZ2cnFuTlpLS3hDTXdBU2ZwODhPRWpDZTc4VDZCdDVRTVpiZWJLdXVwTktzZnR4ejVPNk9LUUtDZnlTblpPSWVJVEZqNXFxcEpKb1IxdXovWUNWUlhZdC9oRm1iYTVTM091TEQ1cm9wR2dFVm80Y0hjMGo5OEtXWlJMbldNU0F4T2Y5ZTJ3cHplcUNNU3R3dHVDaHBPQmo4M2tMYmVidkxCclVaQ1dpUjhHcjdFTGRxdXpkN3JTdWlDMzJYMkUwRTRnVUVjemFwd3F6M296L1ErWGxwTk9kTllFL05Ld25CK3A0YTBiWDlCSU82bzVqUnVqSkVEVVJMUW4xSWw0aWJTeXpBUW8yTU96NHk3RTc5STF2WkU1MkthMkEwcE9ROFA5eC8rOXRPY2xaY0ljNXhpNDRCbzYzdlNmSzAwbWEvazFqYlhacU5BN0w5NmJ6QkptT0pFTmlUVVFJMVNGVzdtSzZYYWZUb1RCS3dSRXNiRUxvYW9VbUt0SzJHU0p6M0hDcWY1MnNSZ2FkYk4wTmNkWFJXWUhPSHIrSlltcWJRdnR0c01MTnkiLCJtYWMiOiI2MzhlY2QzZWI2NzhiMDkzOGMxODU0NWE1Y2I4ZDQzOGU0Y2IwMGYyMjBmZTFjY2FiZjgwNmQ3YTU2ZDFkMWRkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:32:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpEbjJHdmhJNHQ5MUNmSitkRExBcHc9PSIsInZhbHVlIjoiQlUwRnhpdWl5V2krN0JxYjVodGtFS3VIYzZJUmpTOEYvUE13c015UVpxRlZNVElLQlZYOGs2aWt0empXcmI0Rk5wRS96MVE2ZHpkQU1laG9DRWQrWkppUUVQdHIwcGVnR0FSOUFRT1JyYmdnN0NBWHRLcEt6WG9wTjNsZ1FTWlpaWXpqL25hc1kzVlJQVTNqYkthS2tpSU0yeXh4UHhmaGxjRThaZllVTjltTStTUmQ2RHJjRGZQUlZTL3ZzMjhzYVVBampKaDV4VzFwS0FJWGFua1JYalkyeGFpbnFNZHpJV21YeVBIL3Njb0xrcmdjOFZpVGUwK3NSWjRDOWhjeDdoaEFNT0MxT0Q4U0pCamhEYzdBNlJXNWFZQ2hTMkRqd1IyUno3NW1uZU1PMEdNMnU4UFIwMy83cURzWWhMUitlNmpyUVJFVk5ERVZseXY1U1puc0NvbVJBRXgrRGpiWjBxVm9KZTh4RjhsNUxSazdzRU5FbFdtb0lmVFZwTmVHZ05GNHpjR081RkVocVgyTXh6T0JYSThLM0hNOFR6Z0o3MjV6M2tMV2N6QW1ma3dib3ZVNGtxMlZDdy9zV2xCMFB5aDl0ZFlVVnhFWFlObW5Ec243NGR6TUhBek0yWWU3RUNXdWIrS2ZJTXdwekxDQ0JHYmdpc1NKKzltQ2g1K2IiLCJtYWMiOiIwYjcwYzM5OGZkZGEyZDZjMWFkODE1ZTllNGE3OTEwOTk4OWE0MTg0YjY2NWYxMzI3ZWY2MjMyZWQ5OThiNGIxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:32:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjltRmRxOE9YSE9WcTN2bmFtVmVPK0E9PSIsInZhbHVlIjoidng0TTIySWMwbXZWdEErams0UkNFODVZYS9HeVl4Z3JOTS9nZElzY29LSnVmOHNUQmFyalBFRWZZS3QyMWJCdlM1WUUvWTlmQWdrKzJIMitiU0xmUXh6NGZ2cnFuTlpLS3hDTXdBU2ZwODhPRWpDZTc4VDZCdDVRTVpiZWJLdXVwTktzZnR4ejVPNk9LUUtDZnlTblpPSWVJVEZqNXFxcEpKb1IxdXovWUNWUlhZdC9oRm1iYTVTM091TEQ1cm9wR2dFVm80Y0hjMGo5OEtXWlJMbldNU0F4T2Y5ZTJ3cHplcUNNU3R3dHVDaHBPQmo4M2tMYmVidkxCclVaQ1dpUjhHcjdFTGRxdXpkN3JTdWlDMzJYMkUwRTRnVUVjemFwd3F6M296L1ErWGxwTk9kTllFL05Ld25CK3A0YTBiWDlCSU82bzVqUnVqSkVEVVJMUW4xSWw0aWJTeXpBUW8yTU96NHk3RTc5STF2WkU1MkthMkEwcE9ROFA5eC8rOXRPY2xaY0ljNXhpNDRCbzYzdlNmSzAwbWEvazFqYlhacU5BN0w5NmJ6QkptT0pFTmlUVVFJMVNGVzdtSzZYYWZUb1RCS3dSRXNiRUxvYW9VbUt0SzJHU0p6M0hDcWY1MnNSZ2FkYk4wTmNkWFJXWUhPSHIrSlltcWJRdnR0c01MTnkiLCJtYWMiOiI2MzhlY2QzZWI2NzhiMDkzOGMxODU0NWE1Y2I4ZDQzOGU0Y2IwMGYyMjBmZTFjY2FiZjgwNmQ3YTU2ZDFkMWRkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:32:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293492731\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1465497194 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JCMWeV0OAgidGhyKj9jBvtaNpKRE8NvO4i6GwMbL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465497194\", {\"maxDepth\":0})</script>\n"}}