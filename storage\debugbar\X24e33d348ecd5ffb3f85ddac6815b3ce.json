{"__meta": {"id": "X24e33d348ecd5ffb3f85ddac6815b3ce", "datetime": "2025-08-02 09:11:06", "utime": **********.119578, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754125864.124684, "end": **********.11963, "duration": 1.994946002960205, "duration_str": "1.99s", "measures": [{"label": "Booting", "start": 1754125864.124684, "relative_start": 0, "end": 1754125865.99556, "relative_end": 1754125865.99556, "duration": 1.8708758354187012, "duration_str": "1.87s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754125865.995581, "relative_start": 1.***************, "end": **********.119634, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "124ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OJaTmbe0HPQfUDz0Qs1drVJPX6qthq05OyC3jRX4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-822202607 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-822202607\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1637014396 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1637014396\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1613349277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1613349277\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1180643567 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180643567\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1792953413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1792953413\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-539086401 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:11:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNIbFcrUDdlNUN6R3kzbnhEL2tiNXc9PSIsInZhbHVlIjoic3JOV3lUY1RaS2JGYnpRNkxKTnlTMVF0QWJWcEVzVDNwQllmZUZLNlVzSVUzYkxGTW9iOGJOZktJNndkNHNlUnorTHVJNzNGVHh2VkpDTVM5cFZuRUN5dU1CYlpWUzlkYjMxT040MUZFcGNBS3IwT0k2RmpiZWsxTk94d3NTWHdwMzV6NHpwSEIreExZbC90dWxsNnlzTmdSZk5lVm9rQ1BFbmRxWTZJbWliSk1uU044eHV0dmJBNno0Tm5WelFsZDVPTTNjWnMxZzJGcjhGSTduUnI0NS9RSUl1am9zVkE4STMrRFBzcWJnVlMyaGk0NFpLOWNPSkpkM1pQUDJTQWJXVEc2UGd5OTR5bldtZ2ltUzhhOHA4aEQyUjUvWndTWmNCQjBBRjhGaDhWSmRzYzd0aGpJZmU3aGhNMmx0UWFlclJzSW1pVlFsTWl0bmV3WEdSRWt6ZmtaQVAxOXFIT3hpTUN0UjZ2azhvWHc4STd6c2UwVUo5UGl4ei93b0d2blB6a2V5Y0RoMWJicllTais3VVpZajBNRitNMURIWEdYMUphUEtpN25GUUptaVpOSjkyS3dXNlVpVWErNnVDc3F2dmxKQ2lIR3dwZXV6VnEwY0cyNXZXWVdEckR0dzBQclB0OHMwTGhUYmd6RVc3ZHpJNW02bjljVS9qaXMvdW4iLCJtYWMiOiIyZWNkNGRhMTAzMTkxYzA0YjU5NmQ1OWM3YjdhYTViMTljZTNjMzZiNmRhMWEyMjg5OWM4ZTQzYTMwNmEwODE4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:11:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZYV1lvanU5dDcyOTNLYjJBajhQUkE9PSIsInZhbHVlIjoiM1RQZDV6ckw1MytiSkpYbGpHQjRFNm80YWtRNm9zeXN0L2tKVkJHOG9HWVpxaU9iQVIxOE54bUlnclZpeGQvREVITXczV2NFeW5ubDY1OVdFV25Wd24xcGlhSFErTHMrSzR1ckF1L3VzZWQ1WTR4TXo4N2I1QXBKM2xNanF1cnl1VUxxQ3lMOUlTY3h5RTBLbG1tVFZXaFBMUUVSWEV5WEpSdW5EQXFWZjYrU1lkbXZoNEphbDlXU2tac3NURVdCZENMUkpub09jOEowcFRqd0dydlNlbmhMUmE0TG41eVpuaklnbEd6MC83QkFRWER2Y1B6YXZ0MDROZ0lRcWpEVUhPSkhlMTZmN01kd3ZXNExGSkpmQ09ET2pZVnZaT04rV2planlJdjdTbVZJZlBYV0JOZmlIMjNOREFJM3RKN01FZkdHenpHOHZQT1k5Q0ljYmxQZEp3YjRnaHA5R2JzRjhrNjUxQWhQNWFpZmtjVk9aaFpJRHkzQ3FHVVR6anIyL0o1OG5FRXpSM3NqbG5WUG1ZSDNIa3Z5bGlwaFFYUm5KODdhMmpOZzNTbjFLTC84NW0zeWVNNW1kM2V6NWQ0T3pMaDhQbStwRmxwTHh4UFk3a0ZYR0lyMmwrWnFiaDVkU1FqSFZSNEdOaXM4emJuTm9raHE3RlE1SUI0RHg2QysiLCJtYWMiOiIxYTc3MGI0OTJmNzhlMjk0MDQzNTRkODNlMjQ1YzA4OWYxZjNhNTRiMjI4ZWRkNTNjNWQ2YTNhZmQwYjVmMjRhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:11:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNIbFcrUDdlNUN6R3kzbnhEL2tiNXc9PSIsInZhbHVlIjoic3JOV3lUY1RaS2JGYnpRNkxKTnlTMVF0QWJWcEVzVDNwQllmZUZLNlVzSVUzYkxGTW9iOGJOZktJNndkNHNlUnorTHVJNzNGVHh2VkpDTVM5cFZuRUN5dU1CYlpWUzlkYjMxT040MUZFcGNBS3IwT0k2RmpiZWsxTk94d3NTWHdwMzV6NHpwSEIreExZbC90dWxsNnlzTmdSZk5lVm9rQ1BFbmRxWTZJbWliSk1uU044eHV0dmJBNno0Tm5WelFsZDVPTTNjWnMxZzJGcjhGSTduUnI0NS9RSUl1am9zVkE4STMrRFBzcWJnVlMyaGk0NFpLOWNPSkpkM1pQUDJTQWJXVEc2UGd5OTR5bldtZ2ltUzhhOHA4aEQyUjUvWndTWmNCQjBBRjhGaDhWSmRzYzd0aGpJZmU3aGhNMmx0UWFlclJzSW1pVlFsTWl0bmV3WEdSRWt6ZmtaQVAxOXFIT3hpTUN0UjZ2azhvWHc4STd6c2UwVUo5UGl4ei93b0d2blB6a2V5Y0RoMWJicllTais3VVpZajBNRitNMURIWEdYMUphUEtpN25GUUptaVpOSjkyS3dXNlVpVWErNnVDc3F2dmxKQ2lIR3dwZXV6VnEwY0cyNXZXWVdEckR0dzBQclB0OHMwTGhUYmd6RVc3ZHpJNW02bjljVS9qaXMvdW4iLCJtYWMiOiIyZWNkNGRhMTAzMTkxYzA0YjU5NmQ1OWM3YjdhYTViMTljZTNjMzZiNmRhMWEyMjg5OWM4ZTQzYTMwNmEwODE4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:11:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZYV1lvanU5dDcyOTNLYjJBajhQUkE9PSIsInZhbHVlIjoiM1RQZDV6ckw1MytiSkpYbGpHQjRFNm80YWtRNm9zeXN0L2tKVkJHOG9HWVpxaU9iQVIxOE54bUlnclZpeGQvREVITXczV2NFeW5ubDY1OVdFV25Wd24xcGlhSFErTHMrSzR1ckF1L3VzZWQ1WTR4TXo4N2I1QXBKM2xNanF1cnl1VUxxQ3lMOUlTY3h5RTBLbG1tVFZXaFBMUUVSWEV5WEpSdW5EQXFWZjYrU1lkbXZoNEphbDlXU2tac3NURVdCZENMUkpub09jOEowcFRqd0dydlNlbmhMUmE0TG41eVpuaklnbEd6MC83QkFRWER2Y1B6YXZ0MDROZ0lRcWpEVUhPSkhlMTZmN01kd3ZXNExGSkpmQ09ET2pZVnZaT04rV2planlJdjdTbVZJZlBYV0JOZmlIMjNOREFJM3RKN01FZkdHenpHOHZQT1k5Q0ljYmxQZEp3YjRnaHA5R2JzRjhrNjUxQWhQNWFpZmtjVk9aaFpJRHkzQ3FHVVR6anIyL0o1OG5FRXpSM3NqbG5WUG1ZSDNIa3Z5bGlwaFFYUm5KODdhMmpOZzNTbjFLTC84NW0zeWVNNW1kM2V6NWQ0T3pMaDhQbStwRmxwTHh4UFk3a0ZYR0lyMmwrWnFiaDVkU1FqSFZSNEdOaXM4emJuTm9raHE3RlE1SUI0RHg2QysiLCJtYWMiOiIxYTc3MGI0OTJmNzhlMjk0MDQzNTRkODNlMjQ1YzA4OWYxZjNhNTRiMjI4ZWRkNTNjNWQ2YTNhZmQwYjVmMjRhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:11:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539086401\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-609195669 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OJaTmbe0HPQfUDz0Qs1drVJPX6qthq05OyC3jRX4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609195669\", {\"maxDepth\":0})</script>\n"}}