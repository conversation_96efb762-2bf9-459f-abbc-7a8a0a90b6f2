{"__meta": {"id": "X18d38cd8ca014053c654df10bbba79ca", "datetime": "2025-08-02 09:54:07", "utime": **********.195697, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754128446.389053, "end": **********.195726, "duration": 0.8066728115081787, "duration_str": "807ms", "measures": [{"label": "Booting", "start": 1754128446.389053, "relative_start": 0, "end": **********.116326, "relative_end": **********.116326, "duration": 0.7272729873657227, "duration_str": "727ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.116346, "relative_start": 0.****************, "end": **********.195729, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "79.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nVAfpWggIRdumztkyUDThcaik4GntgnySn69UKDW", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2119628770 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2119628770\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1457815938 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1457815938\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2027705179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2027705179\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1075769829 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075769829\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-893970843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-893970843\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2078819545 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:54:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imo1OVRWQitJdlhYMk1TblBMYUtPN0E9PSIsInZhbHVlIjoiVXZYWDJuY0RkUiszcUdGRms5MWIvSFB6cStJUHBxMlp4clBDaVZDWTlPMjlMNy9iL1d5SjgzMUVVV3A3bUt6OERVdXZ0bGZ5VTFOWHhvZ3pzQjg2bXRJa0tGb1N6SVJueHJXTk9yVmw0anRwbVE0a3J4bTU3N3BraDZ1QmtYOHdNRnkxTVcxa2w4Vys4WDh2akZMZ2hmajNPRCtFMUxwZHAwb0w4VElSQS9QdWFGYTZzVkhZQWpTUzVpVTA2VlpxTHZ1WXdLdHB2dkN5Z3FXelNjOGI2M0k0YUpWbzR3dHFJcEpqZ3VzaklpOXlJMWlhTG1aTkdlYnM2WUJYU3NIR0lVcTZnSjBnZ0FVWkNUdjdwWDh6c3AxRE1nOEU1RS85aTMzS1NmR2hLQ2FkUkZBamZYb1VQclA2TGV3SnpONUQ0MTNRSXp3MHBKSlA1cUt0ZVNWbU5tYmplL0hLS2RBNVZFTW9PV0U0Z0ZvUG4wdWhmTExha2FLd1Q5blpSSFlodlNLdmZieExlUjBEWGlHL0M4TUpDbjk3WGlYb0c0Z1ZFWDlRWmVJdGRueEtDUXc0VndjR1NEN09EMElpQzBzRTZENEsvM0VncUY1RkV3eHpTdGZ0aW5Fc01MYUtaR3dZUDlpSTNXQklZNjhkK2tTYktBOGJpK2VzZk5nblBFZk0iLCJtYWMiOiJhOWY3MGFmNWMzMmQxMzdiOGRhMjkyMmIxM2U2Y2Q3MzFlZTU3NTc4ZmMwMGU4ZWY2N2IwNDY5NWNjNWY1MTdjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:54:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlVMUZnSXc4dytkWW81Y2g0ekIrK3c9PSIsInZhbHVlIjoiTXNEOFA1amZTQUd6Y3ptc3VBMGxrNTMyd1plbjlwL1hnaHcxelpqSzczanowenRwTEVBQ055RlNybmpOUGoxTEdvR0tWTkVwTStIVEVIL3BFWWNOSS94WHlha0E0OWQrb2IxQk40NG0vOXNzczVHcG1zazVLRXZOREFIck04cnFkTU1TYXk5amR2ZTJGeTFjbnV1Tm1jMzVleGlwSkVCeVE1ZFQ4MnhraE1LQTdBd3JzQjlMMWpHQXVyandoeFRnMHpteis5YXo3VkxyVXE3N1p4a1ZrbEFTaXUzM3M3SzZWZytnU1A1WXAzTjRYTGRScVJvQ0w4d3dTT2tIL0l3SmVTZDBjMEpleEpBZVh2RlBKZEdPZVRhbTh0V2Q4OEJWeXRKZ1JIb1dCbEs0ZFo1Sk5mR1ZZMHlVUlUxQ2MvejAxeGhpSERXbVNVNGpDZ21jYXhCbHB1Wk1la0NyYzR4aGEvWWFScVhtSzg2dTFpeWFsMm1adnZqZlpROWNQb0VqbVg0ZEhxZnorMnRTaFFhVkZBMml4VXYzaTA0bHJiNUh1S0gvUTErU0lzdXRZNE9UM1djUnBVMGI3WTNWSGhpUm8rYzQzUWl2aHFmL3lNd1lMU0txcVNYRXRuMHorTGpYRVRKNEhoU1BwTjhVMjlVL21Pc08xSWMrZmJwRnZ6cTYiLCJtYWMiOiJhYjgxYzBlZGQxYTg5YmVjZTMzMzliOGUzOTdjZTlkNGM3YWJlYzA0MjI5NDE0NGYzNTIzZTQ1MWRkZmRlMTljIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:54:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imo1OVRWQitJdlhYMk1TblBMYUtPN0E9PSIsInZhbHVlIjoiVXZYWDJuY0RkUiszcUdGRms5MWIvSFB6cStJUHBxMlp4clBDaVZDWTlPMjlMNy9iL1d5SjgzMUVVV3A3bUt6OERVdXZ0bGZ5VTFOWHhvZ3pzQjg2bXRJa0tGb1N6SVJueHJXTk9yVmw0anRwbVE0a3J4bTU3N3BraDZ1QmtYOHdNRnkxTVcxa2w4Vys4WDh2akZMZ2hmajNPRCtFMUxwZHAwb0w4VElSQS9QdWFGYTZzVkhZQWpTUzVpVTA2VlpxTHZ1WXdLdHB2dkN5Z3FXelNjOGI2M0k0YUpWbzR3dHFJcEpqZ3VzaklpOXlJMWlhTG1aTkdlYnM2WUJYU3NIR0lVcTZnSjBnZ0FVWkNUdjdwWDh6c3AxRE1nOEU1RS85aTMzS1NmR2hLQ2FkUkZBamZYb1VQclA2TGV3SnpONUQ0MTNRSXp3MHBKSlA1cUt0ZVNWbU5tYmplL0hLS2RBNVZFTW9PV0U0Z0ZvUG4wdWhmTExha2FLd1Q5blpSSFlodlNLdmZieExlUjBEWGlHL0M4TUpDbjk3WGlYb0c0Z1ZFWDlRWmVJdGRueEtDUXc0VndjR1NEN09EMElpQzBzRTZENEsvM0VncUY1RkV3eHpTdGZ0aW5Fc01MYUtaR3dZUDlpSTNXQklZNjhkK2tTYktBOGJpK2VzZk5nblBFZk0iLCJtYWMiOiJhOWY3MGFmNWMzMmQxMzdiOGRhMjkyMmIxM2U2Y2Q3MzFlZTU3NTc4ZmMwMGU4ZWY2N2IwNDY5NWNjNWY1MTdjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:54:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlVMUZnSXc4dytkWW81Y2g0ekIrK3c9PSIsInZhbHVlIjoiTXNEOFA1amZTQUd6Y3ptc3VBMGxrNTMyd1plbjlwL1hnaHcxelpqSzczanowenRwTEVBQ055RlNybmpOUGoxTEdvR0tWTkVwTStIVEVIL3BFWWNOSS94WHlha0E0OWQrb2IxQk40NG0vOXNzczVHcG1zazVLRXZOREFIck04cnFkTU1TYXk5amR2ZTJGeTFjbnV1Tm1jMzVleGlwSkVCeVE1ZFQ4MnhraE1LQTdBd3JzQjlMMWpHQXVyandoeFRnMHpteis5YXo3VkxyVXE3N1p4a1ZrbEFTaXUzM3M3SzZWZytnU1A1WXAzTjRYTGRScVJvQ0w4d3dTT2tIL0l3SmVTZDBjMEpleEpBZVh2RlBKZEdPZVRhbTh0V2Q4OEJWeXRKZ1JIb1dCbEs0ZFo1Sk5mR1ZZMHlVUlUxQ2MvejAxeGhpSERXbVNVNGpDZ21jYXhCbHB1Wk1la0NyYzR4aGEvWWFScVhtSzg2dTFpeWFsMm1adnZqZlpROWNQb0VqbVg0ZEhxZnorMnRTaFFhVkZBMml4VXYzaTA0bHJiNUh1S0gvUTErU0lzdXRZNE9UM1djUnBVMGI3WTNWSGhpUm8rYzQzUWl2aHFmL3lNd1lMU0txcVNYRXRuMHorTGpYRVRKNEhoU1BwTjhVMjlVL21Pc08xSWMrZmJwRnZ6cTYiLCJtYWMiOiJhYjgxYzBlZGQxYTg5YmVjZTMzMzliOGUzOTdjZTlkNGM3YWJlYzA0MjI5NDE0NGYzNTIzZTQ1MWRkZmRlMTljIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:54:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078819545\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nVAfpWggIRdumztkyUDThcaik4GntgnySn69UKDW</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}