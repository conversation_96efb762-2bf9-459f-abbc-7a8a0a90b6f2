{"__meta": {"id": "X3a35270a0916fd32ab9ea963b35152cc", "datetime": "2025-08-02 08:38:42", "utime": **********.171503, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[08:38:42] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.134744, "xdebug_link": null, "collector": "log"}, {"message": "[08:38:42] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.14604, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754123919.526568, "end": **********.171559, "duration": 2.644991159439087, "duration_str": "2.64s", "measures": [{"label": "Booting", "start": 1754123919.526568, "relative_start": 0, "end": 1754123921.792254, "relative_end": 1754123921.792254, "duration": 2.26568603515625, "duration_str": "2.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754123921.792283, "relative_start": 2.2657151222229004, "end": **********.171564, "relative_end": 5.0067901611328125e-06, "duration": 0.37928104400634766, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46531320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3045\" onclick=\"\">app/Http/Controllers/LeadController.php:3045-3101</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01698, "accumulated_duration_str": "16.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.032211, "duration": 0.013349999999999999, "duration_str": "13.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 78.622}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.118773, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 78.622, "width_percent": 13.899}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3072}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1358058, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:3072", "source": "app/Http/Controllers/LeadController.php:3072", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3072", "ajax": false, "filename": "LeadController.php", "line": "3072"}, "connection": "radhe_same", "start_percent": 92.521, "width_percent": 7.479}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-651165048 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-651165048\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1513411941 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513411941\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1059194090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1059194090\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1812730585 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkJOV3NzNlZSMFUxeTg2dG56REFKcHc9PSIsInZhbHVlIjoibHc2dEFxRjRsOTRlbWxQS2I3YzBncmlneHUrdmF1RWlxM0k4SWptd1hwMHh5ZlVMWTJEVWNJdnA5bFo2b0N2NTdEb2NyeVJtZXhnVmN6Zk83RTdrSVRWNnJaMnRYNUgycU1ZVUFYVUpEVWVnR2FuNlIxUThHRnI3aHU4UXNUQmNLOG9YUkVsZ2w3UW5XcUtZUEpNQ1o0K3NPQnZ3aXB6SHFvN3BjMXN5aC9aaUtvRmI1a1k4eDRnRy9IWXhDT0JaQUZWL0ovbUtVSW50MzREQmRFNkkrT29iQTY3aUNndkJHVFA3bVNKUGxEUlo1VUNTcEpMR1NPSnNYaUdKSEZmK1pHUkFiWGZOdkVpRUdpNmNQTHJwc1RIOURHRXhNSSt0VENnOXNBNG82REdWM3NIQVBwMXBBNFFRQ2pCZ09HNVRYZ1pJVVJEbHB1dzNBMHdzSzZGUXNhaU4zTTk4cTlQZlFCU2VobXpDdWZGb1U4ZWNqQW9SZ2x6b0pSdlBwYjV5b3ZkOGxGbE5yQnlteUhOaEhGRks3U0x0Q3ZsVjJTV3hiWWZRcjVMWkJ1ZHZ1SHdkTTRDMDI0azl4L0dPZlRtK1E1MnNQQm1FejYxWGo3TGVMTldVNEpsWU9iN3pqOEFVRFFEVnB0WFEzdVJRQWMrYUg0NG1Ga1l3Z2w1YTVtbUoiLCJtYWMiOiIzZTZjOTQwMGI3YjliYmQzNzZiZWFhMGVlNDg4NTVlOWQ2YThmNDY0NGE2ZTY2MzQ3ZWZhYTI2YmJmOGRiZTIzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik5ZZUcwZFpZN2JaNXBMOWhCRkhFeGc9PSIsInZhbHVlIjoiSTNVaWw5WitDekVMbzJEV1VTYlp1U1p6WHZkT2U0WmVyTHlNd0pLdUtLOXIyR3VqS0E4ZW0yYklPUmYzRGxZZ1FoeTZHVmU5QTVPNzh3b296TFdZSTI5bFNLZFJNRUUzZ3RYaWEvakxzZlI1OHpSZHlwMkl6bEcwM2NFNm80S3pZZnRSWEtlcUdBZllONVpCSkc4L3NtWVpmUDB4NSs3Um54MWRFUlRWTnhQZGlJblZSZThFSldaNk5qOGJzQVZ1Y004SHpUWlRUK3FzLzFrb3E3aHV2UTJSU05vMW1ETUY3MVdYMXAzbGlWMDcyVEQwcE1qV3JNd1BrQ0xVeDdDbHkwcTkwU1NWNC9CTEhueUJlc1ZPamZWcjRWeU5KODdDdFhITFVBRUFIZll1R09WTVB3OUlIbzYwR0hVd0tVSFFWMnZwam1jRkdkTWdhd3UvRGxBZktCR2x1d3B5VjBIM3F0clVWSVdiRUQ4TEZrcTlRVkdJV1FQM1BxTzBDU0U3Y1krb1l0MDNYVTBoZ3MwNjBzUVFMbVo5Mmo5b0RiWG54Mk5STGdqclZyVHRacDNXZTFZYjdQR080U1dBdUdCdklYK29IN2U0VEFkYzk3dlBaTldmeHNITXkrR1FHQ0FJV1pDUE1NNnhTcGhVQjhTTjZuNHdOd1Z4Uk1tc1VLelAiLCJtYWMiOiI3NWY1MzFkMmQxNGEyYzM5ZjAyZDkyN2ZmMDcxYTMwN2MzZTRkOTI3NzRhZjczN2MwYmQxYzQ1ZGQwOGFhN2FkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812730585\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-433730722 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433730722\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1050928588 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:38:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllrZFhZVDNBV3preUdzYTFsTXBPOVE9PSIsInZhbHVlIjoiUVpGam1idHpzMmR4Yy9wTER3YmNCWWgxYmNkaTVTMExJanlmRzQzc2FVRmVNWmlpakpibFE4T0Nicm5SZjlZTU43Y2I0NkVPbHIxUE9VMmRVL1ZkUGR1Q0FKbFk2RE43ZGtvdDlQOUVXU2Z2dVpqb1Rvb1pXQy8vUDE0RDJPb1YyOXZmZklaQlBYRENvTUNZeHZONXV0NFltY1lEMTdvTlZkU2dEdEFZWnpSRFU5TWlKS1hJSW96dlVmeVBVK1pWRTI0ckZsOVpaTzJSN2RuVklWTHpna1hrUXBHSzB2ZGxBMkpBVnpqa0MySUh1NGcrNmQxNkNIMWNNSHRIMHVaeUsxMmFKbnAyS0FYZDJ1MFBzYWtSb3JaeG5mR2pscTc2TWxOTE1BMTJCN0REUlB2ZTRkSFJIZklXak93cnU3UHFQOEo5a2E0UThoUkdIb0Y0eklaaHZkZkJad2czNWJhNDFicnVtZ2Q0V2k4OEh6aGkxU3prdlF4TVE2SjR6MWx1dDFXd2hSNWxDT2Zva1BTeW55WGZGUmc5MzBKbHpZZEV0SEZ6TE1oVTZ0R0hvZlZzYnUydnE4bUNxd2pRSDlOSjlsZkcxbWtpZWZtbmNtenpoMW9YWWtYaUpHSklJRnRUaHltNkJMYkFuQ2xhSkxxSEU2eUtYaFRCUXhJNldQT28iLCJtYWMiOiJiMzVmMDAwNGUzYzc0ZWE3OTdjNGI1NDI5OWE4OTlhMWUyYjU0OWQ3OTA0ODU5ZDM2YTFmYzM5YmVmMWUzMzkyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:38:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii9uVE1LdHhoVyttU3V2UFBLb2hMdmc9PSIsInZhbHVlIjoiOHUxdllOQTVKQnROTFZoSCs2QjM0UElwQVFUaWhSSE5MWXIvcFZ6eWYwUDk0NjZxUXlxSTJ4Y2RUZSt6R3plMFhiNWVWaGtyWTd6MWY4dXJ1SS9iM2tnWnFtUys2VEk5b1VUNEd5WjU5ZXdGNXpOdDZYSGpodHZPcnREUWhOWHB1ZFJjNm1DbUd3N1RFeFRTOElTa2paclJRb2tEaGVVWjVEUDYyaVNJRUEwK3cxLzVmZlFSTUIzT2VnVEQ5RWV2ZGtOS2wvM0diMXZOU05mc1JwamVDbHMrQS8zMi9wV2FwZysvVnZkTWlUTkVLYlBLZXg3Tm1oK0VHT1RLMDNrcXU4N1I1bFRFd3VmWXFMVWVOdWJST2h1N3pkN1IyM0FUcmY0MWRlbzY5V2NxeEU0bDBYbjQ2NlBJazJ0c1RpUUorNzZuRCs4b21NaWtyWWVSZThBMHBDWWROSjAzZjJ3U0JhbW9vUktud1ZJMnFSVTVlUVlRck5ILzJ5R2xxdUl3NXdGRkhWOTMrUkhMNHEzaFhBWm5FS3lGc3puZUdRMkt1ZjlsZUt3VUdPaHdUTUpTSDFwbk04WmxobmFYNmJIUlBMRUtMNVVTK1UvZU52WnluMU5lWm5WNFZ2N25RTWVZVlhMTXN5b3ViSEdHOWNtZ01KOGphZmJpcmc3OWQ4cFIiLCJtYWMiOiIxNGJhZmU0M2NiYjM5NmE0MTAzOTcyMjNlZTQ0OTAwNTI1NjQ5NWU3YmRhZDdlMDk2YzY0NGNiNzM3MTIyOTdmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:38:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllrZFhZVDNBV3preUdzYTFsTXBPOVE9PSIsInZhbHVlIjoiUVpGam1idHpzMmR4Yy9wTER3YmNCWWgxYmNkaTVTMExJanlmRzQzc2FVRmVNWmlpakpibFE4T0Nicm5SZjlZTU43Y2I0NkVPbHIxUE9VMmRVL1ZkUGR1Q0FKbFk2RE43ZGtvdDlQOUVXU2Z2dVpqb1Rvb1pXQy8vUDE0RDJPb1YyOXZmZklaQlBYRENvTUNZeHZONXV0NFltY1lEMTdvTlZkU2dEdEFZWnpSRFU5TWlKS1hJSW96dlVmeVBVK1pWRTI0ckZsOVpaTzJSN2RuVklWTHpna1hrUXBHSzB2ZGxBMkpBVnpqa0MySUh1NGcrNmQxNkNIMWNNSHRIMHVaeUsxMmFKbnAyS0FYZDJ1MFBzYWtSb3JaeG5mR2pscTc2TWxOTE1BMTJCN0REUlB2ZTRkSFJIZklXak93cnU3UHFQOEo5a2E0UThoUkdIb0Y0eklaaHZkZkJad2czNWJhNDFicnVtZ2Q0V2k4OEh6aGkxU3prdlF4TVE2SjR6MWx1dDFXd2hSNWxDT2Zva1BTeW55WGZGUmc5MzBKbHpZZEV0SEZ6TE1oVTZ0R0hvZlZzYnUydnE4bUNxd2pRSDlOSjlsZkcxbWtpZWZtbmNtenpoMW9YWWtYaUpHSklJRnRUaHltNkJMYkFuQ2xhSkxxSEU2eUtYaFRCUXhJNldQT28iLCJtYWMiOiJiMzVmMDAwNGUzYzc0ZWE3OTdjNGI1NDI5OWE4OTlhMWUyYjU0OWQ3OTA0ODU5ZDM2YTFmYzM5YmVmMWUzMzkyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:38:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii9uVE1LdHhoVyttU3V2UFBLb2hMdmc9PSIsInZhbHVlIjoiOHUxdllOQTVKQnROTFZoSCs2QjM0UElwQVFUaWhSSE5MWXIvcFZ6eWYwUDk0NjZxUXlxSTJ4Y2RUZSt6R3plMFhiNWVWaGtyWTd6MWY4dXJ1SS9iM2tnWnFtUys2VEk5b1VUNEd5WjU5ZXdGNXpOdDZYSGpodHZPcnREUWhOWHB1ZFJjNm1DbUd3N1RFeFRTOElTa2paclJRb2tEaGVVWjVEUDYyaVNJRUEwK3cxLzVmZlFSTUIzT2VnVEQ5RWV2ZGtOS2wvM0diMXZOU05mc1JwamVDbHMrQS8zMi9wV2FwZysvVnZkTWlUTkVLYlBLZXg3Tm1oK0VHT1RLMDNrcXU4N1I1bFRFd3VmWXFMVWVOdWJST2h1N3pkN1IyM0FUcmY0MWRlbzY5V2NxeEU0bDBYbjQ2NlBJazJ0c1RpUUorNzZuRCs4b21NaWtyWWVSZThBMHBDWWROSjAzZjJ3U0JhbW9vUktud1ZJMnFSVTVlUVlRck5ILzJ5R2xxdUl3NXdGRkhWOTMrUkhMNHEzaFhBWm5FS3lGc3puZUdRMkt1ZjlsZUt3VUdPaHdUTUpTSDFwbk04WmxobmFYNmJIUlBMRUtMNVVTK1UvZU52WnluMU5lWm5WNFZ2N25RTWVZVlhMTXN5b3ViSEdHOWNtZ01KOGphZmJpcmc3OWQ4cFIiLCJtYWMiOiIxNGJhZmU0M2NiYjM5NmE0MTAzOTcyMjNlZTQ0OTAwNTI1NjQ5NWU3YmRhZDdlMDk2YzY0NGNiNzM3MTIyOTdmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:38:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050928588\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1214098334 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214098334\", {\"maxDepth\":0})</script>\n"}}