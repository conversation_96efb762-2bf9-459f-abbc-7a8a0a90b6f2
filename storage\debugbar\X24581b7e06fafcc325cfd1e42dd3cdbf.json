{"__meta": {"id": "X24581b7e06fafcc325cfd1e42dd3cdbf", "datetime": "2025-08-02 09:28:04", "utime": **********.335662, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754126883.441248, "end": **********.335692, "duration": 0.8944439888000488, "duration_str": "894ms", "measures": [{"label": "Booting", "start": 1754126883.441248, "relative_start": 0, "end": **********.233617, "relative_end": **********.233617, "duration": 0.7923691272735596, "duration_str": "792ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.233656, "relative_start": 0.****************, "end": **********.335694, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ln9EyMZ23iwxJfNjY8sJYtXSI6hArt9Ue5b9dEgm", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1645250445 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1645250445\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-3663384 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-3663384\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1014637156 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1014637156\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1280654633 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280654633\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-154756841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-154756841\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1316496655 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:28:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJyT1c2WEtTMjlZY0NETTJ5a3ZiVkE9PSIsInZhbHVlIjoiK1FReS9wWGNqZ0RZOXQzc2E3ZzlpT2IyeFpTQzZESSs5UWhvdUZpK2xsckRoSFJNMnBUak0vOXE0L2pYdG52STZDNjEvSHZoMXNRaHNwcXhxNzh0UGlrbjIwVmIrODZScGhPQ3VNcXpkaTdBa01xNHNESlBKR21GWlZ4M1VNVWhjTWhLVE9RNWFvWFdVMWRvQUVQOE1oR1FtNytJV1Y5ZVo3WHNZYXdZekgrSm1Cb2ZKTnF2OURHdUxHMlRuQzNOcVhxbThFNTBDSkl1RVlBbWZXM3U0WlFaRkc1Q3NYMjMvS1dsTXlDZWF2U1lEVjZvajlSZXFXU21jeUJjYVhqaU5lRUVqeHFwMjNpK3NLc0pmeGVCZjkxQVMxQmJwdEh1WjdMamd5VjRlOWN4YXFlQWJUVjlydGxrMFgwZWNXTjBMbk1YdHhFMUNSdDdlWHh6cnVwbWhaSmdtU1JIcjI3bkd5ZmtacHNaQUxyTGNFQTJzbXZTbjFQRlBKQ3FwQWVRR0hmM1FiRnFEelFZNHR2cUF3NGU3YWVsaWNpZFRpSHJxRlVaaEZRcGlkZi92UzEvelVVRzZyUnNjSEZXb0dDcXZIUFpET2RCeE9qTDdTYkJXNWVYZGgxZUJNZ3cvWDZXL1M1YWhxMDJzaklZbmdvTHdBK1k5M1NOcllmTzVJVDEiLCJtYWMiOiJiMWMzM2VlNzE2ZjIxODMzM2QxMzFhYmIwYzc1NTEwNjgwZjhjOTUwNzEzNThjNWRjMjM2MGQ3ZjczM2RjMzY2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:28:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlF6RHMwY2F3aWkxeTNFRG9xZkE1aUE9PSIsInZhbHVlIjoiRU5nV01CM3MwWTNzZUQraTRRUVdsTWE4RXZoMmc5UlNPWCtGZjVFaXFOY0tTTitJbTFFN1ZSQmp4R0E5WmVOejN4cWszS1ZYSG9tZzVsR2dLV0pYOGdtcXNaRjZrWXBHWDZCcGRYaDNFdzZEenNTelVSOStyT3FhS2ZtQmdOZ3dBdExXT3RkZi9GUTFzaG5xdDRhTE41S3lEL1M2UEJZaFYvOWFZRTNCUUJNaDdhdTlmZTVWOTI2Q0xEQlN4TDdYSThhaFV6VW9HcE9LME5pMStwSnk1Q1VqcnU5Y1lhSk1PUExqZDJhenp0MFRTalNWWHlRd0lnZmVQeTMrRTRQY0o0aVo5Z0FxbnNQQUcwRm9uNjJXcDNrM1Btbm9wdW1ObEpLVDd4TzIzWXNhNnR2akdDNXVrVEx4aGhld3hpZFdxdDBBa1pjZENVQjQrQlNNYWdvUzltNzA0SXJ3NkhRWE5OK2ZMcmFIL1F6ZS85TmpLbEluRHpoRCt5d3dOMWxObkxNcExZYVJVNzU5dlhmWlFWOWxIUkpyM3d5Vlk5RVVFWk5BTW9MWGxOcjYvU25tV0hXejY4Z3ZNTW50a1Noa0FwTlZrbExZS01xaWYwcGZ3cDZoM2FRbHFlcEllTlN1cm5PZG9mWmZXRkZWbUhzaWtNdXc3L0Z3VnB2Ym1pbEciLCJtYWMiOiJhM2YwYTVkZWY0MTNkOTAxZDhhOTZlYmRjYWU1OTkxYWFiN2FiOTg1YzhhNWU0ZTVkOTc2MjMwYzhjNjVjYjMxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:28:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJyT1c2WEtTMjlZY0NETTJ5a3ZiVkE9PSIsInZhbHVlIjoiK1FReS9wWGNqZ0RZOXQzc2E3ZzlpT2IyeFpTQzZESSs5UWhvdUZpK2xsckRoSFJNMnBUak0vOXE0L2pYdG52STZDNjEvSHZoMXNRaHNwcXhxNzh0UGlrbjIwVmIrODZScGhPQ3VNcXpkaTdBa01xNHNESlBKR21GWlZ4M1VNVWhjTWhLVE9RNWFvWFdVMWRvQUVQOE1oR1FtNytJV1Y5ZVo3WHNZYXdZekgrSm1Cb2ZKTnF2OURHdUxHMlRuQzNOcVhxbThFNTBDSkl1RVlBbWZXM3U0WlFaRkc1Q3NYMjMvS1dsTXlDZWF2U1lEVjZvajlSZXFXU21jeUJjYVhqaU5lRUVqeHFwMjNpK3NLc0pmeGVCZjkxQVMxQmJwdEh1WjdMamd5VjRlOWN4YXFlQWJUVjlydGxrMFgwZWNXTjBMbk1YdHhFMUNSdDdlWHh6cnVwbWhaSmdtU1JIcjI3bkd5ZmtacHNaQUxyTGNFQTJzbXZTbjFQRlBKQ3FwQWVRR0hmM1FiRnFEelFZNHR2cUF3NGU3YWVsaWNpZFRpSHJxRlVaaEZRcGlkZi92UzEvelVVRzZyUnNjSEZXb0dDcXZIUFpET2RCeE9qTDdTYkJXNWVYZGgxZUJNZ3cvWDZXL1M1YWhxMDJzaklZbmdvTHdBK1k5M1NOcllmTzVJVDEiLCJtYWMiOiJiMWMzM2VlNzE2ZjIxODMzM2QxMzFhYmIwYzc1NTEwNjgwZjhjOTUwNzEzNThjNWRjMjM2MGQ3ZjczM2RjMzY2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:28:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlF6RHMwY2F3aWkxeTNFRG9xZkE1aUE9PSIsInZhbHVlIjoiRU5nV01CM3MwWTNzZUQraTRRUVdsTWE4RXZoMmc5UlNPWCtGZjVFaXFOY0tTTitJbTFFN1ZSQmp4R0E5WmVOejN4cWszS1ZYSG9tZzVsR2dLV0pYOGdtcXNaRjZrWXBHWDZCcGRYaDNFdzZEenNTelVSOStyT3FhS2ZtQmdOZ3dBdExXT3RkZi9GUTFzaG5xdDRhTE41S3lEL1M2UEJZaFYvOWFZRTNCUUJNaDdhdTlmZTVWOTI2Q0xEQlN4TDdYSThhaFV6VW9HcE9LME5pMStwSnk1Q1VqcnU5Y1lhSk1PUExqZDJhenp0MFRTalNWWHlRd0lnZmVQeTMrRTRQY0o0aVo5Z0FxbnNQQUcwRm9uNjJXcDNrM1Btbm9wdW1ObEpLVDd4TzIzWXNhNnR2akdDNXVrVEx4aGhld3hpZFdxdDBBa1pjZENVQjQrQlNNYWdvUzltNzA0SXJ3NkhRWE5OK2ZMcmFIL1F6ZS85TmpLbEluRHpoRCt5d3dOMWxObkxNcExZYVJVNzU5dlhmWlFWOWxIUkpyM3d5Vlk5RVVFWk5BTW9MWGxOcjYvU25tV0hXejY4Z3ZNTW50a1Noa0FwTlZrbExZS01xaWYwcGZ3cDZoM2FRbHFlcEllTlN1cm5PZG9mWmZXRkZWbUhzaWtNdXc3L0Z3VnB2Ym1pbEciLCJtYWMiOiJhM2YwYTVkZWY0MTNkOTAxZDhhOTZlYmRjYWU1OTkxYWFiN2FiOTg1YzhhNWU0ZTVkOTc2MjMwYzhjNjVjYjMxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:28:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316496655\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1916450960 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ln9EyMZ23iwxJfNjY8sJYtXSI6hArt9Ue5b9dEgm</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916450960\", {\"maxDepth\":0})</script>\n"}}