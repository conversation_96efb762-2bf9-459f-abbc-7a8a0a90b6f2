{"__meta": {"id": "X9d7391a5d36449f780417d2309f21b06", "datetime": "2025-08-02 10:26:28", "utime": **********.286059, "method": "POST", "uri": "/leads", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[10:26:27] LOG.info: Lead creation started {\n    \"user_id\": 79,\n    \"request_data\": {\n        \"_token\": \"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL\",\n        \"name\": \"mama bar\",\n        \"email\": \"mamabari\",\n        \"phone\": \"************\",\n        \"date_of_birth\": null,\n        \"contact_type\": \"Lead\",\n        \"tags\": [\n            \"95\",\n            \"96\",\n            \"98\",\n            \"97\",\n            \"94\",\n            \"93\",\n            \"88\",\n            \"92\",\n            \"89\"\n        ],\n        \"postal_code\": \"734429\",\n        \"city\": \"Siliguri\",\n        \"state\": \"West Bengal\",\n        \"country\": \"India\",\n        \"business_name\": \"Smart Internz\",\n        \"business_gst\": \"Smart Internz\",\n        \"business_state\": \"West Bengal\",\n        \"business_postal_code\": \"734429\",\n        \"business_address\": \"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\",\n        \"user_id\": \"79\",\n        \"subject\": \"New Contact\",\n        \"pipeline_id\": null,\n        \"stage_id\": null,\n        \"dnd_settings\": \"{\\\"all\\\":false,\\\"emails\\\":false,\\\"whatsapp\\\":false,\\\"sms\\\":false,\\\"calls\\\":false}\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.672514, "xdebug_link": null, "collector": "log"}, {"message": "[10:26:28] LOG.error: Lead creation validation failed {\n    \"errors\": {\n        \"email\": [\n            \"The email must be a valid email address.\"\n        ]\n    },\n    \"request_data\": {\n        \"name\": \"mama bar\",\n        \"email\": \"mamabari\",\n        \"phone\": \"************\",\n        \"date_of_birth\": null,\n        \"contact_type\": \"Lead\",\n        \"tags\": [\n            \"95\",\n            \"96\",\n            \"98\",\n            \"97\",\n            \"94\",\n            \"93\",\n            \"88\",\n            \"92\",\n            \"89\"\n        ],\n        \"postal_code\": \"734429\",\n        \"city\": \"Siliguri\",\n        \"state\": \"West Bengal\",\n        \"country\": \"India\",\n        \"business_name\": \"Smart Internz\",\n        \"business_gst\": \"Smart Internz\",\n        \"business_state\": \"West Bengal\",\n        \"business_postal_code\": \"734429\",\n        \"business_address\": \"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\",\n        \"user_id\": \"79\",\n        \"subject\": \"New Contact\",\n        \"pipeline_id\": null,\n        \"stage_id\": null,\n        \"dnd_settings\": \"{\\\"all\\\":false,\\\"emails\\\":false,\\\"whatsapp\\\":false,\\\"sms\\\":false,\\\"calls\\\":false}\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.261775, "xdebug_link": null, "collector": "log"}, {"message": "[10:26:28] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.280276, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754130386.521111, "end": **********.286089, "duration": 1.7649779319763184, "duration_str": "1.76s", "measures": [{"label": "Booting", "start": 1754130386.521111, "relative_start": 0, "end": **********.53486, "relative_end": **********.53486, "duration": 1.0137488842010498, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.534881, "relative_start": 1.0137701034545898, "end": **********.286092, "relative_end": 3.0994415283203125e-06, "duration": 0.7512109279632568, "duration_str": "751ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54988528, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads", "middleware": "web, verified, auth, XSS", "as": "leads.store", "controller": "App\\Http\\Controllers\\LeadController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=256\" onclick=\"\">app/Http/Controllers/LeadController.php:256-647</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.054680000000000006, "accumulated_duration_str": "54.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.613297, "duration": 0.03037, "duration_str": "30.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 55.541}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6654432, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 55.541, "width_percent": 1.792}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.682023, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 57.334, "width_percent": 1.829}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6902208, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 59.162, "width_percent": 2.707}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.696645, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 61.869, "width_percent": 5.797}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.7576349, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 67.666, "width_percent": 6.218}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.799686, "duration": 0.013380000000000001, "duration_str": "13.38ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 73.884, "width_percent": 24.47}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.273514, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 98.354, "width_percent": 1.646}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2780, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-138902526 data-indent-pad=\"  \"><span class=sf-dump-note>create lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-138902526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.992191, "xdebug_link": null}]}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads", "status_code": "<pre class=sf-dump id=sf-dump-6608185 data-indent-pad=\"  \"><span class=sf-dump-num>422</span>\n</pre><script>Sfdump(\"sf-dump-6608185\", {\"maxDepth\":0})</script>\n", "status_text": "Unprocessable Content", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-277788807 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-277788807\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1801567906 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">mama bar</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"8 characters\">mamabari</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>contact_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Lead</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">95</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">96</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">98</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">97</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"2 characters\">93</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"2 characters\">89</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>postal_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">734429</span>\"\n  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Siliguri</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bengal</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"5 characters\">India</span>\"\n  \"<span class=sf-dump-key>business_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Smart Internz</span>\"\n  \"<span class=sf-dump-key>business_gst</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Smart Internz</span>\"\n  \"<span class=sf-dump-key>business_state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bengal</span>\"\n  \"<span class=sf-dump-key>business_postal_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">734429</span>\"\n  \"<span class=sf-dump-key>business_address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">79</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => \"<span class=sf-dump-str title=\"11 characters\">New Contact</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>stage_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dnd_settings</span>\" => \"<span class=sf-dump-str title=\"71 characters\">{&quot;all&quot;:false,&quot;emails&quot;:false,&quot;whatsapp&quot;:false,&quot;sms&quot;:false,&quot;calls&quot;:false}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801567906\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1991866521 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3123</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarysvKvoQBjBebid1xx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjN1aVcwTUJ0VnNTQkc1WHJqTjQrT1E9PSIsInZhbHVlIjoidklWVUx4SGdoMjhuUHNGcURwUzNnMUlDVUYwelM4R2lNOXcvZ1N6aUlsS0dad0pZQmEwWkJ4OU42dHhMS09vK2g1YXpvc1dQS2pyQWtTdE40c3d2b1oxaUlDbHJsRHU2SUcxTnVxZXkzdUlFUWV1TnJjSEpNUDNmZmFvaUozc3NRRHlBM0hiZVJ4aU52ZDZZTTE2KzVvdjR1SHRUOElOSWwwRTFucENQWWM0bXJJbVhUNmEyeGRvVlZGZENXcnJDWmk2MGRNcmhCM0IrVVVUSldKZzkwU0pXdVBZb3FueUpWZHNjcGNyYkdPcWh0YlFrQ1d3dGw4UjExWVdSQXhaYWVPV3ZQd0g1dmZWLzA4QnRiOS85Qjlxb2ZMcnBINFFibE54OWRBb055cGVMUUFiQUxhZkdhZmh5TEI5cmlJbzJMYldTbjNwakdQdjY4SVZvNFI0SFVzZEJoWUJEUmtGMkJtdVA1TDQ0ekloMDMzM1JocDIyeVd3OUhpSGE3RGYxSTBOQTlLVXhvV1I5bXBHTk5GNTd2aFBwM0ZmK3ZUQ21YSWg4bHArc01IT2xIQmdVS0gwdTFwVTE0MFRjbVkra0FzOStnbTNTN2c4SXNOaXJ0cHVRZ0dLajVqVWhwVHVKS1JYckdLQVF6RHdqNU13OTVxeXRhekxQUWJ1VE8xYkoiLCJtYWMiOiJlNWFlYjUyODY0OGNkZTdiZDE4NDExNzhiMTU2NDJkZjg1YTM4ZTZhMWJlNzc2MzA0MjI2ZmYyZjgwZjBhMTkxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImlIM21qWHNKa3FHYUtpQmpDeFZQcXc9PSIsInZhbHVlIjoiMHRZemUrSG1VMUc1Z3lJRjRsUFh2MmhpYkVaME1LMCthSzJmOTZDb0FLdzdwRHN2NkwzQU42RkxaN0NqejhPU0NmWkpmZTJGMEYxbUliWElhRDBCeFlpbEVJQ3prUjFhRzdUOE94cENyandKUWlGVUE2bytWdlN6YTMxa2I2NmRCV3c3ZzU0WkhoUW0zOWMyMWlyb1pjV2xrUnFIbGZRbWFwNGJGWFArbit6MEJ0TXU4OVJZOG9hNE1mWURIOVJsS29hNi9QZkh1UG1ONk44a2IvanFNa3I3VncxcFgwTlg3RWtTZFpGWEpVK0hsajcwQVdEOWNIeVYwUGppNENJc01nNTdlbXRFQ3JmcTNqa01ZeHJUYmNRWVlrcWdIMS9STStaM2JhNXBIK2M3b2xKcjBEalJnbzhYajExRk1wN1daeFpqcUZnMDQxUWNSQW12YnJWSE9sOE1DNFE5eThEcE1nM2lwODBFeTZ3MUZ4Z0k3NFROeSt2RzcwOE9CTW5rV0NzTGpiQjM5STZQaU5qZDF6anJ0RG1YUnZvYTVZWHROVlRUUy9sY083RHBZMS8wVWYwbklkMVBrNDI4UmlwSmNXaDd1R3NROEthaWhTWGoxbGdGb2c2T2dGTGgxR0RJT21kcWhjbDRZSVpvQnN6S1FtU25LNUV0cTdTM1ZPZWwiLCJtYWMiOiJmY2ZhYTYzNTM4YmVjNmYzYzY0NTJmMjI3YzZlNTI2ZWFhYWUwMTljY2EyZmJlODVmZTc3ZjgzYjJmYWRkMDJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991866521\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-459738226 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459738226\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-571244785 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:26:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inlhc0JIUmdTc1h6SUpwR1IwRGpLSkE9PSIsInZhbHVlIjoib1NNTGVjM3kyVFhnNUt3MU0yM0FSN3diQVFiVFppL1czeHZaWWV5b2pHdjBjYks0VnNUNmNuM3YyUnJNSE0rUUdJOTliTmllK3lacDV4dXRrQkJKUHdiWWQ5M3pXZGVwaDEwbzNQS2JxbmVQQUJ0NTUrSUZmZE8yNU5hWFBxSDNCc1lwaDlZRCtBOEpocFkzOUVDcVNtd1N1TkpzSEZjQ3puRGxNN2VWcDBoUGVIYU1CNENSR2UxNEwwc3JGMGhTTUxHQnMvVjFtMHNyUUt4TVk4cGFkR3RGdC9GTmN3ZEhxeGZkRFBkcm82TjUzQk94a0o2aUtYTEJBbUFydzFvZWF1QU10bXFRNkRyQXR4blpSbG9UdFJyK1NRbi9ubWltSERUMG5wQWpkYmZDRU5YNFZEMmtyVC8wZHFGRUxWb2xWRCt0QXE3Q0Z3Mi81c200VEJNQmsxQS9iUWh2bDlMelpub2krZ1JLd0k0VmxlS0VXL0Y4T1pjTXZQbUZRVUdMMkpGd1BsQTNZaThZVXNnTXdCYXBBeVNkRkdvdVQrZTNVeUx0WTFUQlFSUlJWbmZDL3FBdkdsMmVadXI2QmkycHQwU1ptRDhJdGZGUUlpd0pJL3pPOC85UHZ5QVg5eEhQNVVKb3pkZUhBd3JYeGh6L1M4Y2pEdDc2SnBLbjBoSSsiLCJtYWMiOiIwMGU2MDY1MjNiZmM5ODJjNmEzYjAxYjQwZTA1NjM3ZWIwNWFmZDY3N2Y1ZmMyNDY4NDI2NTMzMmI3NGM4Yjc3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:26:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii8wcDI4bTRGdnlndmtNclFDMmJac1E9PSIsInZhbHVlIjoiZXh4ekVUQVN0eDY1Szc0MmFrWUNNR2NzSWtOSkdMcmY3U3B1YXZzUnBnOXZpa3JacHVzWVduNHJJTnZVelNoVE4wU0RGUUhiTHlXUVl4TEEwOHdPSlZUL0xyODZvQUpwKzEzdVY5d05idS9NSXpUamtneTdVL2w0N1pTeW1UWmM3dmtuWnhvRnhUTllHYmE3MThqOU5vVXB4cmxnYUZnRkM4MHRZQ1lGRzJBUldxWmVIdXVXR3ZIWEsya2pKQjFwRFEyUFc0ZHo1NW52aHR6MTBSK0RwYjdzWkUvTHRlM2tBMUxReFRFV3JWQUJ2Si92c29DeFpNSVFQYWRDSzNaMFAzN2NsdDR4ZCs0ZnhaajlYYVZWU2RpWmQyUTFLWHRwWnJVL1drenBVUExlYnd1UFNtcEpObERUN2l1MS9XR1lFRXRMQzlSUjFjVTY4QnlHSS96Tmp6VmpKWGRRYUdya1kvSzVWRi9nSURickJweUo5bUdHZGZ0RFN5bUlGeno4TldNaUdLckl6bGx2cEhiSTFqSDRSNTIzMEhPY25FalRucUdlOTdJS21qd1dkWHRQL0twTkxUM2c1Mm5DV2ZuamUzSnJNSitKelZBSFZUSkR0d2EwNXpidVhIZ0tKZFV6czRpSGtQZDhrMU1FeDF0MmVzeWFHeW1CVndGRE9HVzkiLCJtYWMiOiI5Yjg0ZWQ2ODMwZmIzZmM5MGIxOGJjNjU4YzFkNzNkN2VmZjZjYWQyYzY4M2FmNmZhMjEwZDA1MGYyZWM2ZmNmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:26:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inlhc0JIUmdTc1h6SUpwR1IwRGpLSkE9PSIsInZhbHVlIjoib1NNTGVjM3kyVFhnNUt3MU0yM0FSN3diQVFiVFppL1czeHZaWWV5b2pHdjBjYks0VnNUNmNuM3YyUnJNSE0rUUdJOTliTmllK3lacDV4dXRrQkJKUHdiWWQ5M3pXZGVwaDEwbzNQS2JxbmVQQUJ0NTUrSUZmZE8yNU5hWFBxSDNCc1lwaDlZRCtBOEpocFkzOUVDcVNtd1N1TkpzSEZjQ3puRGxNN2VWcDBoUGVIYU1CNENSR2UxNEwwc3JGMGhTTUxHQnMvVjFtMHNyUUt4TVk4cGFkR3RGdC9GTmN3ZEhxeGZkRFBkcm82TjUzQk94a0o2aUtYTEJBbUFydzFvZWF1QU10bXFRNkRyQXR4blpSbG9UdFJyK1NRbi9ubWltSERUMG5wQWpkYmZDRU5YNFZEMmtyVC8wZHFGRUxWb2xWRCt0QXE3Q0Z3Mi81c200VEJNQmsxQS9iUWh2bDlMelpub2krZ1JLd0k0VmxlS0VXL0Y4T1pjTXZQbUZRVUdMMkpGd1BsQTNZaThZVXNnTXdCYXBBeVNkRkdvdVQrZTNVeUx0WTFUQlFSUlJWbmZDL3FBdkdsMmVadXI2QmkycHQwU1ptRDhJdGZGUUlpd0pJL3pPOC85UHZ5QVg5eEhQNVVKb3pkZUhBd3JYeGh6L1M4Y2pEdDc2SnBLbjBoSSsiLCJtYWMiOiIwMGU2MDY1MjNiZmM5ODJjNmEzYjAxYjQwZTA1NjM3ZWIwNWFmZDY3N2Y1ZmMyNDY4NDI2NTMzMmI3NGM4Yjc3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:26:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii8wcDI4bTRGdnlndmtNclFDMmJac1E9PSIsInZhbHVlIjoiZXh4ekVUQVN0eDY1Szc0MmFrWUNNR2NzSWtOSkdMcmY3U3B1YXZzUnBnOXZpa3JacHVzWVduNHJJTnZVelNoVE4wU0RGUUhiTHlXUVl4TEEwOHdPSlZUL0xyODZvQUpwKzEzdVY5d05idS9NSXpUamtneTdVL2w0N1pTeW1UWmM3dmtuWnhvRnhUTllHYmE3MThqOU5vVXB4cmxnYUZnRkM4MHRZQ1lGRzJBUldxWmVIdXVXR3ZIWEsya2pKQjFwRFEyUFc0ZHo1NW52aHR6MTBSK0RwYjdzWkUvTHRlM2tBMUxReFRFV3JWQUJ2Si92c29DeFpNSVFQYWRDSzNaMFAzN2NsdDR4ZCs0ZnhaajlYYVZWU2RpWmQyUTFLWHRwWnJVL1drenBVUExlYnd1UFNtcEpObERUN2l1MS9XR1lFRXRMQzlSUjFjVTY4QnlHSS96Tmp6VmpKWGRRYUdya1kvSzVWRi9nSURickJweUo5bUdHZGZ0RFN5bUlGeno4TldNaUdLckl6bGx2cEhiSTFqSDRSNTIzMEhPY25FalRucUdlOTdJS21qd1dkWHRQL0twTkxUM2c1Mm5DV2ZuamUzSnJNSitKelZBSFZUSkR0d2EwNXpidVhIZ0tKZFV6czRpSGtQZDhrMU1FeDF0MmVzeWFHeW1CVndGRE9HVzkiLCJtYWMiOiI5Yjg0ZWQ2ODMwZmIzZmM5MGIxOGJjNjU4YzFkNzNkN2VmZjZjYWQyYzY4M2FmNmZhMjEwZDA1MGYyZWM2ZmNmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:26:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571244785\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-214623362 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214623362\", {\"maxDepth\":0})</script>\n"}}