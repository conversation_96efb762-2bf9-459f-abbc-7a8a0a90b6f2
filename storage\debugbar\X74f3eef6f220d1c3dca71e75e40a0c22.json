{"__meta": {"id": "X74f3eef6f220d1c3dca71e75e40a0c22", "datetime": "2025-08-02 09:30:33", "utime": **********.476258, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754127032.045265, "end": **********.476317, "duration": 1.4310519695281982, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1754127032.045265, "relative_start": 0, "end": **********.328133, "relative_end": **********.328133, "duration": 1.2828681468963623, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.328159, "relative_start": 1.****************, "end": **********.476321, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QJQkaBifUskJIc4tFrNRoyqU6eabjZmJubyprAD4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-525628570 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-525628570\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-552914730 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-552914730\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1853413082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1853413082\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-859971866 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859971866\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-111302515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111302515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1689185816 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:30:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik8vNnp6MFhrRUFzRmRlajFOMDJMdlE9PSIsInZhbHVlIjoibVd5QmxnVkMwT2ZsSGJiQVUzVkhxdDdwUDBFOC96SlJPamVpS21sSW1ONXRtZEtUeERhMmxubGdQajNJc2hsKzlnc2I4L29yS3FzNjlzSW50V1JHR3Z3ZC9HQTRjTS9YU20zTFBQSUQ2cDdLTG9lSEFkcmROYVlhbSt0Ukh0NVJvYzY0VXNhemxUQ2hZd3o0cUppcGNjYVdYdUlVYVFzVFcrbmZ0SzB3Q0ZSUndRZE8xUmJEU1RsR1FFQWVnc3ZoTFRjeEdqaVgwb1FVbTdoa2NGbEdQc2JkMWhGN0FuVlZYcmhhOWxnaWtyRUFId1I2bUFkN1ArcXgzRmlMVWZhNzRRdEVkRDYyWVhraGVqY09sdHBpTHJ4WXFnMTlZT1hlUjJiWHpWK0UrZDQvVjVWbVFWdVEyN21WeUcyR0I3M1dZTWtheEtWVDBqOHZHSzZ3QU4zVnB4ZUtnMUJKd2h2RlJISWdvRFY1cGtFOXZEdHl4N2U4eUdEclMySXRRNXJST0Voai8yMTUzUHR1SnZTN2dVdUhoWEM2MnRZSlREL2dDckl5ek4zUlFVeVpJUzBMbGZzNTY1bzVPZjlMR2tKVE9xL0hxQzltKzI5WEpQU3RtQjlPclp2VnBLS0daZEFrajR1aGV6OHlMN2hmUm1rbW0rTFowY0t6WGk1UWs4VXQiLCJtYWMiOiIwMDA3YTMyNWEyYWVjZDZiZWE5ZmUwNTk4YjYxMDkxMDVlZTM2N2ZhN2E4NDdmMzU4NTIxZjRkMWVkYzU0ZmQ4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:30:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkNNYm1ZbWVHWDFZRVM3cHVyWDdBa3c9PSIsInZhbHVlIjoiYkhYVkNKR0ZTbzkyQktJeTRHT1k1SFl1VkVrZnljdi9QenlYb29wTGZEMk9OZnA0a2p3Tm0zMmZJOTdFRUovMmVUSGxLMnVRRTc0Q2s0ZWZ4Wm8xQ3JUTUg1T0d3d0FWMlZoejVLMERodWZsQ20yak8rbk0zNkcwNnJkMGhOZE52amVUSzVvcGhwT3FtWTM4cUFCS3p1aC9lZ3dxdUJKQ1pHWmNVRlpPWGRtTFBCZkdsbXg3WS9mWk1hRFVOVlp0cm1QUHVrdE5WM2xRc1R3RTU1SmliQ1pxZWhjSGxtUWNjSDRXQzJqRXE2MXliRVgzbFVaVi91b2UxS05uazRxd0F1Ny9FQlMzek5sRE85bjYxVkNYU1BwV29ybGFWK1B0SFA5ZHMzRlVIRGh5Vm9RVS9OVHFKVnd4V0NYLzNJZkpBd3VyYVZnSjh6aDhiUjBveERzQytzRFYzWWhVS0FXakRQVkFXVEtxMU1mZXVJbUcrZHZ0R3MxdEtYR3gxNjVmY091WnEwUFhHckhRTGI4MVJqS3h0U054TzExK0JvOTR1ais0YU1hTGFpVEVUMElWcEtBZTlmcUdZVG5qLzBOQjlydjMrS1U0dTNwanEzeklmbHAvQTNpKzU1bU1iVXdFbXlEVjM5RUZvckJZTXhXTzhoM0RYLzJRQ0k0Z3BlT3IiLCJtYWMiOiIzMmEyMGJmODljZWY5MGViZGJiZjkzOWE2MDI3NjQ0ZWI1ZWI3MmEyYzkwZDE0OTc2ODgxY2VlZGUxMzQ5NGNkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:30:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik8vNnp6MFhrRUFzRmRlajFOMDJMdlE9PSIsInZhbHVlIjoibVd5QmxnVkMwT2ZsSGJiQVUzVkhxdDdwUDBFOC96SlJPamVpS21sSW1ONXRtZEtUeERhMmxubGdQajNJc2hsKzlnc2I4L29yS3FzNjlzSW50V1JHR3Z3ZC9HQTRjTS9YU20zTFBQSUQ2cDdLTG9lSEFkcmROYVlhbSt0Ukh0NVJvYzY0VXNhemxUQ2hZd3o0cUppcGNjYVdYdUlVYVFzVFcrbmZ0SzB3Q0ZSUndRZE8xUmJEU1RsR1FFQWVnc3ZoTFRjeEdqaVgwb1FVbTdoa2NGbEdQc2JkMWhGN0FuVlZYcmhhOWxnaWtyRUFId1I2bUFkN1ArcXgzRmlMVWZhNzRRdEVkRDYyWVhraGVqY09sdHBpTHJ4WXFnMTlZT1hlUjJiWHpWK0UrZDQvVjVWbVFWdVEyN21WeUcyR0I3M1dZTWtheEtWVDBqOHZHSzZ3QU4zVnB4ZUtnMUJKd2h2RlJISWdvRFY1cGtFOXZEdHl4N2U4eUdEclMySXRRNXJST0Voai8yMTUzUHR1SnZTN2dVdUhoWEM2MnRZSlREL2dDckl5ek4zUlFVeVpJUzBMbGZzNTY1bzVPZjlMR2tKVE9xL0hxQzltKzI5WEpQU3RtQjlPclp2VnBLS0daZEFrajR1aGV6OHlMN2hmUm1rbW0rTFowY0t6WGk1UWs4VXQiLCJtYWMiOiIwMDA3YTMyNWEyYWVjZDZiZWE5ZmUwNTk4YjYxMDkxMDVlZTM2N2ZhN2E4NDdmMzU4NTIxZjRkMWVkYzU0ZmQ4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:30:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkNNYm1ZbWVHWDFZRVM3cHVyWDdBa3c9PSIsInZhbHVlIjoiYkhYVkNKR0ZTbzkyQktJeTRHT1k1SFl1VkVrZnljdi9QenlYb29wTGZEMk9OZnA0a2p3Tm0zMmZJOTdFRUovMmVUSGxLMnVRRTc0Q2s0ZWZ4Wm8xQ3JUTUg1T0d3d0FWMlZoejVLMERodWZsQ20yak8rbk0zNkcwNnJkMGhOZE52amVUSzVvcGhwT3FtWTM4cUFCS3p1aC9lZ3dxdUJKQ1pHWmNVRlpPWGRtTFBCZkdsbXg3WS9mWk1hRFVOVlp0cm1QUHVrdE5WM2xRc1R3RTU1SmliQ1pxZWhjSGxtUWNjSDRXQzJqRXE2MXliRVgzbFVaVi91b2UxS05uazRxd0F1Ny9FQlMzek5sRE85bjYxVkNYU1BwV29ybGFWK1B0SFA5ZHMzRlVIRGh5Vm9RVS9OVHFKVnd4V0NYLzNJZkpBd3VyYVZnSjh6aDhiUjBveERzQytzRFYzWWhVS0FXakRQVkFXVEtxMU1mZXVJbUcrZHZ0R3MxdEtYR3gxNjVmY091WnEwUFhHckhRTGI4MVJqS3h0U054TzExK0JvOTR1ais0YU1hTGFpVEVUMElWcEtBZTlmcUdZVG5qLzBOQjlydjMrS1U0dTNwanEzeklmbHAvQTNpKzU1bU1iVXdFbXlEVjM5RUZvckJZTXhXTzhoM0RYLzJRQ0k0Z3BlT3IiLCJtYWMiOiIzMmEyMGJmODljZWY5MGViZGJiZjkzOWE2MDI3NjQ0ZWI1ZWI3MmEyYzkwZDE0OTc2ODgxY2VlZGUxMzQ5NGNkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:30:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689185816\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-441795352 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QJQkaBifUskJIc4tFrNRoyqU6eabjZmJubyprAD4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441795352\", {\"maxDepth\":0})</script>\n"}}