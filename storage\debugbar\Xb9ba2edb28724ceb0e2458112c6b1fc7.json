{"__meta": {"id": "Xb9ba2edb28724ceb0e2458112c6b1fc7", "datetime": "2025-08-02 09:41:09", "utime": **********.309339, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[09:41:09] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.305008, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754127668.444041, "end": **********.30938, "duration": 0.8653390407562256, "duration_str": "865ms", "measures": [{"label": "Booting", "start": 1754127668.444041, "relative_start": 0, "end": **********.150974, "relative_end": **********.150974, "duration": 0.7069330215454102, "duration_str": "707ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.151003, "relative_start": 0.7069618701934814, "end": **********.309385, "relative_end": 5.0067901611328125e-06, "duration": 0.15838217735290527, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48368224, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.03215, "accumulated_duration_str": "32.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.196596, "duration": 0.02478, "duration_str": "24.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 77.076}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.235421, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 77.076, "width_percent": 2.799}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.24087, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 79.876, "width_percent": 2.24}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.244446, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 82.115, "width_percent": 1.649}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.257988, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 83.764, "width_percent": 2.177}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.266123, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 85.941, "width_percent": 3.39}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.271431, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 89.331, "width_percent": 2.644}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.276303, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 91.975, "width_percent": 8.025}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-923710393 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-923710393\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1144081913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1144081913\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-379450644 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379450644\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-384811879 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdMenkwbExmOEhrQ3ZGZWlRSVg1Rnc9PSIsInZhbHVlIjoiSjJ0TlN1UStJeHBZMFBOSjFiK00wSFJ1bStnN1VpQWYxRW9LSlN6UGpvQklGRExWWVQ0WG9RODhLTFFabnhaNm9sTy8zdlFzNndGUzIyS1lsRDdCY1FQSjRaVzJuaytTSis1WEZZNVZOcTFOTW8rUGtKbDNBZExvcHB4UTlZTUhRZ3F3Yzg1V0hRYlB5MElxZzJZM1VxT0ZmdDA5L2RTWFZBbXBwZkNqMTFnV2FraGJYTWJjSG9EQ1oxMkhTRVBzWDg5RTRTTGlsZHBtalNWcEY5TitiWkNsUmRadThTcmdObjNINEQrek05Q0hpK3FFYmV2ZmZTUFRaeklXV0E2UElFZXhvWVJQSDRWbGg1WWEvcS91SStIZjJCdmZPTEtlZmtuM1pyM3NkdGRHMGFKT2xDQUJvK1crRFNGeW4xYlJtYkZEbVdxM0hhZlEyd3BYU21nUm9mZXJkckwvWENlTXlHSUJPM0FDMnp3bElLV081emRTeC9HRElKQVl3dVd6U0xheHFsOExPdElNM002M0NsTUtsUzFOcFVyU2xuQU8zMi9YaWZ3NUxjVUhjQjdsdmtUbzBqbFd5NEVPODlnOGlvNjJ0ckMrdHlMY0owYmdUVjJtdDZhN2JWL0Z6akp3R292VHVXVFBZNFdQVVBiQXRaWkxHUGpGTkZpU0Q1YWUiLCJtYWMiOiI2MGI1NjZmZGE0YTIyYWUxYzY1ZThlZGZhOGVmZTZlNzU0ODk0MDM1ZTI3YzBhZjI3YzM4ZWE1YTRlZmUxZjhlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkRRNThhQitOSjZ1anBxeTRtcC9DQlE9PSIsInZhbHVlIjoiVFBKdExUUFY5RFJ4cjVaMGFXbVlRaWVNR2pUZWJpazRFNERQZkpBWjQxeDBvSDBmR1NLajR5VFdNaG4wbWg0dXYvM2dwaUZxQlNvTmtkcWVSMzhVU1VwcHhOM1JGeFRuNUVoVTJNZFpPenluV21VeDZ3WXhTZUVpUFAxMUdERHQ4QzEvK1lTNlh2L2dKQWxKbVBKdWpHSGlVVTljSyszZkRqRzNMM01idDZPZVR4VHZyWk9ZU0ZLbkVHSjNoVUk4bEVYQzNlMitKa3Q2T09qc3hpRlcrUncrbEhwVHl5aWYrS2ZFNTZJYnFRclVCVzFOWnlrQk04cjVUd3RYby9ZQktNaTV0STFDOGJ3bU50aTVKUDJVNVdVQ1ZjcFVxWGZlNnhLZnhGRHNLTUpOYlYvUlpjQmRKdVFLUWxqOVcrTmJzbmxtMzcyQXMyaWZGZHZWVnpjc1NEZnRVR0NZVXRwbHUxSzJaMTJicFZ3b0lhT3hBRHY5MnVsd2ZadHFXN2NGSjJISHVCVm5ocWlTakJhbEJtMVFoWUlvaWhqWk83RC9Md0RINTVpL29YblUyT1VGZ2ZEUHpSQitNNTFPYWpBYmxMOWtwY3J1MnZZeDNEb2Q0czAvZmxoWUhFYVlkaFU2bWJjUlNwUTNPVS9aUnduTTJhc24xdG02SUFDQkE0YTYiLCJtYWMiOiIxYzc1OTVkNzVhNjBjNDU0YTM5NDQ0M2JjMGExNDdmOWRhMzg1OTM2NzhiMTk3MTNiYWY5ODBmYjI3NzlkNjYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384811879\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-60531791 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60531791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-885247968 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:41:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktOQmxiZVl2Y0lqRkQ4TGZDbnZrZEE9PSIsInZhbHVlIjoidFVjQ0U1WG5JcGs0aUhBeFdJcFRrbnF5UEhEKzBBZUVmV3lxSkNkZjA3V3EyNzhMSTZPS3I4VEdvNUVpTXNrLzlYYytkNkx4cTFHN1YvOW8xOE1hS3ZiR0NmYVVwRjlodklVNFBZaE9ITk9pTG5qMnNXd09uc2dCRURyN1I4b2d0V3REVmEyWGZad0xLS0s2Um9YaXpZMnZzYnVvK1Q4cEtnalRMbnJrd1FuSy9ValZrSjRSZ21DS2RRMjM0eXZDSWlmUHZNTXp0QS9IRWhTR0FyUmtlUmhIQktYNDEvR1YyNGEzdFkxTXhaNlBJbTRXV3ZvTzVQVnNZK1RtM2NGNlMzZHZwdW4wNVF2TXViQlJveWEzNVdUTmlOT1JNcFhFQmR2VFBSbS9EOWlubXVzbjd3SVE5M1pBK0xYTGNDTVh4MGticUxLUEF2SzR1V0daRjI5dmNOOHZrUWFOU0g3dzQ1NzJ5VGJHNUFsYXRHbkxoa0dmSDVKL1pwVjR5ZGlsM21RMkJEUTlvZzBDbFhUb0NCVzJVY0dLM3JoR2ZWZE1MUWRVNXBtc1A5azhFRGE0RUJERmtTOEIxVk5oMm5yK2xjWXdzaDBKK3djN1B5NXhwSTdMTzNTVDd1NFoxRzRmREYwaVFaWWlmT0FLdWRXb2dMcVhQTDd2ZXBjVzhZZkIiLCJtYWMiOiI3NWMyMjIxYWExZTFiNGJlMWE2ZGRiMjY5YjdmZGZkZGJhNDJmODk2ZmJlZjYxMGUzNzEzYjI0ZjczZWYyODM2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:41:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IldGTjhGbDcxcVVPaEFRbjU2NFpEeUE9PSIsInZhbHVlIjoiQkJYaGtjTmVuSlRtUnhzdUxtc1VFMzRrMHJsbmZzUGFjdG1vaFFrWlgzbHBwWTdGZ2ZiZFUydVF0dWNjdUJmbm9EZGt6WHhybU1PTGwyK1I0a3M1R1AzZkp5UURlYjNpOHhLaHIwTVk2VHRkNVB6OVA1OTVUc0djT3VWdU1zVFlZaVBqNUlyOWMwKzNSMDlncU1WUWk2MkV4eXRyT1lQRjZ6T3V1bUFocGtDQy82NzEyZHQxcTFPT0VxUlA2cDdGYi9lQ3JsOFhqcXFtcG5LcVIzUFl2akxqVEdNNWY0alNDMWZRVDlvR0NkbGV1VEdxS1NzZ1FiaGRTMlRuUitPY3o5ckpVUVZPSmI2VzlYZXdyNmNUajExeXQrQlI2OWxCL212SStqTkIzSlNySzd2QjYwcU10bzFRRGwzVmJwRStQSmdISzNRM29CS0hyUFJENnhoM21QTGh5bWVreGN1bE9pQ0ZobkFJVHhRV011OTlUdzBFWjhSbXpqcytYVnFUVFFWRkRQOEhLeWhNZFFyNEVmREdiYm1DV3luTjFDckJveHdQdHZTRFhUTTJrcnRYMHU3bDlVVmcyaDBWU05aUUxZbkNGYWRNeWxXMHlLUERwM05OOW9JN1BJV3ZONGdFN3BXekMzQmtoZzcrYzdQbnpDOU4ySUlpWVUwdi8yelgiLCJtYWMiOiI1Y2EyNjNlMzU1OTQyN2Q4NzdhN2FlOThjNzQ2ZTViOTQ4NDM0ZjlmMmZkZWFkMDk4OTQzNTg3ZjcxMTdmODA3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:41:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktOQmxiZVl2Y0lqRkQ4TGZDbnZrZEE9PSIsInZhbHVlIjoidFVjQ0U1WG5JcGs0aUhBeFdJcFRrbnF5UEhEKzBBZUVmV3lxSkNkZjA3V3EyNzhMSTZPS3I4VEdvNUVpTXNrLzlYYytkNkx4cTFHN1YvOW8xOE1hS3ZiR0NmYVVwRjlodklVNFBZaE9ITk9pTG5qMnNXd09uc2dCRURyN1I4b2d0V3REVmEyWGZad0xLS0s2Um9YaXpZMnZzYnVvK1Q4cEtnalRMbnJrd1FuSy9ValZrSjRSZ21DS2RRMjM0eXZDSWlmUHZNTXp0QS9IRWhTR0FyUmtlUmhIQktYNDEvR1YyNGEzdFkxTXhaNlBJbTRXV3ZvTzVQVnNZK1RtM2NGNlMzZHZwdW4wNVF2TXViQlJveWEzNVdUTmlOT1JNcFhFQmR2VFBSbS9EOWlubXVzbjd3SVE5M1pBK0xYTGNDTVh4MGticUxLUEF2SzR1V0daRjI5dmNOOHZrUWFOU0g3dzQ1NzJ5VGJHNUFsYXRHbkxoa0dmSDVKL1pwVjR5ZGlsM21RMkJEUTlvZzBDbFhUb0NCVzJVY0dLM3JoR2ZWZE1MUWRVNXBtc1A5azhFRGE0RUJERmtTOEIxVk5oMm5yK2xjWXdzaDBKK3djN1B5NXhwSTdMTzNTVDd1NFoxRzRmREYwaVFaWWlmT0FLdWRXb2dMcVhQTDd2ZXBjVzhZZkIiLCJtYWMiOiI3NWMyMjIxYWExZTFiNGJlMWE2ZGRiMjY5YjdmZGZkZGJhNDJmODk2ZmJlZjYxMGUzNzEzYjI0ZjczZWYyODM2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:41:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IldGTjhGbDcxcVVPaEFRbjU2NFpEeUE9PSIsInZhbHVlIjoiQkJYaGtjTmVuSlRtUnhzdUxtc1VFMzRrMHJsbmZzUGFjdG1vaFFrWlgzbHBwWTdGZ2ZiZFUydVF0dWNjdUJmbm9EZGt6WHhybU1PTGwyK1I0a3M1R1AzZkp5UURlYjNpOHhLaHIwTVk2VHRkNVB6OVA1OTVUc0djT3VWdU1zVFlZaVBqNUlyOWMwKzNSMDlncU1WUWk2MkV4eXRyT1lQRjZ6T3V1bUFocGtDQy82NzEyZHQxcTFPT0VxUlA2cDdGYi9lQ3JsOFhqcXFtcG5LcVIzUFl2akxqVEdNNWY0alNDMWZRVDlvR0NkbGV1VEdxS1NzZ1FiaGRTMlRuUitPY3o5ckpVUVZPSmI2VzlYZXdyNmNUajExeXQrQlI2OWxCL212SStqTkIzSlNySzd2QjYwcU10bzFRRGwzVmJwRStQSmdISzNRM29CS0hyUFJENnhoM21QTGh5bWVreGN1bE9pQ0ZobkFJVHhRV011OTlUdzBFWjhSbXpqcytYVnFUVFFWRkRQOEhLeWhNZFFyNEVmREdiYm1DV3luTjFDckJveHdQdHZTRFhUTTJrcnRYMHU3bDlVVmcyaDBWU05aUUxZbkNGYWRNeWxXMHlLUERwM05OOW9JN1BJV3ZONGdFN3BXekMzQmtoZzcrYzdQbnpDOU4ySUlpWVUwdi8yelgiLCJtYWMiOiI1Y2EyNjNlMzU1OTQyN2Q4NzdhN2FlOThjNzQ2ZTViOTQ4NDM0ZjlmMmZkZWFkMDk4OTQzNTg3ZjcxMTdmODA3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:41:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885247968\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-521350252 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521350252\", {\"maxDepth\":0})</script>\n"}}