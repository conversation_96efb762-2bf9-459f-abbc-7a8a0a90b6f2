{"__meta": {"id": "X46561fabb97f18051399ea5fc3adaf6a", "datetime": "2025-08-02 08:40:38", "utime": **********.416649, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:40:38] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.40489, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754124035.091902, "end": **********.416708, "duration": 3.324805974960327, "duration_str": "3.32s", "measures": [{"label": "Booting", "start": 1754124035.091902, "relative_start": 0, "end": 1754124037.917585, "relative_end": 1754124037.917585, "duration": 2.8256828784942627, "duration_str": "2.83s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754124037.917627, "relative_start": 2.8257250785827637, "end": **********.416717, "relative_end": 9.059906005859375e-06, "duration": 0.49908995628356934, "duration_str": "499ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48368224, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02529, "accumulated_duration_str": "25.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1045089, "duration": 0.00795, "duration_str": "7.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 31.435}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.147808, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 31.435, "width_percent": 4.745}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.161725, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 36.18, "width_percent": 4.033}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.173407, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 40.214, "width_percent": 4.033}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.218961, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 44.247, "width_percent": 6.959}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.250325, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 51.206, "width_percent": 5.694}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2639248, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 56.9, "width_percent": 6.485}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.278976, "duration": 0.00926, "duration_str": "9.26ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 63.385, "width_percent": 36.615}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1250679347 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1250679347\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-348263719 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-348263719\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1738923876 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738923876\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2003149188 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlSeHh5bXlIRXZhcVl5dW9UM3VjUHc9PSIsInZhbHVlIjoidjhUSGV2RnB5SEtTbW0rWUs2ZUVndHRWaFB1L0NISE1KNnYyeURLQk9UNGlKUjBTMTF3dTRQSWdDNDRBRnd6aS9ESklEVGhnUDBQWk5Kcm9XcEJXeXlpbDNOTEVuTnRIK2dJeFFKYVZWYzliSEcyeTNmdVJTNDdncHZmUkI1ZTVsc21GNnVhemNtUHdmcmp1aTdIalJUcTh0M25EdTBsOXdWdVgwS3RxU2ZuVnlBSlh0Q1hUSE9BRUgvSHJlcnlKQm9KS282dlh0VmgxTVVYL2ZjcWVtaERENTE1dUFRR3hBRWJ0UlhURERYMGVhT3phTkhlZHpRL1JuL3FVWXMvZ2lSQ2JOWlB0N1E1RVV5eks2cnZLZ0NaT2dzdnljaGFQU0RwVjNaUTZvRyttOVJNTitaU1V5ZGVoQmVINWw3SmN4VXg0OVI1ais3TDN5d3M1cmFCaGdmMWdaRGxHNzFieHQzTzM2T3JZbzZQM3JjVStqT2g5R2lvRDRPYXdRMzhjb2NiSWFDUzRLN1ZUb1Bsb2hGa1JmeUJockNhd1RKTTJndkNiV0hXN3k4S1psaFpZWjdMbXhOdHk0QzVGY2JvNFgzdW5PNHB4K0pJSHVIQU5adWhnbEFaWEhBUmQ3OTU1TWtqNDhoeUd6REU4UmZPcEJCZUxuZTRkQi9Pa0pCSmoiLCJtYWMiOiIyZWMwNGM1YjNhMGYzOTg0NDBiMWM5Zjk3YWFlNGU2ZmNhZjU4NTlkY2FmZDVmMWQwMWMwZDhiM2NhZGY0MTY4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkwwaUI2K3VvMEZ1eHpoRDFvK1VLc0E9PSIsInZhbHVlIjoiQ090R3dTdmFLM1FHUVJudllFbzZPR21FUzRieHd3NjVvamNxWGZ3UzkvMVdxVEtlbkk1MW44Q0FmY2djMS9CVWE5ZlplRGFNeDdadFl0M2N0ZEtVekpJd1BnSTNjUjlZTzlHMmc5UDJNc2l4VElGNjhmbXo5M0pvekRDMVR4b2lQVmhMZnFBUGVvRmRURkNIaWVOWmNPa2ZNWDh4T3pIcVdyeFZkSzhWaE0ybVI1VnFIbEVRZkh4QllkTFZQTFdhVGJ0SkkwYVJwT1NuOWlDZm5mZGtnTEpnS1VhMGczMSs2Vmt3QTBsRXlrUWlvWkYwWVJtRzRlZE90ZTRQWWt1ODZxaERZejU4NERkZHRKUkE3aW1GTFZUUzduZnN4bThEdTRnRUY3K29FYnFoTE9KRkZGUS9TUXBxdWpmSWpUZm4zalFsaXg3all3VWxSTnBWTFV4SE9Lb2xiZ09YVHVFaVVYSTYrUG90Ym9wNnJMenl0Y0cyWk9WN09UNGdmV2N1bEJOQThUcG5rRVlxR3pZNUtvTnJLMGdOL0d2YnFRaUEwU2ZTS2RMMnVoWEhlc1hQVUFJdFBLbVJZcjBRYWYvR2tlZlNZZnNoS3dvWit0UFlaOUNuT2Rzb2s1UVZlUzU5RGpHTTNUZnF5RjZGVWVNSUFoYVFFWUhuVnh0THVUNloiLCJtYWMiOiJjNjVmYzc1ODFhM2JiODNhNTVlOTkxYTQ0YjNlN2FlMDIwYzM3Y2UyZjUyNzE1ZWVmYzVjZGExMjIxNjg1N2M4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003149188\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1467126277 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467126277\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-886723925 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:40:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRic1ByK0M3UUt5Nmd4alArdDNqRFE9PSIsInZhbHVlIjoiWVBkQWdkQlp0VzRCR3lCS0o2NEErYlZtbkNka0FabU1XNHl0SVFrc3pha0x0eVJZNlZDeTlvVnBHNXRsUmpqYXE0TTFjeGlnSjZYd1B1OS9YVlo2RXNYQlhyc3V0eDJzQXNXR0FhMnE4SUMzbWVQQXBoQ3JpUklWTVllRUJFNzh5ck4wbkwxdWYrSGoxM3pUWFE4WmdVR2R1Ny9xOUtzY0pVOHZlVjkzMEg1VzNFcjJLV2J4MW1QNnVKTjlXM2RHNG0wU1k3Y3R5bExYT1M2bk4rS0ZuL1lJbTlaUTFTaS9WVXgwUWpPRTNHZE94ckI4QW51cVNqV2hVWEovT0xxK2kyaDdJM1pFVllQUmJ0VlZTZzd5Q05NeDE3ckZIM283SjZXZGRPRFZQS2dqWW1mUDhEZnpoc3Q2WWhnMWdOK2Z6L3I4VVA3U0c2WHdPRVYya2hDVTBkNm1uaktFdHlkNGprSG5XYlVPUm1YQ0NELzF3dXMyemhmRHJrTzJoRldVRk05S3ArckNkVDh1Rk9WMGhzcEZPeHFmODVvSVk5Y21IaTdMNUhaVkxUckNweUIra3FrNFo1TUZOTlJ2QTZmYjRlOHNRWFNrb1ozZlYvWUcraFFIc0hRU3ZwZndObTNQOWpQdDJWOEs2ajZnK2dUWG56Z2ZhS1pTMEVCWUptVVkiLCJtYWMiOiIwYjlkNGY5OGM5NGJiNzhkYzI1NzZlOWQ4ZDMzNGE5ZTE2NjE2YWM5YjkxMmI3MWU4OTY1NWFlYzZhNjc5OGE5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:40:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNLUSt4YkZVbEhxT0Jjdjl4bHhRbHc9PSIsInZhbHVlIjoiWFphWG90U0Y1Qy90QjgxanhJZUp0SWE2OUczcmlWNk80QmFlZFkwOTlDSkx5d1VEZ3I2NDkxeWZPTjdsYnh2WnJ6NDd1YUt1RFVLT2N6dDlyd0dBNGJtWDlvT0t5SWlSZFJLT09Ba2RybW52OERMeU4xRmowclpCYmxuLzQ3N1lTdW5lcnJCYTFrdHVXRXU5VVJqTU9CS1B3dlQvMzRxdWx6Tzc2cG5POUJ6elhCZnJJTEJhOVVJOU5ObHJqcXBocG9ndG10V1FjUlZqZ1ZpK2FIMUtRRUQ4UmF3SUlRNE12Q1JyRVh6M2N4NWg0b0UwRWZGMk9hTnBNYzluMkRFWVg4RitJeHVETGhqcGdWQ0pGSjUrVWd0T1B1RDRwWTJrYmFGbitHNUI1azU4U1N4UHRNSk5xdWYwOUpxQWJsLzU2TkgyWlZuSW5sUDV1ZTJnUEVFbFlwdzQwRCtKamJ4N0pYY3JWNUViSnczRW5YOEJncXE2aWJuc1I0V2FTRnhRZmxhWnlYSDVuc0lZQk5uNUtxelptcGFpUEFMRFhxUzVzWk9TcDlFdTdEM1hxTTg4dGErM1ZGZXBvenJ1MHp1bHJTUVZvS3YwMnh6K0x4OWwxNEI0SjgyVi9haGdScjMzYlE5N0tmNGtjZlh1dWRUNFY5OUdZVjFEeU1nTlVxQnkiLCJtYWMiOiIwY2RiZGZmOTFjZTQ1ZDgyN2IwNjQ5MmM1YTdiNmY2YWMzMWEzMWE5OGIyMTgzNjFkOGMzMmM0ZGE3ZjkwZmNiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:40:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRic1ByK0M3UUt5Nmd4alArdDNqRFE9PSIsInZhbHVlIjoiWVBkQWdkQlp0VzRCR3lCS0o2NEErYlZtbkNka0FabU1XNHl0SVFrc3pha0x0eVJZNlZDeTlvVnBHNXRsUmpqYXE0TTFjeGlnSjZYd1B1OS9YVlo2RXNYQlhyc3V0eDJzQXNXR0FhMnE4SUMzbWVQQXBoQ3JpUklWTVllRUJFNzh5ck4wbkwxdWYrSGoxM3pUWFE4WmdVR2R1Ny9xOUtzY0pVOHZlVjkzMEg1VzNFcjJLV2J4MW1QNnVKTjlXM2RHNG0wU1k3Y3R5bExYT1M2bk4rS0ZuL1lJbTlaUTFTaS9WVXgwUWpPRTNHZE94ckI4QW51cVNqV2hVWEovT0xxK2kyaDdJM1pFVllQUmJ0VlZTZzd5Q05NeDE3ckZIM283SjZXZGRPRFZQS2dqWW1mUDhEZnpoc3Q2WWhnMWdOK2Z6L3I4VVA3U0c2WHdPRVYya2hDVTBkNm1uaktFdHlkNGprSG5XYlVPUm1YQ0NELzF3dXMyemhmRHJrTzJoRldVRk05S3ArckNkVDh1Rk9WMGhzcEZPeHFmODVvSVk5Y21IaTdMNUhaVkxUckNweUIra3FrNFo1TUZOTlJ2QTZmYjRlOHNRWFNrb1ozZlYvWUcraFFIc0hRU3ZwZndObTNQOWpQdDJWOEs2ajZnK2dUWG56Z2ZhS1pTMEVCWUptVVkiLCJtYWMiOiIwYjlkNGY5OGM5NGJiNzhkYzI1NzZlOWQ4ZDMzNGE5ZTE2NjE2YWM5YjkxMmI3MWU4OTY1NWFlYzZhNjc5OGE5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:40:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNLUSt4YkZVbEhxT0Jjdjl4bHhRbHc9PSIsInZhbHVlIjoiWFphWG90U0Y1Qy90QjgxanhJZUp0SWE2OUczcmlWNk80QmFlZFkwOTlDSkx5d1VEZ3I2NDkxeWZPTjdsYnh2WnJ6NDd1YUt1RFVLT2N6dDlyd0dBNGJtWDlvT0t5SWlSZFJLT09Ba2RybW52OERMeU4xRmowclpCYmxuLzQ3N1lTdW5lcnJCYTFrdHVXRXU5VVJqTU9CS1B3dlQvMzRxdWx6Tzc2cG5POUJ6elhCZnJJTEJhOVVJOU5ObHJqcXBocG9ndG10V1FjUlZqZ1ZpK2FIMUtRRUQ4UmF3SUlRNE12Q1JyRVh6M2N4NWg0b0UwRWZGMk9hTnBNYzluMkRFWVg4RitJeHVETGhqcGdWQ0pGSjUrVWd0T1B1RDRwWTJrYmFGbitHNUI1azU4U1N4UHRNSk5xdWYwOUpxQWJsLzU2TkgyWlZuSW5sUDV1ZTJnUEVFbFlwdzQwRCtKamJ4N0pYY3JWNUViSnczRW5YOEJncXE2aWJuc1I0V2FTRnhRZmxhWnlYSDVuc0lZQk5uNUtxelptcGFpUEFMRFhxUzVzWk9TcDlFdTdEM1hxTTg4dGErM1ZGZXBvenJ1MHp1bHJTUVZvS3YwMnh6K0x4OWwxNEI0SjgyVi9haGdScjMzYlE5N0tmNGtjZlh1dWRUNFY5OUdZVjFEeU1nTlVxQnkiLCJtYWMiOiIwY2RiZGZmOTFjZTQ1ZDgyN2IwNjQ5MmM1YTdiNmY2YWMzMWEzMWE5OGIyMTgzNjFkOGMzMmM0ZGE3ZjkwZmNiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:40:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886723925\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-663671332 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663671332\", {\"maxDepth\":0})</script>\n"}}