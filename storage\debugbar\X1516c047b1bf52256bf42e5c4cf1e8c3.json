{"__meta": {"id": "X1516c047b1bf52256bf42e5c4cf1e8c3", "datetime": "2025-08-02 10:05:28", "utime": **********.865298, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754129127.748314, "end": **********.865335, "duration": 1.117021083831787, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1754129127.748314, "relative_start": 0, "end": **********.736123, "relative_end": **********.736123, "duration": 0.9878091812133789, "duration_str": "988ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.736141, "relative_start": 0.****************, "end": **********.865339, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3036\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1883 to 1889\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1883\" onclick=\"\">routes/web.php:1883-1889</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6pCWbAadvDFW9yDYpKFBYxEEM1EBGt6fsu9e5Pbj", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1446219252 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1446219252\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1536260011 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1536260011\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2046986135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2046986135\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-481758416 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481758416\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-935888229 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-935888229\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-819467463 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:05:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9ubXUvUzRhWVErY0NtNHRyZjNoR1E9PSIsInZhbHVlIjoidkk2YkpSdGROaXptSWdTOVF6d3BSTFp2SnBaYUNGRnJWei9xVlhSalpaKzloVXRVenNLUUZ3SW9mc21HZ3AzcWh2eUlTRnI2WXhoaGdHQWgyNU5ZWW91YVNlTXh0U3pWZGtHcEtSeTR0Z0tzN0xUK3B1U0M3M0FDSURMemJvZ2VqcjJxRktIL09UV2xKUEF0SStVNEl2ZjZkU1VZU21QcDhvbzhUYkFXWUUyRDJZenZkMXF1VkdUOFJpajRwSzd4Zy8zSWhteHNyZUgxMDc2ZDIvaVZKelFvTUNXeGU4aWJVS1huRFNHVHBYZnNiYUp0SWhBSmNUYjdxU3JTQUtDcTBjQi96L0hqYkVueW55UXo1aUJKQnN2TmwzUHJsZnNOU3drSkFoNlpVb21lZ25lRUlzdU41NDZxWGZGb3dEQmVvMldYMHN2UWo0WnNJR1Q0VXRXVnJKRkJNdjcvTTJIdzBSeXcwQVZyaUNRZ1N2UW1vdm1IaWQvNThJb0RPT1pEUWU0dFRUL2FUS09Qd055N0JabW9qZDBNR0k4QUdsUW53Z21CSEozRzIyTFNIM243Rk9PYlh6NVFQNHNYL2ZXMUljeFBCdnpoendQcXA2ekRVSEo4c0w5KysvUHViaFhSY2pUQkl1Q1RXODlnM2xnVzhrb1VpS3BHSDNXMHpSbWgiLCJtYWMiOiJkMjkwNWNkODE4ZGYxN2FmMjM5NWI4ZmNkMTgxYjE5Mjk1M2RkYzBkOWViN2U3NGYyMWYxOTY3MTJkODAzOTkzIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:05:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkRVRDc0eG54OERna3hzdFFWQVl6TWc9PSIsInZhbHVlIjoiWElUL1Zqb25uOUU0akQwaHFXSHhuTnpXOHdSUDNoNldna09lZjN1dDJWRXFPdkxQMXluRXpQT1ZFUC91Nm1VR1YzeGRtVXFDZlZ2TWlwT1BLNjlsYmNKbEl2M2phUTRZRldlWjBJWG5EODMxeU5SVFdqVWpHTlU2R01GeEluUys1K0J5L0k3K3RoMHhjNFovbXR1bm9EODY3alJsOTlwMnpQZ2NwcjlEcVRxV1VFUjVYNVBhSWt1ZGZkVmE3WUdIRmNmdU1XV0JUb2hGQVhiZVZMNWdQVjRkYUljd3l1aG1tdkNoSzlVc0tJYXhyWDJobVA1K2pNWEJSMm9MQXBjTnNINUw1SVRLajRmcHFKVktxclBUdVd4K00vNktDWjBtcHNUbEZRbHNDdFFHb0lEaEw0ei9HdDZuYyswL0xGekFHdHZXekpRbW04dTNIN1hPYlNPTEkvb2VRSURaOENLak9IcUNUVUZOVU9PM3JKemVLNDUrU0pMNEt0dUo4cVdhR3VlbUFhdjU4eUxQRjdsZzZqdnJEVEMwcEdqL1dYbndiRW1XK015U2hwblBMY2FydFFkOVpQVnBCSzNqd1R6M1JuRnNhWWVmK2w3MlFLeHlqZWNsakJzeFdXMVUzYTJHZE4wVnptbGdCNStXVndGWjZwOU5pK2hLclFwVVJCSmciLCJtYWMiOiI5NGRjNjc4ZjM2ZGM1ZGFhYmM4MmQ5MDI0ODVlNjY2Y2RhNjE3YTJiOTM2ZTJhZGNiODk2OGZiMGNlZDE0ODcxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:05:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9ubXUvUzRhWVErY0NtNHRyZjNoR1E9PSIsInZhbHVlIjoidkk2YkpSdGROaXptSWdTOVF6d3BSTFp2SnBaYUNGRnJWei9xVlhSalpaKzloVXRVenNLUUZ3SW9mc21HZ3AzcWh2eUlTRnI2WXhoaGdHQWgyNU5ZWW91YVNlTXh0U3pWZGtHcEtSeTR0Z0tzN0xUK3B1U0M3M0FDSURMemJvZ2VqcjJxRktIL09UV2xKUEF0SStVNEl2ZjZkU1VZU21QcDhvbzhUYkFXWUUyRDJZenZkMXF1VkdUOFJpajRwSzd4Zy8zSWhteHNyZUgxMDc2ZDIvaVZKelFvTUNXeGU4aWJVS1huRFNHVHBYZnNiYUp0SWhBSmNUYjdxU3JTQUtDcTBjQi96L0hqYkVueW55UXo1aUJKQnN2TmwzUHJsZnNOU3drSkFoNlpVb21lZ25lRUlzdU41NDZxWGZGb3dEQmVvMldYMHN2UWo0WnNJR1Q0VXRXVnJKRkJNdjcvTTJIdzBSeXcwQVZyaUNRZ1N2UW1vdm1IaWQvNThJb0RPT1pEUWU0dFRUL2FUS09Qd055N0JabW9qZDBNR0k4QUdsUW53Z21CSEozRzIyTFNIM243Rk9PYlh6NVFQNHNYL2ZXMUljeFBCdnpoendQcXA2ekRVSEo4c0w5KysvUHViaFhSY2pUQkl1Q1RXODlnM2xnVzhrb1VpS3BHSDNXMHpSbWgiLCJtYWMiOiJkMjkwNWNkODE4ZGYxN2FmMjM5NWI4ZmNkMTgxYjE5Mjk1M2RkYzBkOWViN2U3NGYyMWYxOTY3MTJkODAzOTkzIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:05:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkRVRDc0eG54OERna3hzdFFWQVl6TWc9PSIsInZhbHVlIjoiWElUL1Zqb25uOUU0akQwaHFXSHhuTnpXOHdSUDNoNldna09lZjN1dDJWRXFPdkxQMXluRXpQT1ZFUC91Nm1VR1YzeGRtVXFDZlZ2TWlwT1BLNjlsYmNKbEl2M2phUTRZRldlWjBJWG5EODMxeU5SVFdqVWpHTlU2R01GeEluUys1K0J5L0k3K3RoMHhjNFovbXR1bm9EODY3alJsOTlwMnpQZ2NwcjlEcVRxV1VFUjVYNVBhSWt1ZGZkVmE3WUdIRmNmdU1XV0JUb2hGQVhiZVZMNWdQVjRkYUljd3l1aG1tdkNoSzlVc0tJYXhyWDJobVA1K2pNWEJSMm9MQXBjTnNINUw1SVRLajRmcHFKVktxclBUdVd4K00vNktDWjBtcHNUbEZRbHNDdFFHb0lEaEw0ei9HdDZuYyswL0xGekFHdHZXekpRbW04dTNIN1hPYlNPTEkvb2VRSURaOENLak9IcUNUVUZOVU9PM3JKemVLNDUrU0pMNEt0dUo4cVdhR3VlbUFhdjU4eUxQRjdsZzZqdnJEVEMwcEdqL1dYbndiRW1XK015U2hwblBMY2FydFFkOVpQVnBCSzNqd1R6M1JuRnNhWWVmK2w3MlFLeHlqZWNsakJzeFdXMVUzYTJHZE4wVnptbGdCNStXVndGWjZwOU5pK2hLclFwVVJCSmciLCJtYWMiOiI5NGRjNjc4ZjM2ZGM1ZGFhYmM4MmQ5MDI0ODVlNjY2Y2RhNjE3YTJiOTM2ZTJhZGNiODk2OGZiMGNlZDE0ODcxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:05:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819467463\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-999337850 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6pCWbAadvDFW9yDYpKFBYxEEM1EBGt6fsu9e5Pbj</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999337850\", {\"maxDepth\":0})</script>\n"}}