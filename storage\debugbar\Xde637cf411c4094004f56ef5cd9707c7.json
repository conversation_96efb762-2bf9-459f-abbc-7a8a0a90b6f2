{"__meta": {"id": "Xde637cf411c4094004f56ef5cd9707c7", "datetime": "2025-08-02 08:20:19", "utime": **********.670559, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754122818.436316, "end": **********.670587, "duration": 1.2342710494995117, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1754122818.436316, "relative_start": 0, "end": **********.582977, "relative_end": **********.582977, "duration": 1.1466610431671143, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.582997, "relative_start": 1.****************, "end": **********.670589, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "87.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TQV3KGQg1ThdnKmnx5yQeqK1XrOfwUjyL4blv4lI", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2070114192 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2070114192\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-565942348 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-565942348\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1542763953 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1542763953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1047040653 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047040653\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1167134823 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167134823\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-839859468 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:20:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlMRHBuSmtIUlFqNHFrSnh6MFBmaHc9PSIsInZhbHVlIjoiYkxueFRmcWQ2eEthMkFSQjdCZnU2WFRNQ1Z2TDQxRGtnQnQyUUp3MEZlbi9OMFpGWklCcXZHSHlxSUwwNm84b3B4RWJEN2I3cmZLblZSdHVHNTZCNWt6ZUdKSkFEeGZGcnFQOHBaUkJCRHpHSks4cXBWNThXSWxOZFRrdzY5a1FTaWtCZis1UEcraGVHMmFWYkFXWENWTEJSbUZRK3ZBdnV0UkQvaVVia1lHc05oUnpnRCsrUnFJZm1ZUVF1ZDlpZFZsWjVYcnh5aFJ4TUFJN1J6cXBkVFNRL3hhUC9iRWF3ZXFpZXpoM0piOS9MTS9XRHFsZmRZbGZMaGhETjBEaEk1UmhwUkU0RitiUWs2VzNBNTdFVFpvMWxyMDQ3c2trcVRZQlFIcTFVYWRuZzBKbGVUTmlUMW9mYmZ2UWxPTUJrU3VvaWYvOWtPNlM4L2JmZmVwbk9WN01BTlVtV3JXZmRyTFNFVVpaeitUNlNjNHdKbnhYYkZrYXhobDVsdDBma3JuOUJqVnF3QldMeDZ5aW50RDdScjZ1NGdNNTlYVTVHQWIzdCthSUVydVhLUjY1YUpPaEFjZUt1NmR0KzRkUDY0ZGxOaUtJa2Qwc2dYeXNaeFhPVUdPdXRwMDQ5K2h2UWlNaDVYZnZVZm5xaWpJTWNPcDF3VUMxWWlwT1VvcnciLCJtYWMiOiJkNTc1NDZhMzBiOTNkM2IyM2Q3ZjFhMDhlYmFmNmE0ODFiNTUzZGQ4YTFhNDI2M2NhMjU3ZmM1MTFjYmRlMmNkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:20:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IllJenFiOFJLd1RWNGtmNnE3WTFJVmc9PSIsInZhbHVlIjoiSCs5WlVXcGR6NThpTUVPaEp2dGcyblNlZ0djTjRHK0xjVGdOb2xpQldCUHBwY2lDU2VHRVU0R2oxbFZCeCt0WWZsN1VJRGNpdXRZRGR3VTZUUlZiWW4rMkpDY25PRzFOOUNUaTc2WXk5ZnBCeTBKN0lwbXg5Y2tZV3BPV3V0U2NmSGxleHhWNXFQdUNlRGpBckRxazVZTWN4WXF3eVcwREhkWlpWOHdtMXFCQUh3eEp6Z3hmVmdhd1lZZ0tUSnhzY0ZXVStYeFo4SmFuclZTK1RpazF3L0tteXVGb2dUMmdsS0pYeUk4QnpLN2JMaVRVK1JTbzF5dnBUdHRRbkhjaENXOThqRU5malE2bDJsWUJDNktabUxZQ2ZWdFpHc3h5VXd6bDM5azNuRTZRbGI5Ti9TazFXbUgvVTNMWlZoZTVjd1BJTTVLaTZ3bjlaRGlzdlRhTWhtdm1JMzhNYlJiUmZya1RyN1VJK1RWQXphQVpzYUo2UmNvcCsvOUY1eWhaU3VlYXR5cmlnV1dVcnlyUy9wM2VIakVsYWxWdWdMOXBVaW9JSURWeVlKaEJwbDY3cTNnNlpFOHJzbnlSdWhnNG1VU0RRc1Y1dmtTMU9VTCt6V09kZlVNUUpEU0htMy9XTW1sTUlqcjl0N084K0NhWEJlckdVdE94K2ZCaitmU3giLCJtYWMiOiIxNzk2YmMyNzI5MmMzZTM5NmQxODk1NGRhY2ZjOTY4NmYxNzhiNGMxMTNiYzI5NGQzMThhZjI2ZDVhYTdjYTlhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:20:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlMRHBuSmtIUlFqNHFrSnh6MFBmaHc9PSIsInZhbHVlIjoiYkxueFRmcWQ2eEthMkFSQjdCZnU2WFRNQ1Z2TDQxRGtnQnQyUUp3MEZlbi9OMFpGWklCcXZHSHlxSUwwNm84b3B4RWJEN2I3cmZLblZSdHVHNTZCNWt6ZUdKSkFEeGZGcnFQOHBaUkJCRHpHSks4cXBWNThXSWxOZFRrdzY5a1FTaWtCZis1UEcraGVHMmFWYkFXWENWTEJSbUZRK3ZBdnV0UkQvaVVia1lHc05oUnpnRCsrUnFJZm1ZUVF1ZDlpZFZsWjVYcnh5aFJ4TUFJN1J6cXBkVFNRL3hhUC9iRWF3ZXFpZXpoM0piOS9MTS9XRHFsZmRZbGZMaGhETjBEaEk1UmhwUkU0RitiUWs2VzNBNTdFVFpvMWxyMDQ3c2trcVRZQlFIcTFVYWRuZzBKbGVUTmlUMW9mYmZ2UWxPTUJrU3VvaWYvOWtPNlM4L2JmZmVwbk9WN01BTlVtV3JXZmRyTFNFVVpaeitUNlNjNHdKbnhYYkZrYXhobDVsdDBma3JuOUJqVnF3QldMeDZ5aW50RDdScjZ1NGdNNTlYVTVHQWIzdCthSUVydVhLUjY1YUpPaEFjZUt1NmR0KzRkUDY0ZGxOaUtJa2Qwc2dYeXNaeFhPVUdPdXRwMDQ5K2h2UWlNaDVYZnZVZm5xaWpJTWNPcDF3VUMxWWlwT1VvcnciLCJtYWMiOiJkNTc1NDZhMzBiOTNkM2IyM2Q3ZjFhMDhlYmFmNmE0ODFiNTUzZGQ4YTFhNDI2M2NhMjU3ZmM1MTFjYmRlMmNkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:20:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IllJenFiOFJLd1RWNGtmNnE3WTFJVmc9PSIsInZhbHVlIjoiSCs5WlVXcGR6NThpTUVPaEp2dGcyblNlZ0djTjRHK0xjVGdOb2xpQldCUHBwY2lDU2VHRVU0R2oxbFZCeCt0WWZsN1VJRGNpdXRZRGR3VTZUUlZiWW4rMkpDY25PRzFOOUNUaTc2WXk5ZnBCeTBKN0lwbXg5Y2tZV3BPV3V0U2NmSGxleHhWNXFQdUNlRGpBckRxazVZTWN4WXF3eVcwREhkWlpWOHdtMXFCQUh3eEp6Z3hmVmdhd1lZZ0tUSnhzY0ZXVStYeFo4SmFuclZTK1RpazF3L0tteXVGb2dUMmdsS0pYeUk4QnpLN2JMaVRVK1JTbzF5dnBUdHRRbkhjaENXOThqRU5malE2bDJsWUJDNktabUxZQ2ZWdFpHc3h5VXd6bDM5azNuRTZRbGI5Ti9TazFXbUgvVTNMWlZoZTVjd1BJTTVLaTZ3bjlaRGlzdlRhTWhtdm1JMzhNYlJiUmZya1RyN1VJK1RWQXphQVpzYUo2UmNvcCsvOUY1eWhaU3VlYXR5cmlnV1dVcnlyUy9wM2VIakVsYWxWdWdMOXBVaW9JSURWeVlKaEJwbDY3cTNnNlpFOHJzbnlSdWhnNG1VU0RRc1Y1dmtTMU9VTCt6V09kZlVNUUpEU0htMy9XTW1sTUlqcjl0N084K0NhWEJlckdVdE94K2ZCaitmU3giLCJtYWMiOiIxNzk2YmMyNzI5MmMzZTM5NmQxODk1NGRhY2ZjOTY4NmYxNzhiNGMxMTNiYzI5NGQzMThhZjI2ZDVhYTdjYTlhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:20:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839859468\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1224795274 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TQV3KGQg1ThdnKmnx5yQeqK1XrOfwUjyL4blv4lI</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224795274\", {\"maxDepth\":0})</script>\n"}}