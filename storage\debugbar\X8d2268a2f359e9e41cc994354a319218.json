{"__meta": {"id": "X8d2268a2f359e9e41cc994354a319218", "datetime": "2025-08-02 09:32:07", "utime": **********.271991, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754127126.125582, "end": **********.272018, "duration": 1.1464359760284424, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": 1754127126.125582, "relative_start": 0, "end": **********.195872, "relative_end": **********.195872, "duration": 1.0702900886535645, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.195888, "relative_start": 1.****************, "end": **********.27202, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "76.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "frgryXg5xCtDAQHBATT427FrxD2J9vsJvQKvxPeT", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-494258544 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-494258544\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-491964055 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-491964055\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-980985438 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-980985438\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1062181785 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062181785\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1463162370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1463162370\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-121351526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:32:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik4zS21PNUdtTElmU1M5NGZQZUxLTGc9PSIsInZhbHVlIjoiVXF5Z2t2WmhYeXI0NjlhLy9hRXZFRHVOK1k5VktyTFRZN25XUEo3N3BpMWFaUEJHNVRSNHdyeGQwTHFsRk44MlVvMW5KNHBvdHhrTVZySXhCYTl3SWQ2WDBXekFYVVRwdTdLVDE4YWZ4MlJTTFkxTjVod1E5Z1R0SDJDd3IySXkrMFBxcXNmaTZiYmFvaTVGaWthTU5LSFZzaGdlRGJtOTFrbStFS3NoUTVzdXZlRGllRTg0azE3OWlYc01jN1BYMVgzdXh4RnJNclRXNDJ4R2xvdUZUanREeFNrdEpRNHJUdjVuc0Jhc0ZtZmdqVWFYR2UyUklVV3pPUVdScTBBZUhsblM0L1pMcW1jMVk4eFg1MWM4bDE2MGNvcnF5NmdHS3BhYVhWQU93bStLQlVtMXJhQWw3Ti92ZjEyR0M3eWF3VXF3T2RsY0ZveWZBOGxCQmFaMU8yNlc3R3FXWnIvNXFwRTM1MWdrdjhsb3lFTzBjMUpGOUFCQkxTbCs3R1A3ZFBGZ1VtcXdUWEJ6eTcvbFZudjRMNnlMWWtCTkV2azA1Y1c0OFgrWFQ1U2o4WFJTQk5vc2U3NkRMQ1pkNURyV2ZmNktZL3FkMjhNL21KYm4wdGVvTXMrdWdyMmVyMmY5M0Fzb0pRVXdHajh5L3ZmTGI5WVJqY1NLc3ZQOW1Ya1QiLCJtYWMiOiIyNGFhZjEwOWY0YTFmNmRlYmNmMmQ4ZTgxODM0OTlkM2Q5OTlkNTRhMDNlMzA3MzkxODIwNzQ3ZmVjZTg4MmZjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:32:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjN2eXRtQ1Y3YWtsVFM4cnpMaENscmc9PSIsInZhbHVlIjoiWWtoL1A4NGp6d3hTKzE0cVBhQm5QOXpwZk9ockx6VUhLODY2d3dCU0c1bWsvaTVDcEtOditlNVg5K3lhRVJ5WnQwZyswc1FDUlIzUDgyNWgvMVg4YTNyMDdoVk5nbUxCeTMvSlBUNXR3dVVjVXRiUlJxVklQaENBUUlhazBIeUM3anMzcUVBbmpYbXkxUWluMHlFS0t2YWxpM2U3Z2wvTk0wbUUwZElkVzNHeG1Ub3IrcUlNSWRlWDNibUJOdFRWTWJ1b0J2Rm9jWStBNGZBcG05OTFYMmpwd1RSSUdHbkluME45S3hwY0lDb1l5UmRzT2l2d2x5eC9MMEdUYTgxV3d5MHp0TmtNVmMvTWdIdUR1TEQ1cGpVdExkUGFWSmh4MGw4b1NVVkpBQ1J0TUM4SklPMEQzckZ1emZ5NndoeGtCWS9ZUVR3SVpKRWhKQjEzK0hkc0M5cHpYQWZxMWtpMi8yVk0waUhNTGRrNW5vN3R0S1FMc0RMQkJ0a2NtRDRiblM5eU9pQTNwaFY3QkVtS2dhMG1uNHdia3F5MG9pVldkVTkveVpkZlNLTHZYTVBIUVo3N1JGZjNacEczOGtYU05qYldhNk9lUDlDdlFtcE5Sem1rb010TU9Ta29kVHg1dHlOZzROUWpNSUdyeU9SLzk3eFFXcGVURVVjempoTmoiLCJtYWMiOiI3ZmI0MTk5MmY1ZjY5YmE2NzYzODg2YTBiMThiNWQ0NDg3ZWRkYzAzMjhiY2YyMjY5OTczMmU0ZGVjOWIzYzVmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:32:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik4zS21PNUdtTElmU1M5NGZQZUxLTGc9PSIsInZhbHVlIjoiVXF5Z2t2WmhYeXI0NjlhLy9hRXZFRHVOK1k5VktyTFRZN25XUEo3N3BpMWFaUEJHNVRSNHdyeGQwTHFsRk44MlVvMW5KNHBvdHhrTVZySXhCYTl3SWQ2WDBXekFYVVRwdTdLVDE4YWZ4MlJTTFkxTjVod1E5Z1R0SDJDd3IySXkrMFBxcXNmaTZiYmFvaTVGaWthTU5LSFZzaGdlRGJtOTFrbStFS3NoUTVzdXZlRGllRTg0azE3OWlYc01jN1BYMVgzdXh4RnJNclRXNDJ4R2xvdUZUanREeFNrdEpRNHJUdjVuc0Jhc0ZtZmdqVWFYR2UyUklVV3pPUVdScTBBZUhsblM0L1pMcW1jMVk4eFg1MWM4bDE2MGNvcnF5NmdHS3BhYVhWQU93bStLQlVtMXJhQWw3Ti92ZjEyR0M3eWF3VXF3T2RsY0ZveWZBOGxCQmFaMU8yNlc3R3FXWnIvNXFwRTM1MWdrdjhsb3lFTzBjMUpGOUFCQkxTbCs3R1A3ZFBGZ1VtcXdUWEJ6eTcvbFZudjRMNnlMWWtCTkV2azA1Y1c0OFgrWFQ1U2o4WFJTQk5vc2U3NkRMQ1pkNURyV2ZmNktZL3FkMjhNL21KYm4wdGVvTXMrdWdyMmVyMmY5M0Fzb0pRVXdHajh5L3ZmTGI5WVJqY1NLc3ZQOW1Ya1QiLCJtYWMiOiIyNGFhZjEwOWY0YTFmNmRlYmNmMmQ4ZTgxODM0OTlkM2Q5OTlkNTRhMDNlMzA3MzkxODIwNzQ3ZmVjZTg4MmZjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:32:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjN2eXRtQ1Y3YWtsVFM4cnpMaENscmc9PSIsInZhbHVlIjoiWWtoL1A4NGp6d3hTKzE0cVBhQm5QOXpwZk9ockx6VUhLODY2d3dCU0c1bWsvaTVDcEtOditlNVg5K3lhRVJ5WnQwZyswc1FDUlIzUDgyNWgvMVg4YTNyMDdoVk5nbUxCeTMvSlBUNXR3dVVjVXRiUlJxVklQaENBUUlhazBIeUM3anMzcUVBbmpYbXkxUWluMHlFS0t2YWxpM2U3Z2wvTk0wbUUwZElkVzNHeG1Ub3IrcUlNSWRlWDNibUJOdFRWTWJ1b0J2Rm9jWStBNGZBcG05OTFYMmpwd1RSSUdHbkluME45S3hwY0lDb1l5UmRzT2l2d2x5eC9MMEdUYTgxV3d5MHp0TmtNVmMvTWdIdUR1TEQ1cGpVdExkUGFWSmh4MGw4b1NVVkpBQ1J0TUM4SklPMEQzckZ1emZ5NndoeGtCWS9ZUVR3SVpKRWhKQjEzK0hkc0M5cHpYQWZxMWtpMi8yVk0waUhNTGRrNW5vN3R0S1FMc0RMQkJ0a2NtRDRiblM5eU9pQTNwaFY3QkVtS2dhMG1uNHdia3F5MG9pVldkVTkveVpkZlNLTHZYTVBIUVo3N1JGZjNacEczOGtYU05qYldhNk9lUDlDdlFtcE5Sem1rb010TU9Ta29kVHg1dHlOZzROUWpNSUdyeU9SLzk3eFFXcGVURVVjempoTmoiLCJtYWMiOiI3ZmI0MTk5MmY1ZjY5YmE2NzYzODg2YTBiMThiNWQ0NDg3ZWRkYzAzMjhiY2YyMjY5OTczMmU0ZGVjOWIzYzVmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:32:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121351526\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1554118478 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">frgryXg5xCtDAQHBATT427FrxD2J9vsJvQKvxPeT</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554118478\", {\"maxDepth\":0})</script>\n"}}