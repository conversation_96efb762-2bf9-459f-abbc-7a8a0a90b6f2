{"__meta": {"id": "X0f70472f2ea9942c2bbe8b98475ba6fd", "datetime": "2025-08-02 10:13:02", "utime": **********.497207, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754129581.851333, "end": **********.497233, "duration": 0.6459000110626221, "duration_str": "646ms", "measures": [{"label": "Booting", "start": 1754129581.851333, "relative_start": 0, "end": **********.430822, "relative_end": **********.430822, "duration": 0.57948899269104, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.430849, "relative_start": 0.****************, "end": **********.497236, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "66.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SBuCCQF3lVy5huIe1XNj5KAGBBZECXAGMsgTHGaE", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-161630935 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-161630935\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1359546004 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1359546004\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1258427131 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1258427131\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-92375322 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92375322\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-106748351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-106748351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:13:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1KZnd1Zys0MUd3VWpJYUQrQnMxMGc9PSIsInZhbHVlIjoiSFIzUEFIeVdTL3ZJVk5YRVV4S2JjZ0Vzdk5yaXlxQVpJQ2M3aUxWbitvNE1xL3R3YUR1L1psd2FYOFBFMXgxRUhGeVZUYkNlQTZXSmtFd0dPbVBUSHloblNMeUswTWxuakx0TTNwYk5NNFM4ejRsTE9GMmNVeTdnaFBYRzE2amFFUW03a2o3dnlOVXA4NHlNOVNEWGZ0VElsVE9yZDNFenNUaFpINGdLVG42dDdoM3VSVDBIeXZMTWJYUEJvYU5CbTkvT2ZFNHN0b3g0WWxMMU91akxwcnB3eGt0QWRhY2RWakNjWHBYWU5zajJjaTlSQzFqbVNzVjUvSlQyM1ArcThvZ0l2R2k3YnZ1eEVtRHc3SWdMN3M5SDBSMjNlUkFtMDQrcHNwYmdhejl1bnFrcG9yMHBTcnpEb1k0MlR4WmdBejlFdDcvS1g5bGp5ckFMc0dtQ0IzekdJQ2pHS2MwVmdIZHdrdTNGWTlIRDBVZTNRUG11T2tMc1RFRXpSQnNaMGd2YTF6YVZqZjVDQXdUZWpBKy9yYmExZ1VUZjdlcUdkc0RZQlNqM1ZsdEI1dVk4Mk5PZDMrZWtQZ0tlQnZUUEZQM2MwVnZBRGFkM0JHR0NIeXpSSVRhRkpDTXVGT2E2dzYvZUQ1NjlYSHpvc2hvdjk5WTh1VTkrOXRBdVlHdkYiLCJtYWMiOiJhNDBlOTI0M2Y1MDQ3MjhlOTFjNjExY2Y1MTk0OTdmMzc3MzE2ZDk3ZGVjZjljOTUxNmQwZTUzZTUwZDhlNGFmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:13:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InZndnAyMzl0bDBtazJkM0VNL3VyVWc9PSIsInZhbHVlIjoiMFBhbjFkd3hsczl0aEtsYmhUUlYrR2pNL0FhcnoxWTA0elJZN3NDdGF0dlk2eUdDQU41aGppWWpBVDhyb2dSdUF3bTN0YkYwWkFTbllzTFJ0VSs1L0RuVm5idVFXRUlyU2V0T2x1V2F0NjNvc1kxQThjK0Z4U04xTndicE9JcFJ0ekhHeTQxcTRRY3NYS0hFMGFtQlh4eXRyWmEwYzFkcmhJcGdjaFcwZVZpV24rY1BXWnFlbndDR0lVY2I1WVE1amo5SzE1dFRmR2pTdFBBVy9TMTdCOGlmRkpOUXNoeXRoQWRoR2dmR3Z4NUprSU5uOTJFYzF3OGVpUkZTQlZmVC9ZcVpiT3htWjloRUVuQndtS3VVSmdGSFVoUDZWQWxOVlhrUWt0NllBeUIzTEthSEh2VmxQSXNZbDhIeWJBVmxXZmhJaWdqSTE0VFRsbzM1MnpPY1NjN0pTSm1vc1NpSTA5ck56QVVKNWhvM2dHMkVhQVhCbUFGVDk3MGZXR2RSWk1MaGR0eGR4ZVZHZVpNL0VjdjNIbWJIcTJUeng5WnZ0R0QvNmtrZXRhVE44SStRQ2kwK0tlb2o0cndDdkxtaGlZWUUxazlnV2x4V2RJUVA0R3U3OUZ3TnZTNUkrUnlyZlR6K2IxQkRPWEtsaTZMTTd5ajFOd2Zoa1pjWWNhSWoiLCJtYWMiOiI0ZDA5OGJjY2ZiZTg0ZDIwMDhlM2ViMzYyYjI2NzM5OGJiN2YwNTVhNzQ3ZTFkMDM0MTc1Njc0MDAxZmU3YmY2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:13:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1KZnd1Zys0MUd3VWpJYUQrQnMxMGc9PSIsInZhbHVlIjoiSFIzUEFIeVdTL3ZJVk5YRVV4S2JjZ0Vzdk5yaXlxQVpJQ2M3aUxWbitvNE1xL3R3YUR1L1psd2FYOFBFMXgxRUhGeVZUYkNlQTZXSmtFd0dPbVBUSHloblNMeUswTWxuakx0TTNwYk5NNFM4ejRsTE9GMmNVeTdnaFBYRzE2amFFUW03a2o3dnlOVXA4NHlNOVNEWGZ0VElsVE9yZDNFenNUaFpINGdLVG42dDdoM3VSVDBIeXZMTWJYUEJvYU5CbTkvT2ZFNHN0b3g0WWxMMU91akxwcnB3eGt0QWRhY2RWakNjWHBYWU5zajJjaTlSQzFqbVNzVjUvSlQyM1ArcThvZ0l2R2k3YnZ1eEVtRHc3SWdMN3M5SDBSMjNlUkFtMDQrcHNwYmdhejl1bnFrcG9yMHBTcnpEb1k0MlR4WmdBejlFdDcvS1g5bGp5ckFMc0dtQ0IzekdJQ2pHS2MwVmdIZHdrdTNGWTlIRDBVZTNRUG11T2tMc1RFRXpSQnNaMGd2YTF6YVZqZjVDQXdUZWpBKy9yYmExZ1VUZjdlcUdkc0RZQlNqM1ZsdEI1dVk4Mk5PZDMrZWtQZ0tlQnZUUEZQM2MwVnZBRGFkM0JHR0NIeXpSSVRhRkpDTXVGT2E2dzYvZUQ1NjlYSHpvc2hvdjk5WTh1VTkrOXRBdVlHdkYiLCJtYWMiOiJhNDBlOTI0M2Y1MDQ3MjhlOTFjNjExY2Y1MTk0OTdmMzc3MzE2ZDk3ZGVjZjljOTUxNmQwZTUzZTUwZDhlNGFmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:13:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InZndnAyMzl0bDBtazJkM0VNL3VyVWc9PSIsInZhbHVlIjoiMFBhbjFkd3hsczl0aEtsYmhUUlYrR2pNL0FhcnoxWTA0elJZN3NDdGF0dlk2eUdDQU41aGppWWpBVDhyb2dSdUF3bTN0YkYwWkFTbllzTFJ0VSs1L0RuVm5idVFXRUlyU2V0T2x1V2F0NjNvc1kxQThjK0Z4U04xTndicE9JcFJ0ekhHeTQxcTRRY3NYS0hFMGFtQlh4eXRyWmEwYzFkcmhJcGdjaFcwZVZpV24rY1BXWnFlbndDR0lVY2I1WVE1amo5SzE1dFRmR2pTdFBBVy9TMTdCOGlmRkpOUXNoeXRoQWRoR2dmR3Z4NUprSU5uOTJFYzF3OGVpUkZTQlZmVC9ZcVpiT3htWjloRUVuQndtS3VVSmdGSFVoUDZWQWxOVlhrUWt0NllBeUIzTEthSEh2VmxQSXNZbDhIeWJBVmxXZmhJaWdqSTE0VFRsbzM1MnpPY1NjN0pTSm1vc1NpSTA5ck56QVVKNWhvM2dHMkVhQVhCbUFGVDk3MGZXR2RSWk1MaGR0eGR4ZVZHZVpNL0VjdjNIbWJIcTJUeng5WnZ0R0QvNmtrZXRhVE44SStRQ2kwK0tlb2o0cndDdkxtaGlZWUUxazlnV2x4V2RJUVA0R3U3OUZ3TnZTNUkrUnlyZlR6K2IxQkRPWEtsaTZMTTd5ajFOd2Zoa1pjWWNhSWoiLCJtYWMiOiI0ZDA5OGJjY2ZiZTg0ZDIwMDhlM2ViMzYyYjI2NzM5OGJiN2YwNTVhNzQ3ZTFkMDM0MTc1Njc0MDAxZmU3YmY2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:13:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SBuCCQF3lVy5huIe1XNj5KAGBBZECXAGMsgTHGaE</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}