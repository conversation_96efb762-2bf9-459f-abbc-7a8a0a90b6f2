{"__meta": {"id": "X9ff2a12faa4a9712ce705919d88f40bc", "datetime": "2025-08-02 09:35:59", "utime": **********.473935, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[09:35:59] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.452726, "xdebug_link": null, "collector": "log"}, {"message": "[09:35:59] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.457784, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754127358.150237, "end": **********.473993, "duration": 1.3237559795379639, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1754127358.150237, "relative_start": 0, "end": **********.30249, "relative_end": **********.30249, "duration": 1.1522529125213623, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302539, "relative_start": 1.1523020267486572, "end": **********.473996, "relative_end": 2.86102294921875e-06, "duration": 0.17145681381225586, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46525096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=2988\" onclick=\"\">app/Http/Controllers/LeadController.php:2988-3044</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.030369999999999998, "accumulated_duration_str": "30.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.39116, "duration": 0.02794, "duration_str": "27.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 91.999}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.445831, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 91.999, "width_percent": 4.05}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3015}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.453249, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:3015", "source": "app/Http/Controllers/LeadController.php:3015", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3015", "ajax": false, "filename": "LeadController.php", "line": "3015"}, "connection": "radhe_same", "start_percent": 96.049, "width_percent": 3.951}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-1137210749 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1137210749\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-23551734 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23551734\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1037935782 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1037935782\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-947714718 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im05ZnYvUDgreHV6V0w0K0NuclYvdHc9PSIsInZhbHVlIjoibmxJYklmaTN0VVRXTEJzTSt3YjdKOTNRVUdvd3ZlK2YrZkVwalUxRjFFQ1VCdFp5YUNmaHJhRlpVbnp3djNQZWlkaTJCUlJxd0t2TGYzYk95ek1LMlRxbmJ5NnRJaStzQmZMa2RmR0k1UmJPU1dqUzF5OEpqYm80RVlBdEREUTE2c3RPV0hybDF0Mm5vTGs1d3VwZFdDSGVGWUpxN2JkbmhMdSs0dVpGeGdFK0lzMDlxWmU1K2hSRG0zaERkWGUxZHhjSTZ4bHhBN0toZW1HZXFNUk1QbXlHQk5FYVlGMmZSa2d5eE4rWEM3S0FWVXRod3RNcUI4MUdLTWkzNGpMVG82aHFlQUY5c24wOExYWkJiTGF5VFI4NHBMQk5kcUdNVCtHTXBISUNwOGRxSlFaM3FaKzEyVkdjYUlUMzhobnhBcG9zbGcweU01Um84S1dlZURzVVlEdllCR09STmZGT1FiZHRwcVFqRmduU01PelMyUFRPMmhMWmJCMkEwZE01Snk4QnljSWZIaDYwMlFMTFJQZEpHUU5JYjI4RlgzMzF5TFhvU21CZDI5Z3BSYmJ6dkNFckZsMS94RnlRakc0SFY5eUJlOG5reG9qVGE4bldTUkxOcDdNQXhEbHJUMkhVV0FGVlpHUC9OQWt1bHhnQURwdFFUTFc1Q0Y5c0ROeWwiLCJtYWMiOiI0YWY3ZWY0ZThiZWI0NjgwZTRiMjc2Y2ZiMGNkYjZiZWIwN2EyNWE5ZmY4OWVjODM4MGE1MzZjZGViMjQ0NDY4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IldWaG5NUkdaYVFXWDlKak1rZ09qUUE9PSIsInZhbHVlIjoiQWpkaENrSFRXUTh3TnNvNnNlWjB4Z1EwQWEvNjhSU3FQTEJqYllTdU01UURrazlBNTl1L1dFTTVRSkdTOTc0WkFVQXVIb3Zzd1M0d2hmWHVOdjRUS0JoZG5oaXAyK0NmZU4xTVFRTkk1R0dYWWlJek1tdmRHdTZlOForakxNaDArK0taOHhGUmVSd0tHWXdNSjVnWmllRzJLRmcvTXJ3djJCT0FaSStwTFBSejdKZWsvTFEwQ0VrK2IrOENJaGhkcDhnU1RCSDdZZmVYWmpKelh5SXJPTUI5dXl5YUlOSnFwckt0VXFxcFgxcnNtNWNNUi9va251dHlvUjZtSjB5Q1Z2NEZYS2pCVHZGSnMxaWx3cDJVbXFrOHg4SW1oend2SVlabTFhdjlXT29XalYzYmhLZlh1YVdTd0dXMlNPV2I4SGtMd2lPbnFRWmlOWG1nMWk3QjQyc2IwUmhnQTlPUDB3MVVFNnk2NDFTeFc1anFPeW0rNEFhMnhDcktZQU5pczk1UUNlTzhXaU1CQmFTTzV4em54UEdxeXRVN2Vic2NmdlNlVW40ZlltSXpqaU53cHhmTGVBb1FlTGJwaERka3h1enEzU1djaU9MWVphZm4rV1Y5b21vR1Y4L2dPT21JaGlSRis4WGI5VnhxMWxHS0xBT09neGJnTUtaaUVkcWsiLCJtYWMiOiI0NTg1Y2FjYjQ5ZmVlMTE1ODJlODY0ZjgxZTI2ZmZkN2NhYTc1NzU5N2I1ZjMzY2YyMjVjOWJiODA5NjNkZWI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947714718\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-719850705 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719850705\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-103490146 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:35:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ3ekdVNDdUR3dkd2RxQWh1SGE1VGc9PSIsInZhbHVlIjoiM2xIdzNIQ0hFK3pwaFdkd3ZkcGhxS0VxeHVmTFpCUjVkZ1dubEIxNXhpNmJZQm1CMlRpOHo1TkJpUW9OMVBMWitKS2hVNzhtemFvbXJSYTNWOFZRVVhXeGdiK0Yyc1BjVFhOUnFGREN5ekNkV2RZQ0txTmNMaUM2QURFMldJek5mTysxckdvQldmU2pXV0pWRE45eENXajdrNC9zd3ZsVHVQS0VWdktvQzlkUVhVWFpwbXdid2kxcnFwUEt1akxuRGM0b2RJZVltQ0ZNRFF1WUJuN3lhaUNwT3p4cWlSNklqV1dQdlZxdnFucWpnaGQzejBXbEt4RThXUXBpMUV5RUp3MHB5SS95VVFWd3ZNZzMyTGVPRS9LSTZsT2ZTYkxjSmx0dXd3eExZbmF6WlZIQzArM3U5MS9WLzgyNDNVczBBRWs2NjRuUmdFUXZJVTdrd0RJcGluWCtMNHd6UVJ2UnhKUUpLa3V4SHFUR3BpUXJURFZrUlJyK2c2ZU0zR3ladnRCNURCaGlTc1ZoVFFYYzd3Q0hWcTJOYnBBK3JvaUNhcngwSU9hQkZKejRLOWtYaDd4WDR2RiswdEpCK1ltWStGdjdwSHoxcnJoSjgwTEFQcDBzMnRTZGtwdFhVRzJSNkZmVmFscDdjUVpjV0ZselJPNldiaklOM2JRdGZiN1AiLCJtYWMiOiJjNWE4MGJlYTk2MDdmNzA3YjZiOGQ4MjBjMjYxYjU0NzdlOGY1ZGZjNzg2MThkM2RjMTIxMWNmYjFiODE1NGI5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:35:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjkwU1RMTXlYZWJSaVQ4L2Q1MjJyTFE9PSIsInZhbHVlIjoiMU1DV0FhRmdZNjVDZmlRaXJuSTJhdTJCYzdpKzhSWFIyQlRrZHBvbnVkZzBXblYyMTN4cnJwKy8yR0ZMK2tmYSs2WGpRa1RXa1VmaUhETkVlM1p0K0w4a0NhdVhCOFRiUy9FU1NWdmxpYXhiVlB1Mm5Wa0RyU05pT2ZLaVlEWkgzdDUzbk1vYjNtSDFBbFVzOVhsS0pYUEoxanhZR0NPQWlJZjZIdk9VMHB4Qm9ybmJFcW5JcG50K1VCWnlNNGtXaW8ydk82Z2orTjBUcC9MUUx3aHRoOW9OQ3drK2hHam01RWV0Mm56Q0l0UWFMOTUyNEZkaG1RbDNHMWFyMGptMFRYTVVjblRpUjd5WVp4MllyT2R5MWZ4SmdVL2hncWJWNy9LQk9ncFRvWVNkcXphVTdmbEQraGdoUGxNanZ0ajI1TEt5VEg1N1dneU9yZFFQLzQ3Q05ucFhQUU9ET1U4TzA2cGpoR05tR1NtS1VNMFN3VnNKWDlQRGVrN29YU1R4N1E2RHRya0RjbjRsTFd2UEVuYVYxOHJxYkROR0N5VnFmVmJYK1ZSVDJIczRGRDFydDNiQ0d4N2psb0VYVno3ODdUL0hONDVNbmRzZk54MHFEZXEvNVJpNmd3WG1rdXE3OG5PbVRQSGxiMUhLT1ovUTdlLzdyeFRCbWNPTFJ3VWoiLCJtYWMiOiJjZWJmNGI5MzdjNGM0ZGUxMTA5YzgwZmUzZWI4NTI3MWI2OTA3YzMyZTgyNWQwMzhkM2IwYTgzZDQ1MDA4ZWRmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:35:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ3ekdVNDdUR3dkd2RxQWh1SGE1VGc9PSIsInZhbHVlIjoiM2xIdzNIQ0hFK3pwaFdkd3ZkcGhxS0VxeHVmTFpCUjVkZ1dubEIxNXhpNmJZQm1CMlRpOHo1TkJpUW9OMVBMWitKS2hVNzhtemFvbXJSYTNWOFZRVVhXeGdiK0Yyc1BjVFhOUnFGREN5ekNkV2RZQ0txTmNMaUM2QURFMldJek5mTysxckdvQldmU2pXV0pWRE45eENXajdrNC9zd3ZsVHVQS0VWdktvQzlkUVhVWFpwbXdid2kxcnFwUEt1akxuRGM0b2RJZVltQ0ZNRFF1WUJuN3lhaUNwT3p4cWlSNklqV1dQdlZxdnFucWpnaGQzejBXbEt4RThXUXBpMUV5RUp3MHB5SS95VVFWd3ZNZzMyTGVPRS9LSTZsT2ZTYkxjSmx0dXd3eExZbmF6WlZIQzArM3U5MS9WLzgyNDNVczBBRWs2NjRuUmdFUXZJVTdrd0RJcGluWCtMNHd6UVJ2UnhKUUpLa3V4SHFUR3BpUXJURFZrUlJyK2c2ZU0zR3ladnRCNURCaGlTc1ZoVFFYYzd3Q0hWcTJOYnBBK3JvaUNhcngwSU9hQkZKejRLOWtYaDd4WDR2RiswdEpCK1ltWStGdjdwSHoxcnJoSjgwTEFQcDBzMnRTZGtwdFhVRzJSNkZmVmFscDdjUVpjV0ZselJPNldiaklOM2JRdGZiN1AiLCJtYWMiOiJjNWE4MGJlYTk2MDdmNzA3YjZiOGQ4MjBjMjYxYjU0NzdlOGY1ZGZjNzg2MThkM2RjMTIxMWNmYjFiODE1NGI5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:35:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjkwU1RMTXlYZWJSaVQ4L2Q1MjJyTFE9PSIsInZhbHVlIjoiMU1DV0FhRmdZNjVDZmlRaXJuSTJhdTJCYzdpKzhSWFIyQlRrZHBvbnVkZzBXblYyMTN4cnJwKy8yR0ZMK2tmYSs2WGpRa1RXa1VmaUhETkVlM1p0K0w4a0NhdVhCOFRiUy9FU1NWdmxpYXhiVlB1Mm5Wa0RyU05pT2ZLaVlEWkgzdDUzbk1vYjNtSDFBbFVzOVhsS0pYUEoxanhZR0NPQWlJZjZIdk9VMHB4Qm9ybmJFcW5JcG50K1VCWnlNNGtXaW8ydk82Z2orTjBUcC9MUUx3aHRoOW9OQ3drK2hHam01RWV0Mm56Q0l0UWFMOTUyNEZkaG1RbDNHMWFyMGptMFRYTVVjblRpUjd5WVp4MllyT2R5MWZ4SmdVL2hncWJWNy9LQk9ncFRvWVNkcXphVTdmbEQraGdoUGxNanZ0ajI1TEt5VEg1N1dneU9yZFFQLzQ3Q05ucFhQUU9ET1U4TzA2cGpoR05tR1NtS1VNMFN3VnNKWDlQRGVrN29YU1R4N1E2RHRya0RjbjRsTFd2UEVuYVYxOHJxYkROR0N5VnFmVmJYK1ZSVDJIczRGRDFydDNiQ0d4N2psb0VYVno3ODdUL0hONDVNbmRzZk54MHFEZXEvNVJpNmd3WG1rdXE3OG5PbVRQSGxiMUhLT1ovUTdlLzdyeFRCbWNPTFJ3VWoiLCJtYWMiOiJjZWJmNGI5MzdjNGM0ZGUxMTA5YzgwZmUzZWI4NTI3MWI2OTA3YzMyZTgyNWQwMzhkM2IwYTgzZDQ1MDA4ZWRmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:35:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103490146\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1449870552 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449870552\", {\"maxDepth\":0})</script>\n"}}