{"__meta": {"id": "X82b656fbdd1f0efaac14326b00dd47a8", "datetime": "2025-08-02 09:27:27", "utime": **********.258928, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754126846.507398, "end": **********.258968, "duration": 0.7515702247619629, "duration_str": "752ms", "measures": [{"label": "Booting", "start": 1754126846.507398, "relative_start": 0, "end": **********.197462, "relative_end": **********.197462, "duration": 0.6900641918182373, "duration_str": "690ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.197512, "relative_start": 0.****************, "end": **********.25897, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "61.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "yyK2cKDsXLqFKZ58EWYA7ppOefFx790shA1YfsRy", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2146549689 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2146549689\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1528281731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1528281731\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1389377769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1389377769\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1227975180 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227975180\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1973122114 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1973122114\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-405358299 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:27:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVPMEJYbmkvVHRrYkhIUU5tbGU0ZGc9PSIsInZhbHVlIjoibXlLTkdOMlByZExQZEFDdmZiN1NiUDhiK2JFUnNkQW1jdDY0NFFOZ25WUG1wSFpNblArZnFWSHRqdjBhUGdubEgzRmZHVlZDZUJzK01XVlZ5QlBkK3NWdXFMS1NaYXNEU1lubk82eUZneEZaMXdldnN5eDJTZmg0Z0VuOFhLVW1KOXpmQ1JIQklSVytnUGZnMG5FN2ZNV3dmQjYzVUJUK0hMSU5Ua01pejc4eFlFSlJnbGdVSml3RkJSYlBydjRxUHNxbmdYQThPRnNHSFFGSjFBMWU1elpreEp5WEtHbVVSbUI1cGF3WU5RcnFLWUl1MFRqSko3NXI2cmtpZzJZMGZ0Ykx5NTlBYkQzYnJidkhNR1hIUTBYS3BOZWtod2syeHRvYU9FSzkrbk1uVlZ1aEZ3Nk8yUVZjUnRIRXJGL2pvOUxVYzlkQXVIaWRaWDZ1WUtFRVhPMk82TFdTTGwwK0FpUDBDbkRLK1orYnJNcXB5THFRY1JYTXJkUjJ2R3orbnYwamNkR3ZKOWJnWGp4VlJ0ekhVSXhwVXcwM0tjMTVoRjdHNk0rQUtrQzhKNDBHa3Y4YUVmN25oOU02cUs4TkNDTm5sMzFsUDBXRUxHK1hrY3VBVzhlNW8vZlRycm1LZVNvNWlkMXhsVVhjZU1qRXFtSnRiYmtxcnF1VWZ5WEUiLCJtYWMiOiI3YzBlNjM2NGFhZmZiODBkZjYzNTVkZTc5NWUzNTZhZDk5YzJiNThjNTNhZTU2YTcwZjA3OGI3OGZlOGY3OTUxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:27:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ink1QnQwYTlQWFNiVmZaK1NaNkI3OWc9PSIsInZhbHVlIjoidEZFOEgvVnEwd1JYcjBBeTV4ZDVTc0hORHl2clFSaTlpcTJYMUJiNzFBMGw2VzQvaVkwc3R3RUdtYUZtZngwTWJDVFZrWEdENVZ0UFE3K3BXMGZqNlNtTFBKWFoxY0IybFh4b0tDSzE3eGpJVHdVclNJSXdsbmJhV3RhMm5EK1FzMk5aUzM3d01zNmR0V2lnRHZ0OE16RTZwY29wUzg2ci9odGFtUmdUb0t2UTJxNFFnNTdoZE1NajN6ZWtYbFc3MCtwclc3RVdhTjZlSE1zNStZUXRrYW96YTlOSEJJVTFHSVp2d3Zaby9uZjdpNWdjQjRkNHhPbWRSVjJsVzdZQndMeTAzd29tS05XMU5SaXIrN21ORG1zNkhSN0FISW9PdjdXTzdlVXJDOTdnRzlLL3QrZzRaZjllZE1nTUVUKzBpengrZzhucEVXZVlTdjI1RWRBUFdLTlZVWUg5bFh6eHVZcFNabHdKcksxSThDdks1NUhtNFhkQ1FJVWh6Um90dXlEUm8xb05hODVHVXJPYS9LNm00RWtkYVFoNndWOW5ESGJSN3FEc2ZjeFhrbE9IVmRRcmRZSjluU1cxRlFHT3NqbWR6b3pjU2dXT2ZHYjg3ZmlkejVaSE1XWEhheElrM1dQQVh0YTdPSGhJejlxbkxLamNwalEzTVR2YmFqR2EiLCJtYWMiOiIzYjkzMDFiZWI3NzY2YjkzZGQyY2RhMWRmYjRhMjVjZmIyMjg1ZjJkOWM1MTJhMmYwMDRkNTZiYjJlYmI4NzA0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:27:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVPMEJYbmkvVHRrYkhIUU5tbGU0ZGc9PSIsInZhbHVlIjoibXlLTkdOMlByZExQZEFDdmZiN1NiUDhiK2JFUnNkQW1jdDY0NFFOZ25WUG1wSFpNblArZnFWSHRqdjBhUGdubEgzRmZHVlZDZUJzK01XVlZ5QlBkK3NWdXFMS1NaYXNEU1lubk82eUZneEZaMXdldnN5eDJTZmg0Z0VuOFhLVW1KOXpmQ1JIQklSVytnUGZnMG5FN2ZNV3dmQjYzVUJUK0hMSU5Ua01pejc4eFlFSlJnbGdVSml3RkJSYlBydjRxUHNxbmdYQThPRnNHSFFGSjFBMWU1elpreEp5WEtHbVVSbUI1cGF3WU5RcnFLWUl1MFRqSko3NXI2cmtpZzJZMGZ0Ykx5NTlBYkQzYnJidkhNR1hIUTBYS3BOZWtod2syeHRvYU9FSzkrbk1uVlZ1aEZ3Nk8yUVZjUnRIRXJGL2pvOUxVYzlkQXVIaWRaWDZ1WUtFRVhPMk82TFdTTGwwK0FpUDBDbkRLK1orYnJNcXB5THFRY1JYTXJkUjJ2R3orbnYwamNkR3ZKOWJnWGp4VlJ0ekhVSXhwVXcwM0tjMTVoRjdHNk0rQUtrQzhKNDBHa3Y4YUVmN25oOU02cUs4TkNDTm5sMzFsUDBXRUxHK1hrY3VBVzhlNW8vZlRycm1LZVNvNWlkMXhsVVhjZU1qRXFtSnRiYmtxcnF1VWZ5WEUiLCJtYWMiOiI3YzBlNjM2NGFhZmZiODBkZjYzNTVkZTc5NWUzNTZhZDk5YzJiNThjNTNhZTU2YTcwZjA3OGI3OGZlOGY3OTUxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:27:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ink1QnQwYTlQWFNiVmZaK1NaNkI3OWc9PSIsInZhbHVlIjoidEZFOEgvVnEwd1JYcjBBeTV4ZDVTc0hORHl2clFSaTlpcTJYMUJiNzFBMGw2VzQvaVkwc3R3RUdtYUZtZngwTWJDVFZrWEdENVZ0UFE3K3BXMGZqNlNtTFBKWFoxY0IybFh4b0tDSzE3eGpJVHdVclNJSXdsbmJhV3RhMm5EK1FzMk5aUzM3d01zNmR0V2lnRHZ0OE16RTZwY29wUzg2ci9odGFtUmdUb0t2UTJxNFFnNTdoZE1NajN6ZWtYbFc3MCtwclc3RVdhTjZlSE1zNStZUXRrYW96YTlOSEJJVTFHSVp2d3Zaby9uZjdpNWdjQjRkNHhPbWRSVjJsVzdZQndMeTAzd29tS05XMU5SaXIrN21ORG1zNkhSN0FISW9PdjdXTzdlVXJDOTdnRzlLL3QrZzRaZjllZE1nTUVUKzBpengrZzhucEVXZVlTdjI1RWRBUFdLTlZVWUg5bFh6eHVZcFNabHdKcksxSThDdks1NUhtNFhkQ1FJVWh6Um90dXlEUm8xb05hODVHVXJPYS9LNm00RWtkYVFoNndWOW5ESGJSN3FEc2ZjeFhrbE9IVmRRcmRZSjluU1cxRlFHT3NqbWR6b3pjU2dXT2ZHYjg3ZmlkejVaSE1XWEhheElrM1dQQVh0YTdPSGhJejlxbkxLamNwalEzTVR2YmFqR2EiLCJtYWMiOiIzYjkzMDFiZWI3NzY2YjkzZGQyY2RhMWRmYjRhMjVjZmIyMjg1ZjJkOWM1MTJhMmYwMDRkNTZiYjJlYmI4NzA0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:27:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405358299\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1835544203 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yyK2cKDsXLqFKZ58EWYA7ppOefFx790shA1YfsRy</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835544203\", {\"maxDepth\":0})</script>\n"}}