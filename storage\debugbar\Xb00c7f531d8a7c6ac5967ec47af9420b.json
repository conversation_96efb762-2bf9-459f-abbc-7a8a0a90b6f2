{"__meta": {"id": "Xb00c7f531d8a7c6ac5967ec47af9420b", "datetime": "2025-08-02 09:13:51", "utime": **********.362612, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[09:13:51] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.333323, "xdebug_link": null, "collector": "log"}, {"message": "[09:13:51] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.340468, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754126029.737582, "end": **********.362773, "duration": 1.6251909732818604, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1754126029.737582, "relative_start": 0, "end": **********.135782, "relative_end": **********.135782, "duration": 1.3982000350952148, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.135824, "relative_start": 1.3982419967651367, "end": **********.362784, "relative_end": 1.0967254638671875e-05, "duration": 0.2269599437713623, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46544336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3054\" onclick=\"\">app/Http/Controllers/LeadController.php:3054-3110</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01109, "accumulated_duration_str": "11.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.290478, "duration": 0.00811, "duration_str": "8.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 73.129}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.325457, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 73.129, "width_percent": 15.329}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3081}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.334064, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:3081", "source": "app/Http/Controllers/LeadController.php:3081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3081", "ajax": false, "filename": "LeadController.php", "line": "3081"}, "connection": "radhe_same", "start_percent": 88.458, "width_percent": 11.542}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-1707145990 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1707145990\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-800347218 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800347218\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-242663722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-242663722\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1556628742 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkNhTVNFek8wUlF6T1hDZ2JSK1FEOWc9PSIsInZhbHVlIjoiWGFKdXVESERzVDFZekhmN0JlbXlTZjNYMERzSXVxSk9vd3ZTVkhqeXNFcG5jQjBlYnltWTVwajFWaDB3a1JGanIwLzZPS1NSZHBLeHVrWEpGMVFUb0VodVA3WEV6QTh3ZnhMQUg0d0RqTS82ZytyS0s5NmFGbG42UzZraCtmQnZ3YUhmUzNhWDRVbjBiMU5FbnpTdDlOTndQcldNTExIL245aHl3aFJHZDVzNkQvNW1RNGhOVE5BUVRPSHBXeStjZGc5K0lTc0ttMk9yVS9wRXhwaytvNi94SEdseWtxZFR0UkhROEtNb2MvbTZ5ei9LcXROTDFmeXVqZVBwZDROMTkvVWNhY1IyWUgydU5rL0psbDBLTFlPRXVPVFJYTXpEL1VhOFFLdFBjNDFtRTdrVXVmUnlsTzJYUnJjd3Bid0pPVThReXU5ckdpT0Z4UG9SVWlBMWV6VTBsOGNBNEplaGk2SVdqRVpvdHlzRjI2S1VzSGV6VjJCc3NiRDhBSnNxa1NCYzRKRUJLU1Z4L0MrZGpURHdXYTlPb1dDR3VrM21UMnB0WjFMUVJsVjlGN3ZFSzMrRDJqOCs5a1llTWc3Q1hIMEtiTnNndkJ2ZW9vTUZYYmlzNWFTMndOdlY2bmFlM2NBLzYvUTlrcEhmeWpXbmNieXd3bWNMODNpMTZtRUkiLCJtYWMiOiJiMTQ0ODVmODdhNTMwNDFiNGVlNjcwNmVmZTllMjNhMGUwM2Y2N2NmZjc2NzgyZTMxNGM0YjRmMTkzNjYxZjE0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlpIdkYwZmhwenJaaWw3RGp1d0N4YWc9PSIsInZhbHVlIjoielc0K0Q3cklmeUxSb3VhR3BabzUxK2pGSnJKNU9kS0NsRFM4TUQ2K2kyOHFRK0N6WG1CaFZod3BmZkRmWllxQjBGWC9BUis5amdDbUtDVHF5MVF1bUxoenY2OTYrV0xnYUh3UmVMWUNCSlpxNmx5VW9aWDRlTWl4K0NYQ2VIY2hJbTdqY2ZCVmFDN3Vpb1NVY29NemJYTzErNk85R0lBSEdCaExtVnUxNnN6Q0h2cTBDTW10VjNsQWlRbGNjNk9mTmpPYkMrSDJVRXYzRWJyTzk3WmMyNGlLUzV0TnUycVc2NGFFMy92MzdsaTBMaHF4Q3FwQ1lsM0ZRM0dVNmMxdExVRjFXb3lqcTFwdEZ2ZVh5US9yZkthNjVHbWk0NmpPYTZTemg2Tmg2WWp2MW1ZcENkeEhuK0oxbzBmQi9EclY4Q3cxRVh3eEpKU1BETE4rK0ZSc2J3WnFpZFE1OEdYeE9LajJKN2U1RmpDRVNRVHpncCthemdFaHdXY1hEOGNCOVhUSW41aUs2SDNRVFBkajYzNkU4UEM1T2dYOXlMNk9JWllTY2JZRlJ4RlQ5NVpLY3dZK1Z4TWVyNmcwNnpnYUpkVGh2Qm52djV3K3EwWG5TNWd6dUt4cTVrNWc5clZUYXNTZWZ5WCtIaEVWUzVmcGRqQ0NhQUgwa20vMzE5MW0iLCJtYWMiOiIyYTkwNjMzYWM3ZDAwOTM1ZjE2MDk3OWUwYzllZTM4OGE1OWM4NmEwZGI3OWE2NWFjNzk2ZjRmYTJlY2E3NTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556628742\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1002855600 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002855600\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1233126146 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:13:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1YbTdYOWxrWVV4VjNNc1B2cnR2b0E9PSIsInZhbHVlIjoicWZCcUhOSmgvblNMY2JXS0FmbVkvQXo1RUJKWUtONXJad1dDSjd3VFBSZyt6OVlUZ3J6OEx0bDBRZE5tK09jRUt6QWRvZTMrcXdWMEZXaHEwY3krMHJsOUpGSCtEOW9NN2NhcHhSenVmMEE1VXh1cEh6c0Fqc0l4N3RkSGJQZndtcjdWaTJadWFnK1J2ZUdFRFZFTUxRN1htSFRweDBKUDV4d1BaR1hCd2c4RTJIb3VkMmFtYWVGV1ZjUjdWNlEzelNtOWp1QjUrRlBwdzFsMWZMTzJ2bmFRS0s5c0FWampZaEFwbW0xZGx2OEJwb3U1M1hhYmJuSEEyZzgxc3RIQW5UUUJwT3lmQjZRaGdPMFdUcnFhMTduQ0NYQXlUUWQwaXNuTXBRSzRIN0J0bi9ReEV4WGh2bDlyWm1LcTV0a0VXaDY1REpxYVZoaFNVN2pkYTZEZzUwWU9lMGRHRm43bzdpUXpCcWptbC9EU21HT25NejRrVXlzSlJwNFNrcDlNRktkUStOaU9QbEFteFZTWFFqcTMrZm8zQ213RHZObW5CZi9ZQjV6eHZpYzZSZjhXSFdtL1dkZ1QzdE0rNWRodWpTM2ZzYTNJOUN3N3hpcnZyNEFIdTFjQVNOOFZUbTRBejgwWUo0aTRYSDM2NkhaZmZ2aktBUUdoUlhVOEYvV1IiLCJtYWMiOiIxYmIzYjM5NTViMjVmYjViYTlmODVhMGZjZDM4MTgyYWNkNmI2MDVkODkzY2IwYWIxZjFlZTViZTQ3MjFjOTA4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:13:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlNnU3B2Uk5GOUdLSUxLZkZCRXJKK0E9PSIsInZhbHVlIjoidlVYdmdHS3o4NE1sMHJad3VFM1VmNXVCbjRTNklsZU0yTlhJN0M1Yk1DSDZWaUUzWmJvdmJERitGUXkxT3VaSmluZnhaT3llRE9TTnAxck5XOXlBTCtIdlRDYzU0OGNNQ1BmVmYvTitickM5ZWdBeE1qWnFBTkh4N2hyUERwbnd0NGZjUStWWUUxVDdqUlV3WDZsNzIwNjZhRmhUQVdZZVBCTHQwdTVyWGNJMTk2UWhmNk81YUJFdVdyNDRrUWcxQnZvSE5IMHkveFlMend1T1lTbWZCS3dVSnhodjBlOEwwR1NIRW1DY0xYcEN2eDBHSnEyNFBLUis2Yy95M25WNHNMMDdaeEJ0OXhFbStCM2U1TVNyZTc5V0xVWGc0bWNKYzZGVk1FWGpJOVhlbmcvWEpURWVhUVd5YyszN0ZYMkdZam9BL09NQnNoNmJFTlRoaExkVXR6RXNJTG5sdzRPQ0MzQ0xFdUl2M0hhQTl4QzdhcHBHazlTcVlIaVo2dzRWZjV6NGlQSWhiSUl1ckE4UUZMUFBhdnZ3enN1TXVJcHJhakpESGhEemRBaUhzdmZZVVVJMGU5UWtaYkxUaHlrdW9SQVQzaktDK0tiRjBiaVg3MFBISG9nR2c4b1VWbmJRZmUyeWh3VGZ4RHFEMitydHNqSHpMd25uM3JWSGRhMlIiLCJtYWMiOiJiYzI3MDczYTNlZjhlYzIzMmI0ZjRlMTJjYzAwZGY3NmQ1MzNmYzI5OGMyMjkxNGI5MTgxY2UyNWFkYWZlZDA0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:13:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1YbTdYOWxrWVV4VjNNc1B2cnR2b0E9PSIsInZhbHVlIjoicWZCcUhOSmgvblNMY2JXS0FmbVkvQXo1RUJKWUtONXJad1dDSjd3VFBSZyt6OVlUZ3J6OEx0bDBRZE5tK09jRUt6QWRvZTMrcXdWMEZXaHEwY3krMHJsOUpGSCtEOW9NN2NhcHhSenVmMEE1VXh1cEh6c0Fqc0l4N3RkSGJQZndtcjdWaTJadWFnK1J2ZUdFRFZFTUxRN1htSFRweDBKUDV4d1BaR1hCd2c4RTJIb3VkMmFtYWVGV1ZjUjdWNlEzelNtOWp1QjUrRlBwdzFsMWZMTzJ2bmFRS0s5c0FWampZaEFwbW0xZGx2OEJwb3U1M1hhYmJuSEEyZzgxc3RIQW5UUUJwT3lmQjZRaGdPMFdUcnFhMTduQ0NYQXlUUWQwaXNuTXBRSzRIN0J0bi9ReEV4WGh2bDlyWm1LcTV0a0VXaDY1REpxYVZoaFNVN2pkYTZEZzUwWU9lMGRHRm43bzdpUXpCcWptbC9EU21HT25NejRrVXlzSlJwNFNrcDlNRktkUStOaU9QbEFteFZTWFFqcTMrZm8zQ213RHZObW5CZi9ZQjV6eHZpYzZSZjhXSFdtL1dkZ1QzdE0rNWRodWpTM2ZzYTNJOUN3N3hpcnZyNEFIdTFjQVNOOFZUbTRBejgwWUo0aTRYSDM2NkhaZmZ2aktBUUdoUlhVOEYvV1IiLCJtYWMiOiIxYmIzYjM5NTViMjVmYjViYTlmODVhMGZjZDM4MTgyYWNkNmI2MDVkODkzY2IwYWIxZjFlZTViZTQ3MjFjOTA4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:13:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlNnU3B2Uk5GOUdLSUxLZkZCRXJKK0E9PSIsInZhbHVlIjoidlVYdmdHS3o4NE1sMHJad3VFM1VmNXVCbjRTNklsZU0yTlhJN0M1Yk1DSDZWaUUzWmJvdmJERitGUXkxT3VaSmluZnhaT3llRE9TTnAxck5XOXlBTCtIdlRDYzU0OGNNQ1BmVmYvTitickM5ZWdBeE1qWnFBTkh4N2hyUERwbnd0NGZjUStWWUUxVDdqUlV3WDZsNzIwNjZhRmhUQVdZZVBCTHQwdTVyWGNJMTk2UWhmNk81YUJFdVdyNDRrUWcxQnZvSE5IMHkveFlMend1T1lTbWZCS3dVSnhodjBlOEwwR1NIRW1DY0xYcEN2eDBHSnEyNFBLUis2Yy95M25WNHNMMDdaeEJ0OXhFbStCM2U1TVNyZTc5V0xVWGc0bWNKYzZGVk1FWGpJOVhlbmcvWEpURWVhUVd5YyszN0ZYMkdZam9BL09NQnNoNmJFTlRoaExkVXR6RXNJTG5sdzRPQ0MzQ0xFdUl2M0hhQTl4QzdhcHBHazlTcVlIaVo2dzRWZjV6NGlQSWhiSUl1ckE4UUZMUFBhdnZ3enN1TXVJcHJhakpESGhEemRBaUhzdmZZVVVJMGU5UWtaYkxUaHlrdW9SQVQzaktDK0tiRjBiaVg3MFBISG9nR2c4b1VWbmJRZmUyeWh3VGZ4RHFEMitydHNqSHpMd25uM3JWSGRhMlIiLCJtYWMiOiJiYzI3MDczYTNlZjhlYzIzMmI0ZjRlMTJjYzAwZGY3NmQ1MzNmYzI5OGMyMjkxNGI5MTgxY2UyNWFkYWZlZDA0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:13:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233126146\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-529860110 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529860110\", {\"maxDepth\":0})</script>\n"}}