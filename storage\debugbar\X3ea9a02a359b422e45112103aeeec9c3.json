{"__meta": {"id": "X3ea9a02a359b422e45112103aeeec9c3", "datetime": "2025-08-02 09:55:35", "utime": **********.727457, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[09:55:35] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.704127, "xdebug_link": null, "collector": "log"}, {"message": "[09:55:35] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.711402, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754128534.288907, "end": **********.727486, "duration": 1.4385788440704346, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1754128534.288907, "relative_start": 0, "end": **********.553114, "relative_end": **********.553114, "duration": 1.264206886291504, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.553142, "relative_start": 1.264235019683838, "end": **********.727489, "relative_end": 3.0994415283203125e-06, "duration": 0.174346923828125, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46525112, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=2990\" onclick=\"\">app/Http/Controllers/LeadController.php:2990-3046</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01338, "accumulated_duration_str": "13.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.655513, "duration": 0.01051, "duration_str": "10.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 78.55}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.692008, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 78.55, "width_percent": 12.706}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3017}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7049332, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:3017", "source": "app/Http/Controllers/LeadController.php:3017", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3017", "ajax": false, "filename": "LeadController.php", "line": "3017"}, "connection": "radhe_same", "start_percent": 91.256, "width_percent": 8.744}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-938261539 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-938261539\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-380931005 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380931005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1224104889 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1224104889\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-814214843 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InAvZ3BrcXluRkRVS0NKdDRMUHlENUE9PSIsInZhbHVlIjoiZVRSZmtYS0dBNU5STU5qN1ZQSys5WUYxdHZxSllmdkJZOWx4dzFvS0NxOHBEYmVSekxZb25EQzRMakxqR2Q1OUxQb0hUSCs5eE5OUUxYU3Z6dEZOdTBsbnA1ZzlhM3lLMTlzTzBQVCt6WTVOYnozMW01Yk9WSzA4bSs2b1Rld3lJQmFkaDNNYmU0Q3ErY3N0Q1JzdjE5aEJrOEJiV1JMN1NrWWZuWGpsUFhaaTZoWngrbWtkcjZYN1F1ejVrVlJrKy84a1ZWdFJzQkZYb2ZveFZOM1FmNG44L0dzcVVmN21POVJzSld3aWdKL3N1QWtoRHFoUTlBWFAyZ2lySGRFTkU4OHYyU0dSZVJnVmwvUmVUZ1JsYWZJM0JRdldkT1M4c1Q3aFJ4Um5YbDJqWGx1TXRSL2Rzb2QyUi9sMGJTUmkwWGdtVDZ6cUJ4VC9CbVh1TnEyUnBtM1l2UENodmhFVXJRMFRrVGRHK3U4VmdiUUNuL3NzNUR6MmlwYkFyamtMdG1pbFlkVGhKdk0xSmpPekF6WTArWHF6dHVLRkZpZklxS2lyTEMzcHJrNHFwbWdPUGp3cnVtR2sxL2RNMC81M2phV1FaWkM4WEJoQmdKTHZURWlPbk5EdmllZ1ZqOXkwVzJvVGN4MjlQZXdFVUxKMmpZR0VlODdEY2FVVlFmVzYiLCJtYWMiOiIwOTZhZjU4MjJmNzljOWZlZTM4YmI3OGQ1NGIzNWMxNWVhNGE4MmVjZjk2MDA1ZGZiNjg5NTU1ZjZkOTIzZTBkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlE3MnJWdTQxVGQxcFYvbjVEY3RSTGc9PSIsInZhbHVlIjoiZ2svWm1wNXZhWWdkWEllLytFZzdUYmxhZFdTWkZJc3lmUGhmWmdGZi9SSFVmZGk0eVlGNmtzODlDVG9aVGk4YTlDdHE0UVJ4eE5QZVlRVlhFZ1hRaVNQTWl4QU5OeTg4QmNLOG1PTlBxYmk2QTdlYUx5Y1RscUNXK0pUWXMzckJ0YmRERnFCeHhvSEkxQkVocGkraWs2R2JLaTUvbFkyK29kdzBvVTNrYlpDWVJEaVZYOXhyK2RGWnFrVXJFa0hxUC96U3RxeTRHd2RuclBRaWdGUlgvRGhsaTFIenA2aWs3RVp5NVpYcERGa0hoTk5LRUpUb2Qvdy9LMkhLalNNelZ4L0hDVEpYS2lYQmVMOTZwM0d2dDVQWVkzTTBCN3BnMjNJTkN4Nk01U3FXQUExTzNiMXppUndVaGFQME93Y0I0U1VWVDZobExBY0RiMEI0QUs1OEdoZ3JRQ2lOZXhYWDlBRENKQWlqWnUvcXBTbXpPQzM1N0wxSmlUencraUFkeWtzWGZHamZBSm9FaVFmSE84L09tcGFHcmg1VjVrNzZWYlkrQ2FzN09KYU1DOHRpczBiTHRwRUxJcDg2OWU0K0QxTW1MUEdVYS8rUDFaaWZVRkdOVFNKUGtsdXdaSnhHVkxUcjkvTUt4WllVUmJaaW1Yd3FieTU2djNhY2VRaSsiLCJtYWMiOiIwZDIzMzZiYTM2ZjY5MDU5MTYzYTFmYzZmNjJiMmVkMWJkNTk5NDAwNTUxY2UyZmNmMzNhYzg2YTI0YWViNzNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814214843\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-301878723 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-301878723\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:55:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxMMEgwY3Nta3hNQUJiaHBlZ2tkWEE9PSIsInZhbHVlIjoiVDVXU0RYWHVwRm15bG9LQzFQK21sNnNhVUZ0c1B0bE9YY1U5RHBTK1lucWI1M0RsaEduK1hTS1JRQ3pJWWZrMDcxNkdSYTFLVzFyckRsL0dDWWJuSCtmRXpKRE01a0F1SWdVVGcwUUZOQWoxd1R2K3Z2OGt5c2dlUnFVR0U2T1VyWVc0RG40cnJPa2FYWTBZaDVOVjlBdXpGc2ZBUUtWNGs3MnR0THVjUExNTHFIT1FmUDVRWmg3YVZUSTdRWHcxOHk0U0c0b2JDQ1kxTlJOR1lIRkVtWWdlQ0xXTFZBU1Fma25RWDV2eFJ6aFk0c1RjSFp0OThHUVRoV3NlUHNpeE9PQ2Zta0lKWEU4WGlvTmk5c1l6OEdOOGRGOUFPUzFTWHBKY2JuWFNzTGdseThzaXhCaTNRQXpRaUJEMXpkOUE2UUtGTFEyV2JXVnRNSGl0RjlKejVFNWo1aTZmdkQxdnU1YmxXeXFjd3d3MGtMSUIzaG9BNjdmWTRjSUVnWlFGUHRFYU5ycVhSb0h3WUhMUEUxR1UwK2o1T2d5RXpVU0QyUk5kNkhIM3o0K3E1MS92S1NwQXFYSzIyWTlObEJrbnZadHZneTFKUk1RSS9oRjV5UE9qaDluajJuNnNQRCthTnVuYVVJb1dNRjFmdGZmRGF2bUtXZXpVeE42SWM1RmQiLCJtYWMiOiJiZmM3NGExZGEyZDhhMjdjNWNhYWI2NGVlOWEwZjJjZjQ1MGFkMzQ5MmJhYTQ5Mjc4ODk4NTEzODczNjczOGQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:55:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkxlcndOMENyWmIzc3ZjRW9xNXk5N3c9PSIsInZhbHVlIjoiKzd3Q0ZCZmFvUVRnS2FSR3JPWEw5VVk5OHgvNlVqUjAwZk95Wmx4S0pUOW5WRXJBSHNBUVBRTFNFcUIzWFhwTlhIb0NrMjhzd0pSYjlpR2xlODdMSFRGTTg5UmNiUnBrbUJCaUNxbkN2NmgvZllWZzhlNGkyQkVIdU4rRUhUVkhvbVhNcDZaeDllTXRsc1B6ZS9lRHVhaWFGMzQvem9yWm5QTVJ2OElXcG4xdGNrd2c0bm1qdy80UHdoTHpKSWYxeUpzc2hIcUlBWUQrYjVZVjY1dEc1dHErN25SbXZSSEYzejhhSFptNzUzWWljNmdBNWtIbWViUWIrNXY2aEcvWnFKZGlFU0dDT0RFM3ZVWndFUEJ6Vm1iVjF0bWx6S3pmbVBOeW8waVlmNFRrUGFjUzNZRWJkZEpOcnI4U2IzRVZ4enRBNlZPTjl6SUhQUUVSbG9QYUxMaXRBS3hhWGVpMUQzV1FVcnhURHdkTWV2cmhkTjcwT0xmNVR6Zk1udnhMQlBsWjZVeVB0c3JVZWE1aFVwdDFVV3ZoK3dyYVdEekNHV2ZOKzh4QnVobEhJOC9EZGdDd2thQTd0M0I3a2xmVXVjNHhMYk80d2xxSXllNWpFaUpqMmQwY0d3akgrU1M4cEsvcUJ6SXVNbjl2Z0RNanhMNm53QWcveEhIMUREdFYiLCJtYWMiOiI4OGRiOGFkZTJiYzgxOGJlNTM4YTVhNzZiOGQwNTE5OWRlZTE4M2VlY2NkZTEwYjIyMjU3YWY2NjEwMmUxMzRhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:55:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxMMEgwY3Nta3hNQUJiaHBlZ2tkWEE9PSIsInZhbHVlIjoiVDVXU0RYWHVwRm15bG9LQzFQK21sNnNhVUZ0c1B0bE9YY1U5RHBTK1lucWI1M0RsaEduK1hTS1JRQ3pJWWZrMDcxNkdSYTFLVzFyckRsL0dDWWJuSCtmRXpKRE01a0F1SWdVVGcwUUZOQWoxd1R2K3Z2OGt5c2dlUnFVR0U2T1VyWVc0RG40cnJPa2FYWTBZaDVOVjlBdXpGc2ZBUUtWNGs3MnR0THVjUExNTHFIT1FmUDVRWmg3YVZUSTdRWHcxOHk0U0c0b2JDQ1kxTlJOR1lIRkVtWWdlQ0xXTFZBU1Fma25RWDV2eFJ6aFk0c1RjSFp0OThHUVRoV3NlUHNpeE9PQ2Zta0lKWEU4WGlvTmk5c1l6OEdOOGRGOUFPUzFTWHBKY2JuWFNzTGdseThzaXhCaTNRQXpRaUJEMXpkOUE2UUtGTFEyV2JXVnRNSGl0RjlKejVFNWo1aTZmdkQxdnU1YmxXeXFjd3d3MGtMSUIzaG9BNjdmWTRjSUVnWlFGUHRFYU5ycVhSb0h3WUhMUEUxR1UwK2o1T2d5RXpVU0QyUk5kNkhIM3o0K3E1MS92S1NwQXFYSzIyWTlObEJrbnZadHZneTFKUk1RSS9oRjV5UE9qaDluajJuNnNQRCthTnVuYVVJb1dNRjFmdGZmRGF2bUtXZXpVeE42SWM1RmQiLCJtYWMiOiJiZmM3NGExZGEyZDhhMjdjNWNhYWI2NGVlOWEwZjJjZjQ1MGFkMzQ5MmJhYTQ5Mjc4ODk4NTEzODczNjczOGQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:55:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkxlcndOMENyWmIzc3ZjRW9xNXk5N3c9PSIsInZhbHVlIjoiKzd3Q0ZCZmFvUVRnS2FSR3JPWEw5VVk5OHgvNlVqUjAwZk95Wmx4S0pUOW5WRXJBSHNBUVBRTFNFcUIzWFhwTlhIb0NrMjhzd0pSYjlpR2xlODdMSFRGTTg5UmNiUnBrbUJCaUNxbkN2NmgvZllWZzhlNGkyQkVIdU4rRUhUVkhvbVhNcDZaeDllTXRsc1B6ZS9lRHVhaWFGMzQvem9yWm5QTVJ2OElXcG4xdGNrd2c0bm1qdy80UHdoTHpKSWYxeUpzc2hIcUlBWUQrYjVZVjY1dEc1dHErN25SbXZSSEYzejhhSFptNzUzWWljNmdBNWtIbWViUWIrNXY2aEcvWnFKZGlFU0dDT0RFM3ZVWndFUEJ6Vm1iVjF0bWx6S3pmbVBOeW8waVlmNFRrUGFjUzNZRWJkZEpOcnI4U2IzRVZ4enRBNlZPTjl6SUhQUUVSbG9QYUxMaXRBS3hhWGVpMUQzV1FVcnhURHdkTWV2cmhkTjcwT0xmNVR6Zk1udnhMQlBsWjZVeVB0c3JVZWE1aFVwdDFVV3ZoK3dyYVdEekNHV2ZOKzh4QnVobEhJOC9EZGdDd2thQTd0M0I3a2xmVXVjNHhMYk80d2xxSXllNWpFaUpqMmQwY0d3akgrU1M4cEsvcUJ6SXVNbjl2Z0RNanhMNm53QWcveEhIMUREdFYiLCJtYWMiOiI4OGRiOGFkZTJiYzgxOGJlNTM4YTVhNzZiOGQwNTE5OWRlZTE4M2VlY2NkZTEwYjIyMjU3YWY2NjEwMmUxMzRhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:55:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1121038585 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1121038585\", {\"maxDepth\":0})</script>\n"}}