{"__meta": {"id": "Xff61b62becbf2549d57b490ec4bdefbf", "datetime": "2025-08-02 09:12:20", "utime": **********.180658, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754125938.75705, "end": **********.180694, "duration": 1.4236440658569336, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1754125938.75705, "relative_start": 0, "end": **********.101281, "relative_end": **********.101281, "duration": 1.3442308902740479, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.101306, "relative_start": 1.****************, "end": **********.180697, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "79.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tKlhEXiheBRvtNnSDjeNmLbkqcQj0EOL43B9eLpS", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1061161322 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1061161322\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2092488532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2092488532\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1894268195 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1894268195\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1492068084 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492068084\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-130186100 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-130186100\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1979661353 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:12:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB0WWZZeHlhY2F4K0kzL1R1eUZERHc9PSIsInZhbHVlIjoiV2VlcWRxQUE5R3crWFdYaUJ2SWYxTjlpd3JnRXJwRHdsTk50Q0F2cEIwSFNwNEV4aVBOYnZtZUlSYnVDWlRNQ2FKQ2RTTUFBelFSUERxU3FjWEJLb0RIclVIaHo2RmlrS2p5T2JHbHMydkJKR3YxOC9MT3czQitRNEVaVGlKaEdkRVJEYzBYVEdKeUQ1WE02RlJpSXZJTGFvbXhqYS9BQndlNFpnTEtDcFdEZUlzWVIyQ0VxbHlyZlFSSVhMcW80MUtVMWxjSFJoSlZQK25neWtvMW51aEZPVVk5WnYvQmpKd2poMWFuZjBOd2tWYWkwbHAyRFZ4RHp0R1ZkaGJpV01pVDBVRUx3NmhFR3VqRzF5MzYrczdsK1REdmk4Z3MySVBGeS82QnlubzRsejdNVnNNUkh3cWtWUzRjT043bnFiZkxOV3lqRGhrbEk0VkZFSTM2UFM4WTdCVkZ5dThUVDk4TFhUSVl2VzN5ZGREQVVWZzJkT2hmZGdoY3c3Rm5Zcm5udVdRaC84bWx5aHhZTVpZL1dYTGJvT2hrdWhCTndEaW1hUUM1R3ZscE83aitUNkQ2TGlFS0ppTUlGN2lDelRVRlFkSDdHMXBNbmlSU1VWMGFxM3FHS3VsYm9ySmRNYldRSURpU3FOcWRJcVhnTkRtTXo2ME9UdmIzRTIwSksiLCJtYWMiOiJmYThiYTY4Mjc0ZThiMmQ2Y2UxMDc4MGE3YjgxYWY2OTJkMDFhZWNhMDZiN2Q1YmYyZjhkN2I1M2ViM2UyYTkyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:12:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImxreFlCYjRVVnRzK1ZXM3R1L2g2UXc9PSIsInZhbHVlIjoiakxhS0U5QW5NYklpOC9XYVUxVnFISkhFWkZpN3REZXZpSDJ2ZXNOd0IrVUtvVnI5SUFyMFhlcDRpN1hSV0VremM0RDdreWtlaDBtRnkvTEhuOWplbGlHWW1iODZmL2xDQmtpWVRVL1RWWlFxNlJ1UENMMm5wU3QrdUkvbnFlVGEyRzVwVDVzdTRNM1R1TkZocHB6UDA5NUtNNmtWVVlqRlJrMWxreEpweDh6bTBoK2QvRWh2Z2Y2ZnNPdHpnRzdhYVlVRDJJdmVxRUk4TnJRT0Y1YTd0WnE3QXJOTEZwd0dzQmxtaVhsTG5rMUllTjBBbkpxWC9kU2RadHFsRzkrODJuMnh2OTVLUXRFUDB0TUFMQVFJemE4dUUwRytUbXVCSGhwYzdXWm1nQlFRaHEvR3NPejNkcFpuSGMwcERlVU4yZEFBN3hCYW5pVlRCZnNBMjVidG1VbTViYjZpNmVCQjBFbnliTWhyTjg5dEVtdjJoTjZwWEIxNnM1ZmZKTXRmK3ZxL1MvTGZUREU3SU1JaUwwSjBjd2JIcm5CK0RmWUIvd1kxTDU5NkE2QTBtalIyN1JSa2RJK21VT3NYaFpyVzJmWXBIWmhRcnhIMC93UUNZZUxIaTBYQUZPY3ZMcER5VkZIOHlZa2FqbU4xTlFxOE5hdFZyNWJHY1pkNmFiUGQiLCJtYWMiOiI2Nzk0MGM0ZTczYWY1Yzg5YWU3NjIwYmU2MGJhNGE0MTg3OWY3MGVkNDI2ODJjZDNiNjE0ODQyZjhjNDgwMzg0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:12:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB0WWZZeHlhY2F4K0kzL1R1eUZERHc9PSIsInZhbHVlIjoiV2VlcWRxQUE5R3crWFdYaUJ2SWYxTjlpd3JnRXJwRHdsTk50Q0F2cEIwSFNwNEV4aVBOYnZtZUlSYnVDWlRNQ2FKQ2RTTUFBelFSUERxU3FjWEJLb0RIclVIaHo2RmlrS2p5T2JHbHMydkJKR3YxOC9MT3czQitRNEVaVGlKaEdkRVJEYzBYVEdKeUQ1WE02RlJpSXZJTGFvbXhqYS9BQndlNFpnTEtDcFdEZUlzWVIyQ0VxbHlyZlFSSVhMcW80MUtVMWxjSFJoSlZQK25neWtvMW51aEZPVVk5WnYvQmpKd2poMWFuZjBOd2tWYWkwbHAyRFZ4RHp0R1ZkaGJpV01pVDBVRUx3NmhFR3VqRzF5MzYrczdsK1REdmk4Z3MySVBGeS82QnlubzRsejdNVnNNUkh3cWtWUzRjT043bnFiZkxOV3lqRGhrbEk0VkZFSTM2UFM4WTdCVkZ5dThUVDk4TFhUSVl2VzN5ZGREQVVWZzJkT2hmZGdoY3c3Rm5Zcm5udVdRaC84bWx5aHhZTVpZL1dYTGJvT2hrdWhCTndEaW1hUUM1R3ZscE83aitUNkQ2TGlFS0ppTUlGN2lDelRVRlFkSDdHMXBNbmlSU1VWMGFxM3FHS3VsYm9ySmRNYldRSURpU3FOcWRJcVhnTkRtTXo2ME9UdmIzRTIwSksiLCJtYWMiOiJmYThiYTY4Mjc0ZThiMmQ2Y2UxMDc4MGE3YjgxYWY2OTJkMDFhZWNhMDZiN2Q1YmYyZjhkN2I1M2ViM2UyYTkyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:12:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImxreFlCYjRVVnRzK1ZXM3R1L2g2UXc9PSIsInZhbHVlIjoiakxhS0U5QW5NYklpOC9XYVUxVnFISkhFWkZpN3REZXZpSDJ2ZXNOd0IrVUtvVnI5SUFyMFhlcDRpN1hSV0VremM0RDdreWtlaDBtRnkvTEhuOWplbGlHWW1iODZmL2xDQmtpWVRVL1RWWlFxNlJ1UENMMm5wU3QrdUkvbnFlVGEyRzVwVDVzdTRNM1R1TkZocHB6UDA5NUtNNmtWVVlqRlJrMWxreEpweDh6bTBoK2QvRWh2Z2Y2ZnNPdHpnRzdhYVlVRDJJdmVxRUk4TnJRT0Y1YTd0WnE3QXJOTEZwd0dzQmxtaVhsTG5rMUllTjBBbkpxWC9kU2RadHFsRzkrODJuMnh2OTVLUXRFUDB0TUFMQVFJemE4dUUwRytUbXVCSGhwYzdXWm1nQlFRaHEvR3NPejNkcFpuSGMwcERlVU4yZEFBN3hCYW5pVlRCZnNBMjVidG1VbTViYjZpNmVCQjBFbnliTWhyTjg5dEVtdjJoTjZwWEIxNnM1ZmZKTXRmK3ZxL1MvTGZUREU3SU1JaUwwSjBjd2JIcm5CK0RmWUIvd1kxTDU5NkE2QTBtalIyN1JSa2RJK21VT3NYaFpyVzJmWXBIWmhRcnhIMC93UUNZZUxIaTBYQUZPY3ZMcER5VkZIOHlZa2FqbU4xTlFxOE5hdFZyNWJHY1pkNmFiUGQiLCJtYWMiOiI2Nzk0MGM0ZTczYWY1Yzg5YWU3NjIwYmU2MGJhNGE0MTg3OWY3MGVkNDI2ODJjZDNiNjE0ODQyZjhjNDgwMzg0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:12:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979661353\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1306472600 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tKlhEXiheBRvtNnSDjeNmLbkqcQj0EOL43B9eLpS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306472600\", {\"maxDepth\":0})</script>\n"}}