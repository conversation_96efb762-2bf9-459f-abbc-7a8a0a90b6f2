{"__meta": {"id": "Xef0a06b315afc1c72fbf5b3211eb235d", "datetime": "2025-08-02 10:15:15", "utime": **********.435625, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754129713.857808, "end": **********.435684, "duration": 1.5778758525848389, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1754129713.857808, "relative_start": 0, "end": **********.308265, "relative_end": **********.308265, "duration": 1.4504568576812744, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.308328, "relative_start": 1.****************, "end": **********.435689, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "H0XSMLQy9UARjCEB64CC4kGbfK2hqK1KnBoAWpup", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1054798426 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1054798426\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-282111771 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-282111771\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1619836786 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1619836786\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1961202051 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961202051\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1235645035 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1235645035\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1419174411 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:15:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijc2ZnhFLzViQ042ZHNBRi9SWWNwOEE9PSIsInZhbHVlIjoiREdMNkNMaGJGdWI4Y0tqRWpmdTNaSm56aURoTDI5T1NPQjRFek1lWGt4QW9kaDMwN3dQOHQ5VDJ2aTUrNUkwWVFhU3BvcmliZHN3Y0xrOTJNOUNxdnZpWmZCYTdhZy92WlROVHJkUVo4N1MvSXpORUJSKzVzUU9RMy9heTc0NUg5bFZ3NHAvcE5UZ0wzTlEwZWhOa2ttRXpPa2xjRDZvY0k3bnpWU20vNTR4ZE9MWUl1eFcxZnVjYzJReFU1SzhoeE03WXBPMzUvd1BhWS8zTUhpd0hRWForU2dFTDNGYlVMdHpYV2NFUmNUaTRmSXBjU2x1UkJiMjdwT1Y4QlRJaThYWFRmZEVGUEpSdnRNOVRuaUNaSXZUeXlidGRvc2gzS3Y4dW50RE5pdUMyMDJVUnVPYWljTko0RUtYUVY4UHAyelNVbEhibSsrZU9sK3BGdmhITXAvUjVxNlh0aVNYMWZUanRaeXR1azVicDlVdGF4b2VBNkprTWVoUUxmVXJNOU5JZW5Vd3d5TjZiSk1DQ2RQYXVIbHM4Q1FFUC80bExKVG8xc2dXZ0FjSFdMVEdWZHlDM0J2blNWMEpNRTBZbDdkUzBORkk2WHY5SXRyY2FYaDdpSDQzeGhEaHRUQTFsczNFYzlSTFdPYWVoOEhkSFFHaTJtNk1LZnlpdzNDdEUiLCJtYWMiOiJhZGU1MTEwOTFkOGI5NjVlN2UyMTMxOWE3NDUyNzNkNDA0ODZhOTM3YmMzNmIzNjY0MjhlNWNiMGJhYTFmNDhkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:15:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlNudkc2cDczemFVb3pyaUdndXFoc1E9PSIsInZhbHVlIjoiZDc2MXR3RzZhelJyemJkamNqUmpkMGhWN3ozNkM3UWxDRDVNNE1MemcrU0xtOUNCbGsvckx3WER4NW5ycnl3MHI2SS95ODZ6MTV5aVRRVktBV21hcXVmV2d3VDRKNGxKSGRTUVFYdXFJV0N2d3p3OTJnbnpGRERkMFU0N3R2L1AxeUs3dS9MQng2QllyUnpLRWZ6dTcxR1RESDJqSWFUYzYrVXkxUForV0pab042eTN6TXBDM0ppWHJrKzhJUUdzNmdOVEhBVlhQNWhsN3g0OW5jZ3JpUWJ3ZzhhRUZUdnZJYm1xbUNOcWZwbDhUaWNvQVpoUU5mczdIMWVNSm9IaWRwZzkxY3dBM3pIQVRHZ3hFakc3bWw4UkZyaWcya0MybGo4KytrSzJoamhWekxia0QyWXF1eSt3M0ZWTmh0OHNLS0d6b0F1dG80OGRxRWl5OFQ5WlVodm51RWY4cTU1bzEva1pwa3FwR3dma0RISXlxRVJjNEhJZTA1RjA2clp5bkN3eXdaQVNMOVZ1QWYvSXcvRHRLbE5nQTBOWXFUaWxoaVFPeWc5WW1LWG9GSWZpR1ltN3NGamNvTENFWFhLTkFDellSYUpuazNhQWNXcEJnV003ZHZpV3VJS3hsa2JUZmRBR1hvR2pLVlNEMVo1cnpsTkkwYUtJL041NmdpeDciLCJtYWMiOiIxOTVkNjBiZDg5ZGMxMDZjNjViZWIzMWVlNDU1MGIzMDk1YzFjM2QxOWNkZDA5MTc4ODFjZjZhZDQ5ZWFjNjNkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:15:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijc2ZnhFLzViQ042ZHNBRi9SWWNwOEE9PSIsInZhbHVlIjoiREdMNkNMaGJGdWI4Y0tqRWpmdTNaSm56aURoTDI5T1NPQjRFek1lWGt4QW9kaDMwN3dQOHQ5VDJ2aTUrNUkwWVFhU3BvcmliZHN3Y0xrOTJNOUNxdnZpWmZCYTdhZy92WlROVHJkUVo4N1MvSXpORUJSKzVzUU9RMy9heTc0NUg5bFZ3NHAvcE5UZ0wzTlEwZWhOa2ttRXpPa2xjRDZvY0k3bnpWU20vNTR4ZE9MWUl1eFcxZnVjYzJReFU1SzhoeE03WXBPMzUvd1BhWS8zTUhpd0hRWForU2dFTDNGYlVMdHpYV2NFUmNUaTRmSXBjU2x1UkJiMjdwT1Y4QlRJaThYWFRmZEVGUEpSdnRNOVRuaUNaSXZUeXlidGRvc2gzS3Y4dW50RE5pdUMyMDJVUnVPYWljTko0RUtYUVY4UHAyelNVbEhibSsrZU9sK3BGdmhITXAvUjVxNlh0aVNYMWZUanRaeXR1azVicDlVdGF4b2VBNkprTWVoUUxmVXJNOU5JZW5Vd3d5TjZiSk1DQ2RQYXVIbHM4Q1FFUC80bExKVG8xc2dXZ0FjSFdMVEdWZHlDM0J2blNWMEpNRTBZbDdkUzBORkk2WHY5SXRyY2FYaDdpSDQzeGhEaHRUQTFsczNFYzlSTFdPYWVoOEhkSFFHaTJtNk1LZnlpdzNDdEUiLCJtYWMiOiJhZGU1MTEwOTFkOGI5NjVlN2UyMTMxOWE3NDUyNzNkNDA0ODZhOTM3YmMzNmIzNjY0MjhlNWNiMGJhYTFmNDhkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:15:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlNudkc2cDczemFVb3pyaUdndXFoc1E9PSIsInZhbHVlIjoiZDc2MXR3RzZhelJyemJkamNqUmpkMGhWN3ozNkM3UWxDRDVNNE1MemcrU0xtOUNCbGsvckx3WER4NW5ycnl3MHI2SS95ODZ6MTV5aVRRVktBV21hcXVmV2d3VDRKNGxKSGRTUVFYdXFJV0N2d3p3OTJnbnpGRERkMFU0N3R2L1AxeUs3dS9MQng2QllyUnpLRWZ6dTcxR1RESDJqSWFUYzYrVXkxUForV0pab042eTN6TXBDM0ppWHJrKzhJUUdzNmdOVEhBVlhQNWhsN3g0OW5jZ3JpUWJ3ZzhhRUZUdnZJYm1xbUNOcWZwbDhUaWNvQVpoUU5mczdIMWVNSm9IaWRwZzkxY3dBM3pIQVRHZ3hFakc3bWw4UkZyaWcya0MybGo4KytrSzJoamhWekxia0QyWXF1eSt3M0ZWTmh0OHNLS0d6b0F1dG80OGRxRWl5OFQ5WlVodm51RWY4cTU1bzEva1pwa3FwR3dma0RISXlxRVJjNEhJZTA1RjA2clp5bkN3eXdaQVNMOVZ1QWYvSXcvRHRLbE5nQTBOWXFUaWxoaVFPeWc5WW1LWG9GSWZpR1ltN3NGamNvTENFWFhLTkFDellSYUpuazNhQWNXcEJnV003ZHZpV3VJS3hsa2JUZmRBR1hvR2pLVlNEMVo1cnpsTkkwYUtJL041NmdpeDciLCJtYWMiOiIxOTVkNjBiZDg5ZGMxMDZjNjViZWIzMWVlNDU1MGIzMDk1YzFjM2QxOWNkZDA5MTc4ODFjZjZhZDQ5ZWFjNjNkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:15:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419174411\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-99224312 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H0XSMLQy9UARjCEB64CC4kGbfK2hqK1KnBoAWpup</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99224312\", {\"maxDepth\":0})</script>\n"}}