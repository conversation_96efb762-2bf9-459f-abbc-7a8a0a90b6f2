{"__meta": {"id": "Xa1a375a8b4aa712dc9bd90048ebf7037", "datetime": "2025-08-02 09:41:01", "utime": **********.053739, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754127659.427139, "end": **********.053784, "duration": 1.6266448497772217, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1754127659.427139, "relative_start": 0, "end": 1754127660.9626, "relative_end": 1754127660.9626, "duration": 1.5354609489440918, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754127660.962622, "relative_start": 1.****************, "end": **********.053788, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "91.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kuHQdU0N8Bo9s0XYMv46ZUIkmhdE6aPTmgW7mkA1", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-17378859 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-17378859\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-946020318 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-946020318\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1439270936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1439270936\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-496096719 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496096719\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-660129391 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-660129391\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1318602719 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:41:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9UODNzdEJZNUxrRWJEaU5MeE5Bcnc9PSIsInZhbHVlIjoiaGZuUTBPSUsvT0Mxdk9GWjZFZTQ5WHdydHVkdFl5UkxWalFkalViWE41R01vWitucWJyRHp6NXpFT3VFSTFiRTAwQVJUYWNCS2gxN2o1RmpndU5oTE1DYThqTGhTblBBNkNGSlJEYnNTbFhPd2RFdU9BUWlYeWw5bFdWaFZhYlZoeklIZ1FydVNIRFdzSnBEa0xaQnJ1UW9jZUFVUC9jVk9keUpQalBRV3g2Tk5vTlA3bkxNZmUzZ0R6b2V6UGdCaFl3dHlwdTEzdVBpampYZkdMTWZJZlZQdUxHL2dwVGZjUXBJZ0RXMjEwQkxsZDVrd2VmeU9yaTNZdGNmUGFhZFdjNGEzRFpUamFBZGZZSzZOZlR3T0Jyc3p4YjkwcUREV0luZjlsZjVBa2N6eGRHMmwwVG9uUU10UTdTWm1XZXVoNnhXYUUrOEErSHgzbVV2ZTFDS29EWGxQcU9sK0xndHlPZm9wVFpvcFB5WHNNakpiVlFhbitNTG53YlhGNFF1NUJCVkx6eVdqYmxRQ3BIVzZwNGxwTngzZlRGdThKWGxVcENuMXZ6d1cvSHgrUUlXd3RheTVMVktyZ1NXQ2ZLbTdxZVRNOUJQWDErc3pDc0toTDRiTEVCWGE4Z3p3cGxPU0FzYTUwYk96aUFqM01qd1R1SlhaTlh3ay9ITWNLOFAiLCJtYWMiOiIyZDliNWY0YzA2OGJiOWM2OWUxOGYwMmY1NWYwNmU4YjM3MTQ2NjVhMjQ0ZTRkNjRiNTFmOTJiYWIxN2Q5M2RiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:41:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlU1OTg2dTZNaHN3NUYrcUJyVEU1OXc9PSIsInZhbHVlIjoiWSsycXhXVTZLMzlMaWNKNVB0b1NabFhMT3dwYktMY1JrcjdPK3F2NGRYSHh5YjhRdjc5a256MzNIZzhFb1FPZ3hNRTkyTm80ZEhBem1naUhRYkdVcGxuMlVNTFc5c3Fsdk9IZFVwVjlwejRiL1lTd1U0SkJWNmFONnlMd1BKSWJ4QzVVTks1NUFFTjB1cGtTOXZKR2RHRVJVcGkySEhRQzErMWpwbTg4NWxRc0VPUVc2QS9kejExb1RocUY1NmI3ZCtrZTFpSlNXdHFOcmY0eERlekJ1ZFpQOWhlMGVIRDZ0S3dSSllZd0FmNlA2RlFoZzY5ZG1DSkl3WkUxR2NSb0dWQ2EvZmZJZkJaZzc1aXY0NnhIQU8wMENDVnFXMDEvVHdGV1I5WlhXNENwcDQvL2ZxVEV3c2RtSDlxS0xZRy84TG83aVZ4ZjErb2tabzZMUElpK3h1SnZSWS9LaVBnalN0UFZQb3hmR0YrcVpzWUtIZGtxbmRNTU11V2ZXWHpobTlRVlhNWUxkaC8wT2p1V0NSdFd5bmoyb3ZZaWNKN3ZBSENqNkM1MmtvVnJETlpPMlJyZVBFTWFYVXp4bnJKRDE0cTV2ZW5XNmlkZ1YyQUlLN0dyNWhzUFVRNWEzZVVWSk81dTBENS8xRmhnSmRHT1JmRHlHSUN6Q3UzR1VVSkkiLCJtYWMiOiI4NGYzZjQ0NzAxNTI2NDJmYWNlNDc0Yjg1YTVhYjJiZjY3NzU3NTgzOGEzNmMxYzhhMDkxZGM1ZWJiY2Y0ZTZiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:41:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9UODNzdEJZNUxrRWJEaU5MeE5Bcnc9PSIsInZhbHVlIjoiaGZuUTBPSUsvT0Mxdk9GWjZFZTQ5WHdydHVkdFl5UkxWalFkalViWE41R01vWitucWJyRHp6NXpFT3VFSTFiRTAwQVJUYWNCS2gxN2o1RmpndU5oTE1DYThqTGhTblBBNkNGSlJEYnNTbFhPd2RFdU9BUWlYeWw5bFdWaFZhYlZoeklIZ1FydVNIRFdzSnBEa0xaQnJ1UW9jZUFVUC9jVk9keUpQalBRV3g2Tk5vTlA3bkxNZmUzZ0R6b2V6UGdCaFl3dHlwdTEzdVBpampYZkdMTWZJZlZQdUxHL2dwVGZjUXBJZ0RXMjEwQkxsZDVrd2VmeU9yaTNZdGNmUGFhZFdjNGEzRFpUamFBZGZZSzZOZlR3T0Jyc3p4YjkwcUREV0luZjlsZjVBa2N6eGRHMmwwVG9uUU10UTdTWm1XZXVoNnhXYUUrOEErSHgzbVV2ZTFDS29EWGxQcU9sK0xndHlPZm9wVFpvcFB5WHNNakpiVlFhbitNTG53YlhGNFF1NUJCVkx6eVdqYmxRQ3BIVzZwNGxwTngzZlRGdThKWGxVcENuMXZ6d1cvSHgrUUlXd3RheTVMVktyZ1NXQ2ZLbTdxZVRNOUJQWDErc3pDc0toTDRiTEVCWGE4Z3p3cGxPU0FzYTUwYk96aUFqM01qd1R1SlhaTlh3ay9ITWNLOFAiLCJtYWMiOiIyZDliNWY0YzA2OGJiOWM2OWUxOGYwMmY1NWYwNmU4YjM3MTQ2NjVhMjQ0ZTRkNjRiNTFmOTJiYWIxN2Q5M2RiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:41:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlU1OTg2dTZNaHN3NUYrcUJyVEU1OXc9PSIsInZhbHVlIjoiWSsycXhXVTZLMzlMaWNKNVB0b1NabFhMT3dwYktMY1JrcjdPK3F2NGRYSHh5YjhRdjc5a256MzNIZzhFb1FPZ3hNRTkyTm80ZEhBem1naUhRYkdVcGxuMlVNTFc5c3Fsdk9IZFVwVjlwejRiL1lTd1U0SkJWNmFONnlMd1BKSWJ4QzVVTks1NUFFTjB1cGtTOXZKR2RHRVJVcGkySEhRQzErMWpwbTg4NWxRc0VPUVc2QS9kejExb1RocUY1NmI3ZCtrZTFpSlNXdHFOcmY0eERlekJ1ZFpQOWhlMGVIRDZ0S3dSSllZd0FmNlA2RlFoZzY5ZG1DSkl3WkUxR2NSb0dWQ2EvZmZJZkJaZzc1aXY0NnhIQU8wMENDVnFXMDEvVHdGV1I5WlhXNENwcDQvL2ZxVEV3c2RtSDlxS0xZRy84TG83aVZ4ZjErb2tabzZMUElpK3h1SnZSWS9LaVBnalN0UFZQb3hmR0YrcVpzWUtIZGtxbmRNTU11V2ZXWHpobTlRVlhNWUxkaC8wT2p1V0NSdFd5bmoyb3ZZaWNKN3ZBSENqNkM1MmtvVnJETlpPMlJyZVBFTWFYVXp4bnJKRDE0cTV2ZW5XNmlkZ1YyQUlLN0dyNWhzUFVRNWEzZVVWSk81dTBENS8xRmhnSmRHT1JmRHlHSUN6Q3UzR1VVSkkiLCJtYWMiOiI4NGYzZjQ0NzAxNTI2NDJmYWNlNDc0Yjg1YTVhYjJiZjY3NzU3NTgzOGEzNmMxYzhhMDkxZGM1ZWJiY2Y0ZTZiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:41:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318602719\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1191360445 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kuHQdU0N8Bo9s0XYMv46ZUIkmhdE6aPTmgW7mkA1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191360445\", {\"maxDepth\":0})</script>\n"}}