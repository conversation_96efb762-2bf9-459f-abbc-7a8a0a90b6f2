{"__meta": {"id": "X1a14bdfee3b5e58890152215edef3db0", "datetime": "2025-08-02 09:42:20", "utime": **********.508668, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754127738.664988, "end": **********.508749, "duration": 1.8437609672546387, "duration_str": "1.84s", "measures": [{"label": "Booting", "start": 1754127738.664988, "relative_start": 0, "end": **********.361949, "relative_end": **********.361949, "duration": 1.6969609260559082, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.36198, "relative_start": 1.****************, "end": **********.508759, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SxkbtLpl35L0r6QXyLxKYIZwT3vZZWzNOVE8EIXZ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-788208393 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-788208393\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-202403450 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-202403450\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2030462975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2030462975\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1761954039 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761954039\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-924223654 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-924223654\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1065295278 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:42:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjN1cytHVzlOUWFSY0xwL1pEWURiMkE9PSIsInZhbHVlIjoiNHVsS1drcG5tUEJ1M1dSSlp1TGZNdGxyRDdEbkJtOGM2MlpMNjgyUWg2TkIwSUREbFpnZ21QNGhMaUpScEtlNnU1V2lsRTk3SXhxNWZGUVJ4QSs3TFNsZXlYRjZMVTZzbXFkSFBKN3NGVnRWUmR3bENGYlBxZHFRNXVJeE1ObERDaktPZjN3VzNoWVFleGhvYW1weWd4UFhIVElSTmJ6eXdZSEd5L2VaS3pXODZXNkwybm83ZG9LSjVBM0tjcmt6UFpOdjBvTmgrNTByTDB3NHlXc28yMWJleDYwRmoyMXRJMTN2alpwZnd1OEN3UjZGdjRoa0RaeWFTc2xrT0MvQkwrRHoyYkIrR0ZGRjJ3WDFjbnNDd3grTU8zZlM5RVAwc20weUE5T09qbnVobW16S1N3VWlqd29JcnNoL3Vjajd4R0huNHhMR1pxNWZOdVVVRmlsUUljU0JwUWtLcU1ZVUpRQ2cvV0Y3QU9ncnBteXhuelZyRXhNdHdJNUNXelFxZmVpOUEyODVEWXE5bjBwR1lxZ29vZS9UZzVyU2hmaFpUbXdUN2hyeDlUY3lmRWs2NU1jUnBPSkxLeDlDbmVJek5ma2lTU1A4b01YOVBpbzk0MEVzV3Nnc2ZEbDVPa2NXbjk4OGJzOFoyUGVCaHdGakJ5VVQrSVU5dGJjc01TclYiLCJtYWMiOiI3OGNkY2QzYjFhZDA4Njg4ZDE4YWRiNGI1YjJmYTUzMjdhMjA4NGU0NTM5ZDljNzk3MTUxNjEwZGZiNTZmNDZjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:42:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlluSFpUZ0ZHWStPanhKMFoxY2F0a3c9PSIsInZhbHVlIjoiZ1pJWHBBRWQ2aXEyN0JDc3VqMVRreUhjSFJRSGpVa0h4U2F1U2NQRSsraCtSQVVFMFEyaGRVQXd0RTRjRzM0bDJadFdqazM4ZWVZMG42dUtGaGZNWFIzL1ZwTk0wM1RqeGxPR21taWdMZGlWdjJubGlkQzRzcVpVeXBqaGlCT0JyeWsrc3N2dkZLalVrNTlwVmJtUHdybEk4NWd2RG5lWUJuajlvcjI0U3ZVZlg5OThpZDVIb3NjZHVodmlVaVQ1Q2RHNkpZemUxT0tqOWJGVzRIY0dUMU1kVGttQi9INHk3V0ZUMWw3NnhkLzFIMGRFbmU1TGkxdk1CbVRWRWsyaHQ4Vjc2T1N3emtINDVIUDRXZEVBQzFoNEVZVzJJa0JzdzBiZWlONEZZWldXcW5CR0UyWmZnTjA5TFN3MlFCVCtDUnRqUzNVQ1Y3T1RVQ2pXOEFyZVFpY1ZvZzMrTndnOVBvWXYrLy92bzFLSXM0YTNEQ1lEazlsM1BtQytCM1luSUdYaDBBSnFrbHVrV01XUzg3OFkvdnQ1WHRRQWN3ditoRWduZ0JmdnRiQU9oK0JqZFJyMFVaM3FPdGsyQzZlc3c2SXlWV3dkdkpJOGJESnFodmRFV1B6VEJOajkybGRqMlpIcTRLU09acEo4WDZka2xoMWl1WEtydWpXV1daZEkiLCJtYWMiOiJkZjJmMThkZjI5YTZjNjVlOGI5YmFlNWE2ODJhMjYyNDJhYjQ3YzBhODE5MDc1MzlhMmYyNGVmNDdmNWNhMmE5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:42:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjN1cytHVzlOUWFSY0xwL1pEWURiMkE9PSIsInZhbHVlIjoiNHVsS1drcG5tUEJ1M1dSSlp1TGZNdGxyRDdEbkJtOGM2MlpMNjgyUWg2TkIwSUREbFpnZ21QNGhMaUpScEtlNnU1V2lsRTk3SXhxNWZGUVJ4QSs3TFNsZXlYRjZMVTZzbXFkSFBKN3NGVnRWUmR3bENGYlBxZHFRNXVJeE1ObERDaktPZjN3VzNoWVFleGhvYW1weWd4UFhIVElSTmJ6eXdZSEd5L2VaS3pXODZXNkwybm83ZG9LSjVBM0tjcmt6UFpOdjBvTmgrNTByTDB3NHlXc28yMWJleDYwRmoyMXRJMTN2alpwZnd1OEN3UjZGdjRoa0RaeWFTc2xrT0MvQkwrRHoyYkIrR0ZGRjJ3WDFjbnNDd3grTU8zZlM5RVAwc20weUE5T09qbnVobW16S1N3VWlqd29JcnNoL3Vjajd4R0huNHhMR1pxNWZOdVVVRmlsUUljU0JwUWtLcU1ZVUpRQ2cvV0Y3QU9ncnBteXhuelZyRXhNdHdJNUNXelFxZmVpOUEyODVEWXE5bjBwR1lxZ29vZS9UZzVyU2hmaFpUbXdUN2hyeDlUY3lmRWs2NU1jUnBPSkxLeDlDbmVJek5ma2lTU1A4b01YOVBpbzk0MEVzV3Nnc2ZEbDVPa2NXbjk4OGJzOFoyUGVCaHdGakJ5VVQrSVU5dGJjc01TclYiLCJtYWMiOiI3OGNkY2QzYjFhZDA4Njg4ZDE4YWRiNGI1YjJmYTUzMjdhMjA4NGU0NTM5ZDljNzk3MTUxNjEwZGZiNTZmNDZjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:42:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlluSFpUZ0ZHWStPanhKMFoxY2F0a3c9PSIsInZhbHVlIjoiZ1pJWHBBRWQ2aXEyN0JDc3VqMVRreUhjSFJRSGpVa0h4U2F1U2NQRSsraCtSQVVFMFEyaGRVQXd0RTRjRzM0bDJadFdqazM4ZWVZMG42dUtGaGZNWFIzL1ZwTk0wM1RqeGxPR21taWdMZGlWdjJubGlkQzRzcVpVeXBqaGlCT0JyeWsrc3N2dkZLalVrNTlwVmJtUHdybEk4NWd2RG5lWUJuajlvcjI0U3ZVZlg5OThpZDVIb3NjZHVodmlVaVQ1Q2RHNkpZemUxT0tqOWJGVzRIY0dUMU1kVGttQi9INHk3V0ZUMWw3NnhkLzFIMGRFbmU1TGkxdk1CbVRWRWsyaHQ4Vjc2T1N3emtINDVIUDRXZEVBQzFoNEVZVzJJa0JzdzBiZWlONEZZWldXcW5CR0UyWmZnTjA5TFN3MlFCVCtDUnRqUzNVQ1Y3T1RVQ2pXOEFyZVFpY1ZvZzMrTndnOVBvWXYrLy92bzFLSXM0YTNEQ1lEazlsM1BtQytCM1luSUdYaDBBSnFrbHVrV01XUzg3OFkvdnQ1WHRRQWN3ditoRWduZ0JmdnRiQU9oK0JqZFJyMFVaM3FPdGsyQzZlc3c2SXlWV3dkdkpJOGJESnFodmRFV1B6VEJOajkybGRqMlpIcTRLU09acEo4WDZka2xoMWl1WEtydWpXV1daZEkiLCJtYWMiOiJkZjJmMThkZjI5YTZjNjVlOGI5YmFlNWE2ODJhMjYyNDJhYjQ3YzBhODE5MDc1MzlhMmYyNGVmNDdmNWNhMmE5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:42:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065295278\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14265182 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SxkbtLpl35L0r6QXyLxKYIZwT3vZZWzNOVE8EIXZ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14265182\", {\"maxDepth\":0})</script>\n"}}