{"__meta": {"id": "X0f5eec1e9869ce2d1712518ab8eaf888", "datetime": "2025-08-02 10:32:01", "utime": **********.971087, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130720.938579, "end": **********.97113, "duration": 1.0325508117675781, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1754130720.938579, "relative_start": 0, "end": **********.871129, "relative_end": **********.871129, "duration": 0.9325499534606934, "duration_str": "933ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.871143, "relative_start": 0.****************, "end": **********.971132, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "99.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hrues0t20hbaolmy1eCFUzbHQbKG5XjyBEi04Rtm", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-821128596 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-821128596\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-327542802 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-327542802\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-849482548 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-849482548\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-405599486 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405599486\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1452885512 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1452885512\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-626160217 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:32:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inpob3d0cE94K25uaTRaSk9BUGpCK1E9PSIsInZhbHVlIjoiem5NMTZyZGRiM0NUcjVkanpzbTYveXE5M3RpODFHQk95Rk5sR20vdlpUVFkzcHhtOEFVOEZNWFF1ZGNQbnFpMGdjOUtGS0NpTDcyL2JiYnBBN0JXTmVvVTREbEtwQktNSzROdE1jbEt3bzZoNG9qc05QVVFLM1ZkVXlTVnBGcGZlYU50T0VnL3hBTnUyWGRJWXFPMFVNaGRIWmJyZnViZlJCWE0yak1DRlA2WEY0eFR2NXZ0aVdnaFZZSkVaMWtaV0NENTVOMVlRck5MbkZzcUZXZ1d4ZGhsbWRDei9INGQyUk9HOTBCWm9KRHkya0IwY2pjYjhHVGw3em93OUh4Mm9PTi9vaG5hZXNrMDRPVkV5SnpxSGVYM2UvcEc4T1pFeCswRlRWd095RkRqUUE1cHowbzZFQ0xTQUd5SVMzNGVjMldtenZzQiszbjRUdEdwVll0ODcxeGZEYitqa0t1ckllbkZlZlBBQm4yVC8vVzhEdEhnTHc1S1hDQ0pyY0dvZTduaW1LYVBIeUN4dnNqSnlBMFBBYmVIQ004eVYvaDE5SUUyK3dVdENrSmxrNjN2VXJCREFWdU9MelUyT29HN1NscU90bVpQcWQ4T2Y3eGRMZHNlS25uVVBYRDJleWl3dHp1MWVmdVFHZmE2THMxRmpQaktDVlBVSW1WazRoRUsiLCJtYWMiOiI1NTliMzFlZGY5ZTliODkxNmYyMTc0MjQ4NmQ2OWIwMjExMzFkNDdhODRiYzY5MDM4NDY4ZmY1YzQ2N2JjZTQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:32:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNYWDh0MGJMZ3JEOWsrNmQvWFFnSlE9PSIsInZhbHVlIjoidThicVdIYVBCS29qSFZUN0c3UUxxbzlySTMxYjZGc2VoODVFbzQvNHE1allxMUlQcElERldReStBU3E0Sk5JNlROanIxbmtYYTF1S1U1bExIbnd0L2hyTDVJLzllV1RubE1pZkcxaFZkS0tCdHFYOHZiaE93VTVwcFZMYk42SDBlRzVGNXhsb3hzQlhQYVJ5OFJKY1VJSURYUTIxaUovaU1QWDNkT2dCbmhkalpqQXpJU2RyRjhGczR1Yk1SNUM4UXZSdzFqbU5FcjlzYjJCK2ZFb3Rid3pGRHc0V0VnYlBMSERzZVAvaGp5OHN1T2dCRlRrZ1VmSjhIK1l3UmgyQ3JreW9saURRR1NhV1pSbCtwQnh5Q3N5UXMzWUFKYTNNTUtkZ1FNTUFKenlvSmRXRWUraHEzOWpGaVNReWZqcE90TmplWFROdmI1TXdYRmt6ZEtYWU5BZkVIN3lsK2laOXlvK1creGVWcEJEa3RLMTVvTjJJcHB0M0syYXIyY2dFVjBNMUcxTzVIM25xcFc2bktrUVk2TUkvQ3Awcm1IYjRia0VyOEFGdFhRS0ZubUtXWUV3Z0pvRUZPVWQzcUhzMVNrUXQrYWExK1g4b3NSOWZKSlVueFV0YlRyVzJPUlVmaEFhbVdlZ2pmUlpXcGd4dnVoR21oOVVkVkVpZVFjSkEiLCJtYWMiOiI1Yzk1ZGU4NTUyYWI1YTM4NmJhZTM0ZDM5NmE1MjFkZGFhNmExZWNiZDlhMDhmZjVjNTIwN2E0Yjk3MWU1YjVhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:32:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inpob3d0cE94K25uaTRaSk9BUGpCK1E9PSIsInZhbHVlIjoiem5NMTZyZGRiM0NUcjVkanpzbTYveXE5M3RpODFHQk95Rk5sR20vdlpUVFkzcHhtOEFVOEZNWFF1ZGNQbnFpMGdjOUtGS0NpTDcyL2JiYnBBN0JXTmVvVTREbEtwQktNSzROdE1jbEt3bzZoNG9qc05QVVFLM1ZkVXlTVnBGcGZlYU50T0VnL3hBTnUyWGRJWXFPMFVNaGRIWmJyZnViZlJCWE0yak1DRlA2WEY0eFR2NXZ0aVdnaFZZSkVaMWtaV0NENTVOMVlRck5MbkZzcUZXZ1d4ZGhsbWRDei9INGQyUk9HOTBCWm9KRHkya0IwY2pjYjhHVGw3em93OUh4Mm9PTi9vaG5hZXNrMDRPVkV5SnpxSGVYM2UvcEc4T1pFeCswRlRWd095RkRqUUE1cHowbzZFQ0xTQUd5SVMzNGVjMldtenZzQiszbjRUdEdwVll0ODcxeGZEYitqa0t1ckllbkZlZlBBQm4yVC8vVzhEdEhnTHc1S1hDQ0pyY0dvZTduaW1LYVBIeUN4dnNqSnlBMFBBYmVIQ004eVYvaDE5SUUyK3dVdENrSmxrNjN2VXJCREFWdU9MelUyT29HN1NscU90bVpQcWQ4T2Y3eGRMZHNlS25uVVBYRDJleWl3dHp1MWVmdVFHZmE2THMxRmpQaktDVlBVSW1WazRoRUsiLCJtYWMiOiI1NTliMzFlZGY5ZTliODkxNmYyMTc0MjQ4NmQ2OWIwMjExMzFkNDdhODRiYzY5MDM4NDY4ZmY1YzQ2N2JjZTQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:32:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNYWDh0MGJMZ3JEOWsrNmQvWFFnSlE9PSIsInZhbHVlIjoidThicVdIYVBCS29qSFZUN0c3UUxxbzlySTMxYjZGc2VoODVFbzQvNHE1allxMUlQcElERldReStBU3E0Sk5JNlROanIxbmtYYTF1S1U1bExIbnd0L2hyTDVJLzllV1RubE1pZkcxaFZkS0tCdHFYOHZiaE93VTVwcFZMYk42SDBlRzVGNXhsb3hzQlhQYVJ5OFJKY1VJSURYUTIxaUovaU1QWDNkT2dCbmhkalpqQXpJU2RyRjhGczR1Yk1SNUM4UXZSdzFqbU5FcjlzYjJCK2ZFb3Rid3pGRHc0V0VnYlBMSERzZVAvaGp5OHN1T2dCRlRrZ1VmSjhIK1l3UmgyQ3JreW9saURRR1NhV1pSbCtwQnh5Q3N5UXMzWUFKYTNNTUtkZ1FNTUFKenlvSmRXRWUraHEzOWpGaVNReWZqcE90TmplWFROdmI1TXdYRmt6ZEtYWU5BZkVIN3lsK2laOXlvK1creGVWcEJEa3RLMTVvTjJJcHB0M0syYXIyY2dFVjBNMUcxTzVIM25xcFc2bktrUVk2TUkvQ3Awcm1IYjRia0VyOEFGdFhRS0ZubUtXWUV3Z0pvRUZPVWQzcUhzMVNrUXQrYWExK1g4b3NSOWZKSlVueFV0YlRyVzJPUlVmaEFhbVdlZ2pmUlpXcGd4dnVoR21oOVVkVkVpZVFjSkEiLCJtYWMiOiI1Yzk1ZGU4NTUyYWI1YTM4NmJhZTM0ZDM5NmE1MjFkZGFhNmExZWNiZDlhMDhmZjVjNTIwN2E0Yjk3MWU1YjVhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:32:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626160217\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2129045160 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hrues0t20hbaolmy1eCFUzbHQbKG5XjyBEi04Rtm</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129045160\", {\"maxDepth\":0})</script>\n"}}