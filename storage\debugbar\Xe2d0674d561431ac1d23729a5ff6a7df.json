{"__meta": {"id": "Xe2d0674d561431ac1d23729a5ff6a7df", "datetime": "2025-08-02 08:27:24", "utime": **********.473102, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754123242.025129, "end": **********.473206, "duration": 2.4480769634246826, "duration_str": "2.45s", "measures": [{"label": "Booting", "start": 1754123242.025129, "relative_start": 0, "end": **********.25752, "relative_end": **********.25752, "duration": 2.232390880584717, "duration_str": "2.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.25757, "relative_start": 2.***************, "end": **********.473213, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RW1XQB6K0ldCvP4ImWtwcB4QSjfJRPDmF4TiV1JX", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2131021097 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2131021097\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-594777266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-594777266\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1884299604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1884299604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-750910613 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750910613\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2090560791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2090560791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:27:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkUvRUVERzNsdTN4ditIaW5VNmZEQ2c9PSIsInZhbHVlIjoiMU9RU2xlRU1mVnFPV1o0WWFIMDRZUXpzV1gxNll2Qzd6bXNUSHl2ME9qRk8zb3Y0WW5PWjd3aWNGTG9TbjZTMk80UVI3aVJkZTlLV1kwN2MvVU9WYUlYVkxmREhYeDdRMmtiWVlnSUI5RnBPSDZIOWtpMkNHSUVwSittNWRZOTNDdXF0VUd4NnJEeFl0QzllM09OZ2xveVMvaXI5cWF5SURSZWVmeGFOajYxVy9YdjlINlg5VE10UzRCalc4cnhkZ1N1TDZ3dTNRMjdiMjVyN1I2QmRkSW1ZUmF2cUp5RWMxUHZ1TG1Hb2VNcHZQSHFQS0NXNktUcEprenI3aFBpK0RvdEU4OEc4SGxJOEpUNnZRUGxtY1BVYy84b210K0IzZUdYZTVYVlJuV0hLekZUR09iTE5uT3lFemxkQWVYNTJtT0JPR29JcVdnV29VSzJqa05ZWWovUDN2UTFrcFV2SEJWeWdjOEJxOXZMSTZ5UUY5WUt3NUMxR1ovZmlucURBR0czMld0aGpQM1J5ei9WZzFUUVp1dlR2OWxlT3lTTWJVenB1YStYRXhwQmR2TitLUG5OOWdnNWdmUTBJZ3lpdUhVb2w3UnR5MFhGU2tueWhQMmd1NGhyWWoxNzdvTHc5QmRYNHVaU2JrczA3VlpJQWVmMXNJOHZrOFhMY3gybE4iLCJtYWMiOiJiMzA4ZGRmMmJhZmEyNmUwM2UxYzdiMjVhYTUzYjAzMjMxZjFjZTQzOTZmNjRiNjNmNDY2YWQ2YTFlNjNiOGFmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:27:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InpJdk5BWHZlYTdSeFQxUFl6d0tmQ3c9PSIsInZhbHVlIjoiQ2pZaUhxR2hTNS9GRGVXdytnblRTV2hkMzZacFVqYzhDenlXVHF0TmRJb2RIWm5jTjNZTVhMdjd6elpObHdPc3BPRDd4M1ljS041T0w3VmVBWnVCTWQ2TmZuVVVYamZQQ3dvOWNOeUZVTUFPMTdTbWkvYTNTK0pVVmhJTHFaTDJVTUNXamVMc0hlOHRHcHJqVGVOMzJpdWw5TDlYdStTZXIveG4wTFN1a0ViWVRuREN0NnZiek13cVdwdzB0YW1nK2tVOXNvWjdJV0ZtL2xBcXNIUGNrelNaTzAwaFA1RmVBRTVuR0NsRFJyR3lXcmxsRGlXUlN1Q2VPTUhrenNCN1VNTzBHRmJNWHNpQWNOVi84VFI1d3J6WG5leWYrUXREVGJuTmVjNDNzM2tsYWd4Q2ZNWU13QkdsMTNFL0pMaWlia2l3ODlOa0F6K2phNUlPdTJ1Q2tmOVp4RU9LY2x6akZqUll3SmFOSXh5TUU0YlY4dUJ1ZS9SbG1rc2lMcndZUjNrWmlTclJFS3MzdlEvbWxnMHhwckhvWHhEVFlIT2hMdmJBQkV6cmtQTWl4SnNTV0RIaGFSRzN0VEI0ZUhZOGNaKzFKTFcrT3Z1blZGbmwxOUp1TVc4K3pPRHRoQjR6OHhwTXRNSTFMUm9kdXQxbm0zcEF2TEEwL2JLTjZXbW4iLCJtYWMiOiIzYTJkZDczNWU0NTRiYjYzOGE4MTNhZTE3YjdkZjAwOGMxM2Y4YWM3OWJiMTVmYjJkYmIwNDU5MzkxMDc0YTNhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:27:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkUvRUVERzNsdTN4ditIaW5VNmZEQ2c9PSIsInZhbHVlIjoiMU9RU2xlRU1mVnFPV1o0WWFIMDRZUXpzV1gxNll2Qzd6bXNUSHl2ME9qRk8zb3Y0WW5PWjd3aWNGTG9TbjZTMk80UVI3aVJkZTlLV1kwN2MvVU9WYUlYVkxmREhYeDdRMmtiWVlnSUI5RnBPSDZIOWtpMkNHSUVwSittNWRZOTNDdXF0VUd4NnJEeFl0QzllM09OZ2xveVMvaXI5cWF5SURSZWVmeGFOajYxVy9YdjlINlg5VE10UzRCalc4cnhkZ1N1TDZ3dTNRMjdiMjVyN1I2QmRkSW1ZUmF2cUp5RWMxUHZ1TG1Hb2VNcHZQSHFQS0NXNktUcEprenI3aFBpK0RvdEU4OEc4SGxJOEpUNnZRUGxtY1BVYy84b210K0IzZUdYZTVYVlJuV0hLekZUR09iTE5uT3lFemxkQWVYNTJtT0JPR29JcVdnV29VSzJqa05ZWWovUDN2UTFrcFV2SEJWeWdjOEJxOXZMSTZ5UUY5WUt3NUMxR1ovZmlucURBR0czMld0aGpQM1J5ei9WZzFUUVp1dlR2OWxlT3lTTWJVenB1YStYRXhwQmR2TitLUG5OOWdnNWdmUTBJZ3lpdUhVb2w3UnR5MFhGU2tueWhQMmd1NGhyWWoxNzdvTHc5QmRYNHVaU2JrczA3VlpJQWVmMXNJOHZrOFhMY3gybE4iLCJtYWMiOiJiMzA4ZGRmMmJhZmEyNmUwM2UxYzdiMjVhYTUzYjAzMjMxZjFjZTQzOTZmNjRiNjNmNDY2YWQ2YTFlNjNiOGFmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:27:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InpJdk5BWHZlYTdSeFQxUFl6d0tmQ3c9PSIsInZhbHVlIjoiQ2pZaUhxR2hTNS9GRGVXdytnblRTV2hkMzZacFVqYzhDenlXVHF0TmRJb2RIWm5jTjNZTVhMdjd6elpObHdPc3BPRDd4M1ljS041T0w3VmVBWnVCTWQ2TmZuVVVYamZQQ3dvOWNOeUZVTUFPMTdTbWkvYTNTK0pVVmhJTHFaTDJVTUNXamVMc0hlOHRHcHJqVGVOMzJpdWw5TDlYdStTZXIveG4wTFN1a0ViWVRuREN0NnZiek13cVdwdzB0YW1nK2tVOXNvWjdJV0ZtL2xBcXNIUGNrelNaTzAwaFA1RmVBRTVuR0NsRFJyR3lXcmxsRGlXUlN1Q2VPTUhrenNCN1VNTzBHRmJNWHNpQWNOVi84VFI1d3J6WG5leWYrUXREVGJuTmVjNDNzM2tsYWd4Q2ZNWU13QkdsMTNFL0pMaWlia2l3ODlOa0F6K2phNUlPdTJ1Q2tmOVp4RU9LY2x6akZqUll3SmFOSXh5TUU0YlY4dUJ1ZS9SbG1rc2lMcndZUjNrWmlTclJFS3MzdlEvbWxnMHhwckhvWHhEVFlIT2hMdmJBQkV6cmtQTWl4SnNTV0RIaGFSRzN0VEI0ZUhZOGNaKzFKTFcrT3Z1blZGbmwxOUp1TVc4K3pPRHRoQjR6OHhwTXRNSTFMUm9kdXQxbm0zcEF2TEEwL2JLTjZXbW4iLCJtYWMiOiIzYTJkZDczNWU0NTRiYjYzOGE4MTNhZTE3YjdkZjAwOGMxM2Y4YWM3OWJiMTVmYjJkYmIwNDU5MzkxMDc0YTNhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:27:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1541589822 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RW1XQB6K0ldCvP4ImWtwcB4QSjfJRPDmF4TiV1JX</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541589822\", {\"maxDepth\":0})</script>\n"}}