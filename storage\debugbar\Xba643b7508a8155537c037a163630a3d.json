{"__meta": {"id": "Xba643b7508a8155537c037a163630a3d", "datetime": "2025-08-02 09:44:00", "utime": **********.081647, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[09:44:00] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.053078, "xdebug_link": null, "collector": "log"}, {"message": "[09:44:00] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.059968, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754127838.256594, "end": **********.081732, "duration": 1.8251380920410156, "duration_str": "1.83s", "measures": [{"label": "Booting", "start": 1754127838.256594, "relative_start": 0, "end": **********.867354, "relative_end": **********.867354, "duration": 1.610759973526001, "duration_str": "1.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867381, "relative_start": 1.6107871532440186, "end": **********.081768, "relative_end": 3.600120544433594e-05, "duration": 0.2143869400024414, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46525096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=2988\" onclick=\"\">app/Http/Controllers/LeadController.php:2988-3044</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0083, "accumulated_duration_str": "8.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.993494, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 67.229}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.043993, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 67.229, "width_percent": 18.916}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3015}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0538402, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:3015", "source": "app/Http/Controllers/LeadController.php:3015", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3015", "ajax": false, "filename": "LeadController.php", "line": "3015"}, "connection": "radhe_same", "start_percent": 86.145, "width_percent": 13.855}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-577705156 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-577705156\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1581401986 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581401986\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1228290817 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1228290817\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1878626902 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxJMUcrUmF0RHpnTkVhbk1VanlmVkE9PSIsInZhbHVlIjoiWnpzTVRVc0M5VjYxNGZNQ2JRN3RBbWFSbkhSOUVkbldaT2VsZXB0VjJvbG1sa2lOSUNSeTBkektJSXIvVTh6ZlkzMFQrZVp3U3d2ank4Z2xRS0JSMk42SWYydTFtR1ZQZkErY3hid2JiaVRZcG9sNjU0Y0cxMDBNdUpERS9WOVNwcUdwb1c4dUhydnNJS3NXUm9BOTFhbjVSdmZIeUZKYXAwRW0vSk9WMWNEd0EzV2l5MEVON2Z0L1J3TUhWNlJtcW5hMytaVzEzeTRaV3FxNXJoaGJBbmhNSGwvakliY2VsYnV5cTM5NGpxa1ErdlFjejh4b3ArTnN1aFlnL0RtcU05RjNSNUEyY2trZHo2d0pMMlNlYTI5cHl6cy9EdXFsdGtVT0t4eW1vakxuYk8zOU1ZM2EyOWcwMlErcVVvalM3bzIrWkZrZ1o4SEg0UG8yLy9VVERkek5QbGdWb3FyQkR6ZzhZLzU2SHhVMkcrN1RreW90NG9YbTY1UDBsczhlaFBucStEWVJycmtSSUxTTC8veXVxU3FETmhuZWlKVmV4dWNoTWZYaUgxaUpoMWo3L1djMVdBVkw4bkZ5RFBxRkZVUzBEVnZJem1TdDlVQ3NONWRXS1pLZmx5ZXM3NDhxbFZ4SUZ3UjhRSmlzUGo0aG9iV0Y3TkhqcmU2VnhHMWgiLCJtYWMiOiJhNjI3Yzk3ZDYzODQ5NzJiNTA4ZjBjMDA5YWM3N2JhOTQwMzU5NTcwZDQ4ZjVhMzY2NzA1M2Q4ZjgyOGFiYzY5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImxacTBTRUNXMzlGc2pTcjBvb0syOEE9PSIsInZhbHVlIjoicTZUZ1pqVTd6bU5YY1h4TXQ5TWdsaVA2ZzVyNmtHSTRkQXNSL0sraTVvaDZYS3hJUUtTblNxMCsxYjl3bmxGUUg5ejdEeXBtdkZCRHRwd0JlNHI5RjFHeHRTbG9WNUlrSElxYTVjdFM3SWt6VUM0eC9tOE4zSHFpa0FZUzM4eTIrTjJMU2Y2K0tvR1l5ZENvQWlFNnlra3FUcEJvVWFzOXF5dFFEZFpQWUlzdFU4T2pZa1haVmVJdjRXbC9pa09jc2M1MHUyNDRtaml5OFFnei9sc2xHMWIyWUpHM2w4cTNXSldXN3NpVGo3N3JPenFuWFB3cDNsUmRUYWZ3Z0dnd3daVHhtRisvK2JKOW5uSnkzcFBubUVscG9ZWjVzWmdpNzJIeTZQeUYxN0F4dW1mNklUSDJZNS9FTm5FbHJOSzA0MVZVKzl5VFVZMzdWZGU2MTFHenBsczZISWJTc2grc1FNUW11VXJzQ2UyR3NUcGdrL1B6RmdHMklhM3J4Q2NHeU5VZk1kU3RWcjFaWk5uKzIreGg1Z083TEprQUpTRkUvbm9OcFUvMUo1UHIxcEhHUFpGaW5QMzJsa0Z0dXJ6QUJCcGFpejJkY29xdnpEQmRhcGMyd3BRSFEwY1k2c1h0cCtYSXVkdkdSSVZDNlpsOWZ5b25CeVpSa2pCZHVuTUYiLCJtYWMiOiJjYTdkZjRjYWRmMWMxMDFhNTc1NDE4MzA3M2RmYmYzMGRmMWE3ZDA0MzY1ODQzZGNmZGRlYmEwNjY0MzczNmYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878626902\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-111560034 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111560034\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:44:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijc4d3pWUTFuaGIrYVd6U1VvZ203RFE9PSIsInZhbHVlIjoidXJzVEJzQWlXK3pHL1BLeElQWTNsNWw3cEpnZkk1K3ZsblhXRUZsNUwvNmZmSGF0VW92cUptVWlxRHp0YUJYbDVhVlJDbkxwb0NlVEZMd1ozb3pjOWdsSDJtTUo2R3NNYlpLWmtPM2hEUC9qd0V0RXhyVTA2cERxYU4ya1VFKzc1ZytSNU5xaGhkcnhmbG9LWVFORnQzR1hyL1lldjdMT2xEL0ZWWUpDYWlNVmpoQmhlWThxUUR0VG5HZ1dSVkZQR2tIZGYrRHNCQVB2TFhSMjlFWGs0dWhFSVdFVk5wdTZpRVd3TXVtY2ovcVozUzB1UG84OUovaGpIczdSV0FCM090VHBRdHgvZTZWMnV4SDYzZHpaL1p0VFU1a3VITm9iNFU5S1FNMnVJM0RWbUdLQTRuVFFkTU4vTmR4OWdoZHR1VFpqRmxjZGpvV3p2TVJuditNQnhVRjc0UHVpdTRJc2ZySVgyTURvVUZFTm8yaElVL24wTWVYQlZjRW54dU15S2ZJK0Z0WEZPUWxUTU5TVSs3OENhaTQ1cjBGSVdkYk12UGFJU3BsQ1h1WlAzckZFeURHNDF1djJEQkp2SFNvM3NkbXpsWnVtUWc1Um1oNDZqMk80VUNqaTNYcW0rRWtIdFNRcXByTWI1YUxUeHNmVkdzZEwvQnlnUHptQVBtbVIiLCJtYWMiOiJlOGMxMTE5ZWI2Y2VmMDhiZjFlZDg3YjU5ZDBlM2FmMTBiZmZjNmRmNThmNTAzOTg1YWFmNGY5NDNjZTJmZjIyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:44:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Inl1WnEzTm9yQkEzcGZ5ZnBGVGE4dFE9PSIsInZhbHVlIjoiQWZHZ0RMcGpLdDFDRjgxbGRWMlAyNWtDaFB0L2RaR0NKSi9TS3ZTUHZ6ZDY5ZHNDdlVTdm9nWi9GZElkV0xBdkQxMjZxQ2o2dTNuN3hTdWNmeTJMMXJCR1lQcWlLNCtzNW9Pc0oyVXhUYmE3TTRwbTdpTjBFd241ZHFmbGs3eHM1Mk1UeDVPNU9FZFN6TjdnektoZTZXTGc0elFHZ1hLTHZBZ05ib2s4M1lBMUZoUUo0R1RVcWVVekRUNE9lTnBMYVRBbkpYeFJkczNvYVU5N0xob2xLdkxZd1V2NVZUYnp5ZVo0RjZ6R3k3ZHpCYWpHdWlIQ1hoc0k1UDBFZzJyODFnM3d1MXc1Nm40Yk9zMmF6aWgxTisvWGdiRERRbGpYTWpvMVdHVjk1Qk5LMGNvRkUwWVhZRitBR0kweTBTbDN0YjlXSEF4d2R3WlU3UE9mRnpIVndJeG1PMld4VHpIMlEyOHdETXl3N0k0ZTUzU1FHeWRrUHB3OGY2S08rZy9EWUwydTN4WHRWWXo0L0o5YTRFc1U5L09Sd1ZJUE9tWThyVlc3Q3huMjNVdmZNTjM2elN1NTVwK2JjNlg0cEMzMnBrK0dJUklDU3dEWFUxMUZUNXVDU1FxUCt2bVpTZ0J5MThwUFlEL0VZb1daRjh0ZEttVEFnTjV4bXFPY3ZkUjQiLCJtYWMiOiJkM2RmYTdlYTYyZDlhNzBhYjNmZTM2NGE5NjFiYTA2OWU1ZmYwMjllNzQ3NmFiOWIwNWU0OWIxZTQzNTE0MjhhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:44:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijc4d3pWUTFuaGIrYVd6U1VvZ203RFE9PSIsInZhbHVlIjoidXJzVEJzQWlXK3pHL1BLeElQWTNsNWw3cEpnZkk1K3ZsblhXRUZsNUwvNmZmSGF0VW92cUptVWlxRHp0YUJYbDVhVlJDbkxwb0NlVEZMd1ozb3pjOWdsSDJtTUo2R3NNYlpLWmtPM2hEUC9qd0V0RXhyVTA2cERxYU4ya1VFKzc1ZytSNU5xaGhkcnhmbG9LWVFORnQzR1hyL1lldjdMT2xEL0ZWWUpDYWlNVmpoQmhlWThxUUR0VG5HZ1dSVkZQR2tIZGYrRHNCQVB2TFhSMjlFWGs0dWhFSVdFVk5wdTZpRVd3TXVtY2ovcVozUzB1UG84OUovaGpIczdSV0FCM090VHBRdHgvZTZWMnV4SDYzZHpaL1p0VFU1a3VITm9iNFU5S1FNMnVJM0RWbUdLQTRuVFFkTU4vTmR4OWdoZHR1VFpqRmxjZGpvV3p2TVJuditNQnhVRjc0UHVpdTRJc2ZySVgyTURvVUZFTm8yaElVL24wTWVYQlZjRW54dU15S2ZJK0Z0WEZPUWxUTU5TVSs3OENhaTQ1cjBGSVdkYk12UGFJU3BsQ1h1WlAzckZFeURHNDF1djJEQkp2SFNvM3NkbXpsWnVtUWc1Um1oNDZqMk80VUNqaTNYcW0rRWtIdFNRcXByTWI1YUxUeHNmVkdzZEwvQnlnUHptQVBtbVIiLCJtYWMiOiJlOGMxMTE5ZWI2Y2VmMDhiZjFlZDg3YjU5ZDBlM2FmMTBiZmZjNmRmNThmNTAzOTg1YWFmNGY5NDNjZTJmZjIyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:44:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Inl1WnEzTm9yQkEzcGZ5ZnBGVGE4dFE9PSIsInZhbHVlIjoiQWZHZ0RMcGpLdDFDRjgxbGRWMlAyNWtDaFB0L2RaR0NKSi9TS3ZTUHZ6ZDY5ZHNDdlVTdm9nWi9GZElkV0xBdkQxMjZxQ2o2dTNuN3hTdWNmeTJMMXJCR1lQcWlLNCtzNW9Pc0oyVXhUYmE3TTRwbTdpTjBFd241ZHFmbGs3eHM1Mk1UeDVPNU9FZFN6TjdnektoZTZXTGc0elFHZ1hLTHZBZ05ib2s4M1lBMUZoUUo0R1RVcWVVekRUNE9lTnBMYVRBbkpYeFJkczNvYVU5N0xob2xLdkxZd1V2NVZUYnp5ZVo0RjZ6R3k3ZHpCYWpHdWlIQ1hoc0k1UDBFZzJyODFnM3d1MXc1Nm40Yk9zMmF6aWgxTisvWGdiRERRbGpYTWpvMVdHVjk1Qk5LMGNvRkUwWVhZRitBR0kweTBTbDN0YjlXSEF4d2R3WlU3UE9mRnpIVndJeG1PMld4VHpIMlEyOHdETXl3N0k0ZTUzU1FHeWRrUHB3OGY2S08rZy9EWUwydTN4WHRWWXo0L0o5YTRFc1U5L09Sd1ZJUE9tWThyVlc3Q3huMjNVdmZNTjM2elN1NTVwK2JjNlg0cEMzMnBrK0dJUklDU3dEWFUxMUZUNXVDU1FxUCt2bVpTZ0J5MThwUFlEL0VZb1daRjh0ZEttVEFnTjV4bXFPY3ZkUjQiLCJtYWMiOiJkM2RmYTdlYTYyZDlhNzBhYjNmZTM2NGE5NjFiYTA2OWU1ZmYwMjllNzQ3NmFiOWIwNWU0OWIxZTQzNTE0MjhhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:44:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}