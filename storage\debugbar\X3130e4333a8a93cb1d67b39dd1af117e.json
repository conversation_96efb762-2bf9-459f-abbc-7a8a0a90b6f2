{"__meta": {"id": "X3130e4333a8a93cb1d67b39dd1af117e", "datetime": "2025-08-02 10:46:39", "utime": **********.880991, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754131598.640488, "end": **********.881055, "duration": 1.2405672073364258, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1754131598.640488, "relative_start": 0, "end": **********.807646, "relative_end": **********.807646, "duration": 1.1671581268310547, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.807661, "relative_start": 1.***************, "end": **********.881058, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "73.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SXVSepPb9PHjvWsvLBwawgxDE5mMnMBeOR1OkU0g", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-601985871 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-601985871\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-514401145 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-514401145\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1548215527 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1548215527\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-498247855 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498247855\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1012530502 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1012530502\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-71287906 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:46:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InIxWnJsa3pzNy9NdDJ2NjZOVE1sT1E9PSIsInZhbHVlIjoickt3eVVNbWlJeHIxbVpMMWRHUjFla0hQK3oycUtHblNxcVczNWlGK1FpeVBBbUdteUg4UHdGN2JZYnpnMitwb3Y2R2IwUWRBNkxZVVVIYW1oLy9ObkIvNk5pSm1HdVBmWHl6M2gvQjRSVGJGSmJWeU9BSnhDeUdPRVFUOXU3RjgvUE9YVUxpYk5SMXVTWUxPVmxtWkk0OHJkSHlZMHFGNDUreFZNMjV3MG5hU01XYmxGMm13dnM3UVkxUTc1YUcwdFZHbVdxUzZkQnhvc0JadHNkbnpiOVJseXJIUGxaUm5SUSs0U2FlUE5jZTRLRW92TFdUaGhZeWVFRkx0Y3NCV1hWa1pRRHJYbnNLYlRzRzdEWlVhekJLbzg1U2VVZXRiZ1gvV1ZwbUFSRjN6ZWEwZ2dONFVZU3Nvc2lrcURuZEJ2TFNyeXQ5RUxJc0ZRY1IrVzhyY1pNUTdpRHZNUklmYUIzU2ozM0xzNTNrN1pkaGd3L1MyNWZLcXVVUlVaVFI4YVpId2o0S3grN0RvTmtzUzZ5Y3lKUGVEOW5ySFd6WHdZLzlYaXVNcm9LK0EvSFdQOHBzOGhaSnZEcHRCMUpRYU9ZRk5rQytwYzlISFBLQVBHRTI4cDNGYXIxTXFwMEtOa3dESFVpcEhhWHhVSjRLZ2I5b0ZnKytOZlB3SHd0a2MiLCJtYWMiOiI1YWI0MGNhNDE5M2U1NDViMzFlNDkxMThiNDRjMTgwNzI5M2FmNWFmYTM1NDMxZWIzMWZkMTUyODY5NDAwZjIxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:46:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlkvcDB1ZjJyZit3MTYyWVFUS1ZjYUE9PSIsInZhbHVlIjoiY2FWbUxIc0Q2dEMxWEZSVzZOYW93ZTI3bHJYaWxIYll5S3A4QVpGZ2xFMlhDZHF4MlNMalFDTVlCNEhyV2UwWDNNM2Noam0ybUUrTUpta29ackpyVmYrYURhbCtFOTBIRTExTERZdkY2WUo4d0IyZ3JmckNpbEY0REhTV1Z4c3F4ZjJJTFkrMEpKNDlHT0NyRU1IWER0VEJxSTA1Mm9CWUdadXR5OW5PWnRzM0hkR0NXTUtnaDRrSjN5UThRN2pOY0tLN1Y2VUc0QUU1OUk0NlMva2FlcURYY3NaeGVQZkxadnAyS2dWSWRYcjZFdzBvbjYzMVU4d0ZoL1ArdG15U0p6cGZxVW1HbDZuVzkzOE81NkgyQ2swbVk0eXBhRTR5c1dVMTBabzB4SDBlQ095ZFE2UFhkTmdEQmlOMnE4WU1VU1RQaHVtMGV4VmRPTm5WWTlIZzkrd3BMWlJlRnQ0UTJ2V3hpbmR5d3RDUFdDcFcxOCsrdWdJMFl1bEJkUDlaSGY3TFBSSTlmZnZlWTdkKzh1VVh4UDFCQTVlZEgyRlBES0l5K0ROU3pzcndUSE1KaTREd0MxQ3ZQdXNNWVRyTks3ZGl1b201ejh0TnNwLyt1VE9pS21qSDNNTHRLOExuSHZDRGxoTkY0NzJVclBmTVFWbElHREdleWRTRk9TR1QiLCJtYWMiOiI5YTgzMjVhMTQyMGZjMmQyMGI0NWY2MmQxYWY1NDJhYTM3MDY2NDFkNWUwOGM2ZDYyZDdkMDQ0NmE2MzFmMGU5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:46:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InIxWnJsa3pzNy9NdDJ2NjZOVE1sT1E9PSIsInZhbHVlIjoickt3eVVNbWlJeHIxbVpMMWRHUjFla0hQK3oycUtHblNxcVczNWlGK1FpeVBBbUdteUg4UHdGN2JZYnpnMitwb3Y2R2IwUWRBNkxZVVVIYW1oLy9ObkIvNk5pSm1HdVBmWHl6M2gvQjRSVGJGSmJWeU9BSnhDeUdPRVFUOXU3RjgvUE9YVUxpYk5SMXVTWUxPVmxtWkk0OHJkSHlZMHFGNDUreFZNMjV3MG5hU01XYmxGMm13dnM3UVkxUTc1YUcwdFZHbVdxUzZkQnhvc0JadHNkbnpiOVJseXJIUGxaUm5SUSs0U2FlUE5jZTRLRW92TFdUaGhZeWVFRkx0Y3NCV1hWa1pRRHJYbnNLYlRzRzdEWlVhekJLbzg1U2VVZXRiZ1gvV1ZwbUFSRjN6ZWEwZ2dONFVZU3Nvc2lrcURuZEJ2TFNyeXQ5RUxJc0ZRY1IrVzhyY1pNUTdpRHZNUklmYUIzU2ozM0xzNTNrN1pkaGd3L1MyNWZLcXVVUlVaVFI4YVpId2o0S3grN0RvTmtzUzZ5Y3lKUGVEOW5ySFd6WHdZLzlYaXVNcm9LK0EvSFdQOHBzOGhaSnZEcHRCMUpRYU9ZRk5rQytwYzlISFBLQVBHRTI4cDNGYXIxTXFwMEtOa3dESFVpcEhhWHhVSjRLZ2I5b0ZnKytOZlB3SHd0a2MiLCJtYWMiOiI1YWI0MGNhNDE5M2U1NDViMzFlNDkxMThiNDRjMTgwNzI5M2FmNWFmYTM1NDMxZWIzMWZkMTUyODY5NDAwZjIxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:46:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlkvcDB1ZjJyZit3MTYyWVFUS1ZjYUE9PSIsInZhbHVlIjoiY2FWbUxIc0Q2dEMxWEZSVzZOYW93ZTI3bHJYaWxIYll5S3A4QVpGZ2xFMlhDZHF4MlNMalFDTVlCNEhyV2UwWDNNM2Noam0ybUUrTUpta29ackpyVmYrYURhbCtFOTBIRTExTERZdkY2WUo4d0IyZ3JmckNpbEY0REhTV1Z4c3F4ZjJJTFkrMEpKNDlHT0NyRU1IWER0VEJxSTA1Mm9CWUdadXR5OW5PWnRzM0hkR0NXTUtnaDRrSjN5UThRN2pOY0tLN1Y2VUc0QUU1OUk0NlMva2FlcURYY3NaeGVQZkxadnAyS2dWSWRYcjZFdzBvbjYzMVU4d0ZoL1ArdG15U0p6cGZxVW1HbDZuVzkzOE81NkgyQ2swbVk0eXBhRTR5c1dVMTBabzB4SDBlQ095ZFE2UFhkTmdEQmlOMnE4WU1VU1RQaHVtMGV4VmRPTm5WWTlIZzkrd3BMWlJlRnQ0UTJ2V3hpbmR5d3RDUFdDcFcxOCsrdWdJMFl1bEJkUDlaSGY3TFBSSTlmZnZlWTdkKzh1VVh4UDFCQTVlZEgyRlBES0l5K0ROU3pzcndUSE1KaTREd0MxQ3ZQdXNNWVRyTks3ZGl1b201ejh0TnNwLyt1VE9pS21qSDNNTHRLOExuSHZDRGxoTkY0NzJVclBmTVFWbElHREdleWRTRk9TR1QiLCJtYWMiOiI5YTgzMjVhMTQyMGZjMmQyMGI0NWY2MmQxYWY1NDJhYTM3MDY2NDFkNWUwOGM2ZDYyZDdkMDQ0NmE2MzFmMGU5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:46:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71287906\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1646220488 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SXVSepPb9PHjvWsvLBwawgxDE5mMnMBeOR1OkU0g</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646220488\", {\"maxDepth\":0})</script>\n"}}