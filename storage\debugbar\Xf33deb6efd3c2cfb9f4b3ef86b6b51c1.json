{"__meta": {"id": "Xf33deb6efd3c2cfb9f4b3ef86b6b51c1", "datetime": "2025-08-02 08:27:40", "utime": **********.71749, "method": "GET", "uri": "/appointments?type=past&email=raja%40test.com&name=<PERSON>%20Kumar%20Singh", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754123259.067381, "end": **********.71754, "duration": 1.6501591205596924, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1754123259.067381, "relative_start": 0, "end": **********.398598, "relative_end": **********.398598, "duration": 1.3312170505523682, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.398614, "relative_start": 1.331233024597168, "end": **********.717556, "relative_end": 1.5974044799804688e-05, "duration": 0.3189420700073242, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48901848, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET appointments", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\AppointmentController@index", "as": "appointments.index", "namespace": null, "prefix": "/appointments", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAppointmentController.php&line=108\" onclick=\"\">app/Http/Controllers/AppointmentController.php:108-183</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.03452, "accumulated_duration_str": "34.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4953341, "duration": 0.02086, "duration_str": "20.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 60.429}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.54668, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 60.429, "width_percent": 3.795}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 189}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 275}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5669012, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 64.224, "width_percent": 5.359}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 189}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 275}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.578627, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 69.583, "width_percent": 3.853}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 189}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 275}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 111}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.592749, "duration": 0.0057599999999999995, "duration_str": "5.76ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 73.436, "width_percent": 16.686}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 211}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 275}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 111}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.681004, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:211", "source": "app/Models/User.php:211", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=211", "ajax": false, "filename": "User.php", "line": "211"}, "connection": "radhe_same", "start_percent": 90.122, "width_percent": 2.839}, {"sql": "select * from `bookings` where exists (select * from `calendar_events` where `bookings`.`event_id` = `calendar_events`.`id` and `created_by` = 79) and `email` = '<EMAIL>' and `name` LIKE '%<PERSON>%' and `date` < '2025-08-02' and `status` != 'canceled' order by `date` desc, `time` desc", "type": "query", "params": [], "bindings": ["79", "<EMAIL>", "%<PERSON>%", "2025-08-02", "canceled"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AppointmentController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\AppointmentController.php", "line": 170}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.693167, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "AppointmentController.php:170", "source": "app/Http/Controllers/AppointmentController.php:170", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAppointmentController.php&line=170", "ajax": false, "filename": "AppointmentController.php", "line": "170"}, "connection": "radhe_same", "start_percent": 92.961, "width_percent": 7.039}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/appointments", "status_code": "<pre class=sf-dump id=sf-dump-1907787843 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1907787843\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-190344565 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">past</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"13 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><PERSON></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190344565\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1298255755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298255755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1790205698 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/leads/16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpKWldSZVBzall3VjBKWitkZ2hQc2c9PSIsInZhbHVlIjoiN0VHK1hhem9OcENEeHBtNng0aXB5aXhnYlRRaWxub1E0TWY4dmZkblk2Sm5TeVluQSsrRjNNellRdldxRVg2RDRZVko3REtXV2k2VjIrMjVwNCtTNFFpOEl5T3NCZE10N1pBa1RmUkIxajVlWHU4ZzlJckxQN2UzSVM2VVpRWGxMdDhqM20zVGp6ekFzL0dlcVppY0NEWHpGOC82cGFMQXp2RVpXSHBoMXNVR291cE1GSGYvanNNTlBSWE1GejhJMkZJVXA2d3Jld2VmNGg1VUE2UCtYcmxpN2sxd0dYZ0RIeE1nR1FLNzhGOWU5aGtXV1BEenlKVXYxbjI2Q0RPSnNjWGVGa2tiUFJSVm0wUWRncUxyV1doUlJzRzUxTTZxamtnT0dDekhsVm8xZWdoVFJtcHFQbEdKUHBCMWYvdlNIMjIzZmZlKzl1U1JLcURpZGM4WGlHaklOeU92TWd6VExubkZtMDRMQ0hsR3NyTno4bHRwQml5blJyN043aUtaS1FZQlpCNEdFTjhQUDhlM3ZFNjEzay9BRE1QL1MySGEyYnJnQ2dhNzhxZGswaDE1L09jMERMSVB4OGw0c3FmRDhuK3ZrV3ZVMHlIOEEzMUNaZjdaWmRHaDBYck9zZTkvYnlIcFY3RmJYd0hzN05Ha29lTndybGY3dHpJZElIemsiLCJtYWMiOiI2NWU2ZWU2MzcyMzMzZmY4MDEwZjUxNThmNzUzYjY1Y2I5YjU4NGRhZGY3OWIyZTEzOWU0NmI5YzRhMzg2NmRjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkVqOFBacGxjZTl4L3pQbWFYQVlrWFE9PSIsInZhbHVlIjoiLzZ5dzU4R3NZVUp5dnBuY2FjT2lXZFQ0WW0vSVB3YWg3Y09DQXlhQ2wyUGg1NVFpUmltYUx1RjV0cmZpNGFhd2wraFJwcWp5ZGQxNWJUVVZNSUsrZmRoTGhvZGFhQ1MyN0UyRHhidmRabjl2SWpwMkJ3ZWExR3hoUGZNbmhZYjNrejdhNEtydXBYOTBXOTNkRHBlMndkYWQ2d1hJbnNmUEd0WTBrZGJmcnBPbUZjOUVISGZXL29NWkdYT2I0d1ZOOTAyY04vMUUzUVNiYlp4NW13QWRkZEY1VTE3QWpwOGlCV0d2alVjT1RVVEtjYkdnWXlYd0VQckpIbS9TYjNvNXNpSndtMzlqZ1NEV3YwOTcwSlRPZ1M4bnp0Y0pqaWtUMlR6SFVrTUQ4a2lYQmRyL2NSUDNBZDkyaUdvRnNlM2hOaVVDdnhzMFBtd3hkMEE0NDdzby9JSW5PWlNnQStqcFppdmJVL3FSWVUxVXZOeUpWSjBkYWllTnRScThYbXREaTk5bzA0K2hCcEk0Yk0xMGxZd29INWRKcUxCWUJ5a0V3R2luVE41blB4cGdlOXlEcHlWUysvZURNVHV4dTJTcHhtRjMvUFpYUGpPaWV3Mk1aaFdEd3NQc0VDdmVoaU01UXArQkZUN2dKUmNKVzgvVzZKNHlrZzJ3V0xzWEpTMWwiLCJtYWMiOiI0MTJmZWY4Yzg3MmZiY2VjZWNjZTcxM2JjZDc0OGY0NTgyMTU1NWY1YTdjNGU1YjM3M2Q2MTYxMTNhY2RhMGU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790205698\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-916213131 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916213131\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1343998351 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:27:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBza0hXSWNWdk55cjFZcGlKZ001WUE9PSIsInZhbHVlIjoiZ3R1VGhNOFNBd0RNOHJONXRScVo3bUp1YmVxLytoZVNML3JVQUJYQnZpSlBCSzFiTzJjVVI0NXZCcUFSdHVxY2FIY1hOd2owNEpMT081NEpIWi83V3pTTGYwdERmSUhsdlFBMUpNY3J6L3FSaGVrb1Q2Q0o4aVZ3cWx0NWRzMFNGbGtySGJ3WmlMYmFoblBQSUxROEcxZzU0dzZsZ1JRU2Z4Q3lpcVpwMWtRTVBmdy9NbEcrMWhJVmJpS3pkK09FM3F2TGtic0VKdUk3ZFdTcExGdmlDdHhEWjlFS1BQN09vYmxZZWxrQ1hMOGxTb0pmVktKYmI2NkxWSlNxenJrQUc3b3dhWnppSmYzK0tWTEhCbmFLM1pTdWdwL2FydHNaU2pFazVJNXJVN0dzL1ZBQThQaW1TR0NtMUdTQWgvRTNjS2NaVFVCWUlWTXc3aFlKSmZhM1BGUVBFaENjT2FZNGNBamlhUUtIRjRla1JLSTNmKzlLNzRHUjBvWE4zbDJUMnY1K091eFY2TGNaTk1YMnRtS2dZNVJCVU5QOGV0YU1ieG1Sazh6cE80dGJZNitsTHcxTmtQL29pdEoxUzFwbGI1bUZuRXNiL1JzOUdmc1NVZDJtT2RaUEtkTG83c1ZWdHZXKy9vaHJPR2F6TGdtS0FBbHp1NG9xcTVVUmdiVUUiLCJtYWMiOiI3ODk1MWVhZTQwYmVkMmIzMWIyNjk4NGM4ZmUxODlhMjY4MjRjNWQyNTE2OTkwZTJlY2MwYzQzY2UyMTA5MzIyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:27:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjljYi9meEYyQ1dqbXpRY0dadXptRFE9PSIsInZhbHVlIjoiOGFzbFdjb2ExZlZiaFVHZ3l3UmRBeUp2UHViZ0FrcGlhMlJtZTdRMWdHMXZ0MFdIZjBKTHFXaE55RVJ6OGRMTEttUjhZc2xkS0p1cllZMjVBdkhsT3pxcjE4QXk2aTM1TU1FbUZwZDN5SWsrTmRDdm5MOGNad29BK1dyOExpVFFWczZqN1NCZDR5RmNQZHhTOXhsWW9PTnRlUTZiOHVwbk5SWTdGMzVDR241OEp5VmI3N293K3NtTVNXYWtOdFhpZE11UG1NWFEwTkpab1V1UXFGZDc5Y3dtRnJIV2cyUXZpejRzZ25ja3gyMEVKbGp2L1gvVjdadmFQZ2o1eUlNUEFMejgvWDdVTHNwbURkUEtuNGxueCs3Ull1MTVhRlVlL1I4UWNnVmtyT2t5SmVwYVVGclFvc0VocWlXcHpTdDFwTWRBMHdwUXVLQU94QUsvMlZXbVlwRE03S1R6blNmeTBsK0kwM0hYTW9DVU9iRDFRYUF0OFFRR0VoTU02ZEZReDUrcVJvMnpUVitUOE82RlBuc2NaTEs1bzNDdUpmejloK0tlOWpZenIrYXRLYXBRbldleTh3dXhTUnV0TnVuYkkzK0ZWSy83YXNlc3MvdnFhYm50L3R2Wm83RkJ5OHAzM01oanV0cVl5eHgrNDY3YlYySSs3aW12Wm14Ti9RdVUiLCJtYWMiOiJjODE1NDRjYzI0N2Y5ZDNiOGM0ODBhZDBjMzg2MDNmYzkwZGU5ODg0ZDllYzMzNmJlZDMzMzViNDgyYWVlYmMwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:27:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBza0hXSWNWdk55cjFZcGlKZ001WUE9PSIsInZhbHVlIjoiZ3R1VGhNOFNBd0RNOHJONXRScVo3bUp1YmVxLytoZVNML3JVQUJYQnZpSlBCSzFiTzJjVVI0NXZCcUFSdHVxY2FIY1hOd2owNEpMT081NEpIWi83V3pTTGYwdERmSUhsdlFBMUpNY3J6L3FSaGVrb1Q2Q0o4aVZ3cWx0NWRzMFNGbGtySGJ3WmlMYmFoblBQSUxROEcxZzU0dzZsZ1JRU2Z4Q3lpcVpwMWtRTVBmdy9NbEcrMWhJVmJpS3pkK09FM3F2TGtic0VKdUk3ZFdTcExGdmlDdHhEWjlFS1BQN09vYmxZZWxrQ1hMOGxTb0pmVktKYmI2NkxWSlNxenJrQUc3b3dhWnppSmYzK0tWTEhCbmFLM1pTdWdwL2FydHNaU2pFazVJNXJVN0dzL1ZBQThQaW1TR0NtMUdTQWgvRTNjS2NaVFVCWUlWTXc3aFlKSmZhM1BGUVBFaENjT2FZNGNBamlhUUtIRjRla1JLSTNmKzlLNzRHUjBvWE4zbDJUMnY1K091eFY2TGNaTk1YMnRtS2dZNVJCVU5QOGV0YU1ieG1Sazh6cE80dGJZNitsTHcxTmtQL29pdEoxUzFwbGI1bUZuRXNiL1JzOUdmc1NVZDJtT2RaUEtkTG83c1ZWdHZXKy9vaHJPR2F6TGdtS0FBbHp1NG9xcTVVUmdiVUUiLCJtYWMiOiI3ODk1MWVhZTQwYmVkMmIzMWIyNjk4NGM4ZmUxODlhMjY4MjRjNWQyNTE2OTkwZTJlY2MwYzQzY2UyMTA5MzIyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:27:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjljYi9meEYyQ1dqbXpRY0dadXptRFE9PSIsInZhbHVlIjoiOGFzbFdjb2ExZlZiaFVHZ3l3UmRBeUp2UHViZ0FrcGlhMlJtZTdRMWdHMXZ0MFdIZjBKTHFXaE55RVJ6OGRMTEttUjhZc2xkS0p1cllZMjVBdkhsT3pxcjE4QXk2aTM1TU1FbUZwZDN5SWsrTmRDdm5MOGNad29BK1dyOExpVFFWczZqN1NCZDR5RmNQZHhTOXhsWW9PTnRlUTZiOHVwbk5SWTdGMzVDR241OEp5VmI3N293K3NtTVNXYWtOdFhpZE11UG1NWFEwTkpab1V1UXFGZDc5Y3dtRnJIV2cyUXZpejRzZ25ja3gyMEVKbGp2L1gvVjdadmFQZ2o1eUlNUEFMejgvWDdVTHNwbURkUEtuNGxueCs3Ull1MTVhRlVlL1I4UWNnVmtyT2t5SmVwYVVGclFvc0VocWlXcHpTdDFwTWRBMHdwUXVLQU94QUsvMlZXbVlwRE03S1R6blNmeTBsK0kwM0hYTW9DVU9iRDFRYUF0OFFRR0VoTU02ZEZReDUrcVJvMnpUVitUOE82RlBuc2NaTEs1bzNDdUpmejloK0tlOWpZenIrYXRLYXBRbldleTh3dXhTUnV0TnVuYkkzK0ZWSy83YXNlc3MvdnFhYm50L3R2Wm83RkJ5OHAzM01oanV0cVl5eHgrNDY3YlYySSs3aW12Wm14Ti9RdVUiLCJtYWMiOiJjODE1NDRjYzI0N2Y5ZDNiOGM0ODBhZDBjMzg2MDNmYzkwZGU5ODg0ZDllYzMzNmJlZDMzMzViNDgyYWVlYmMwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:27:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343998351\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-83221691 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83221691\", {\"maxDepth\":0})</script>\n"}}