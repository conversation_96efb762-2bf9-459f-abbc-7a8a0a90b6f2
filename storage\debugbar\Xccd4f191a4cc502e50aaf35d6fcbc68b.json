{"__meta": {"id": "Xccd4f191a4cc502e50aaf35d6fcbc68b", "datetime": "2025-08-02 10:28:34", "utime": **********.663104, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130513.567729, "end": **********.663138, "duration": 1.0954089164733887, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": 1754130513.567729, "relative_start": 0, "end": **********.570423, "relative_end": **********.570423, "duration": 1.0026938915252686, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.570449, "relative_start": 1.****************, "end": **********.663141, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "92.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GGEX4FINBviOpWOk9TnKmkRZl47DRIUmf1zRN4pw", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1960268479 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1960268479\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1651043976 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1651043976\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1216497091 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1216497091\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-772605393 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772605393\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1992096287 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1992096287\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1620568054 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:28:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNVVk5XdjFWQWJ3YUI2RVVCM2xMRFE9PSIsInZhbHVlIjoiMXVaN3dITHJxVVN6bm9OYnBMZlBKSXhmSFRzR2FSMXVocTFTVE9tTE1xNGtZajNudmJBa2phNnppKy9tdm5VUXFOZW9aNDA4MFRhSVdvemlyTTlzalNVWUUvUTdXSS9XYy9OSHh1ZnJPYy9FU1BiUFNJN2ZEeGpLL2Z3N3dmYnFabEg3UlpZQlV1NllWME8zT3I4MUxhMDArV3kyTlpPSnFVMlRTNTUvZHY3ZTA5anprZU10YndsZTh6VVRKUlpkcmc1d283REg1b1o4K3puVmQ3SzkyY2pYTHRWWS9jTFgwamxOZFk0YmdLK2ZpbFNaejFpTUdSMk9kdlZvNFA0aVJLNnBUUHJ6QlZHZTY3ZnlQM09mVERzZDd0ak9wWXp2OXAyWjZCVm1OSnpkelhWQjYyQnBuMHd6L3RYWm91ZlU0VHljM0lRRWFEZzJabzlyUzR6ZzdVRVAwdmVkUERtQWQ4dEhpMXRoTk5QUVFXOEJ3RERMSDVUK3JhQ1Y3dHFPMnF3bzNPM2VqdkZTQmg4dS9CZ1FoVmU2ZEtCOWhFaTdjSFpQY0lwbk1vSmVSQS93MUNFalIzYU5uZEFTQU84c1NBcHRiVGpuSkFRSGZVSTQ3MEJGYnlaeTgwOEJsMFNKbEVtOVRDWGJ4ajB6K3B6cTV4SGFSd0lPdERmQXlXb3oiLCJtYWMiOiIxZjljNDY4OTFkN2M0MzYxZmFiYmU4ZmJmOWNhOTI3ZTRmM2VkODJlMDYyMmMzMGE5ODc1YzY4MDIzN2MzM2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:28:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImNqMEVvZVgxVnZXUkdZS3VsWTdNSkE9PSIsInZhbHVlIjoiM1VybHlKSlZ0QmVYUFNicWZROElWVzhieUpBMUloQzNMWEI2L2JwZ05KWlZHeGdvZ0NmR3ZlbDFSVFJKY1AvTWpJWDl2T21KUnkrV1RSZi82UFpKZGNrcWxNL0pKWDRJSE5zMXduY1dNbTQ0QndycVJ1SVlBM1ZDd1RkNFFqQjVOdXdmZ2JRTWZwcXNWWjR0WnVscXduOWErcEkxUDVYcG9rc3RGSXRDd2pEQzJSL1lGRGlSdEIyOWx5bkVySWZXT29DL3liOFlSYjdGSEJoQmVBUXJzREEybnhyZll0ZWN1cGV6eWNOMlVIdjVNcnlrK1praHdUM250bmFVWDB4MElJMDBiTDR3NUkrTEhWaC8zSFdsSEhwZ0FmbFVTbnMva3pIRnVFL1VYcDc0V0NpYWNSeCt2dm9BYm9abVV5enVobnFGZE1mTlVRV3ZvYUJPZlJvZW83QWxGKzlVdkdwWHkyQklNNzk1TnExQlgwV2hjNDVocGZKSm9MQXB1aGFpVzFZM01yQnJZbFkyL3BXUS9iVnI0Q3dlY1FFcmVBSkZXYkVMamdZUkNKSzcrQjYyZzgrdCtsSzBHV1RLazI1MVc1Ym8rMm1JdVVXbFd6YVdxenQwR3ZzbGg3ZFVOaWdKWS81cVVpTWRnWWNsOVk1c2xCRk11MEwycDJZVTlScTYiLCJtYWMiOiI2OWRlYzE5M2NlZjNlYzk0YzAyY2IwNDVkMmM5YjZkYTQ2NTBiM2I4NTk4MmEzMDNkMTg3MzQ3MGVjNDIzN2NhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:28:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNVVk5XdjFWQWJ3YUI2RVVCM2xMRFE9PSIsInZhbHVlIjoiMXVaN3dITHJxVVN6bm9OYnBMZlBKSXhmSFRzR2FSMXVocTFTVE9tTE1xNGtZajNudmJBa2phNnppKy9tdm5VUXFOZW9aNDA4MFRhSVdvemlyTTlzalNVWUUvUTdXSS9XYy9OSHh1ZnJPYy9FU1BiUFNJN2ZEeGpLL2Z3N3dmYnFabEg3UlpZQlV1NllWME8zT3I4MUxhMDArV3kyTlpPSnFVMlRTNTUvZHY3ZTA5anprZU10YndsZTh6VVRKUlpkcmc1d283REg1b1o4K3puVmQ3SzkyY2pYTHRWWS9jTFgwamxOZFk0YmdLK2ZpbFNaejFpTUdSMk9kdlZvNFA0aVJLNnBUUHJ6QlZHZTY3ZnlQM09mVERzZDd0ak9wWXp2OXAyWjZCVm1OSnpkelhWQjYyQnBuMHd6L3RYWm91ZlU0VHljM0lRRWFEZzJabzlyUzR6ZzdVRVAwdmVkUERtQWQ4dEhpMXRoTk5QUVFXOEJ3RERMSDVUK3JhQ1Y3dHFPMnF3bzNPM2VqdkZTQmg4dS9CZ1FoVmU2ZEtCOWhFaTdjSFpQY0lwbk1vSmVSQS93MUNFalIzYU5uZEFTQU84c1NBcHRiVGpuSkFRSGZVSTQ3MEJGYnlaeTgwOEJsMFNKbEVtOVRDWGJ4ajB6K3B6cTV4SGFSd0lPdERmQXlXb3oiLCJtYWMiOiIxZjljNDY4OTFkN2M0MzYxZmFiYmU4ZmJmOWNhOTI3ZTRmM2VkODJlMDYyMmMzMGE5ODc1YzY4MDIzN2MzM2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:28:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImNqMEVvZVgxVnZXUkdZS3VsWTdNSkE9PSIsInZhbHVlIjoiM1VybHlKSlZ0QmVYUFNicWZROElWVzhieUpBMUloQzNMWEI2L2JwZ05KWlZHeGdvZ0NmR3ZlbDFSVFJKY1AvTWpJWDl2T21KUnkrV1RSZi82UFpKZGNrcWxNL0pKWDRJSE5zMXduY1dNbTQ0QndycVJ1SVlBM1ZDd1RkNFFqQjVOdXdmZ2JRTWZwcXNWWjR0WnVscXduOWErcEkxUDVYcG9rc3RGSXRDd2pEQzJSL1lGRGlSdEIyOWx5bkVySWZXT29DL3liOFlSYjdGSEJoQmVBUXJzREEybnhyZll0ZWN1cGV6eWNOMlVIdjVNcnlrK1praHdUM250bmFVWDB4MElJMDBiTDR3NUkrTEhWaC8zSFdsSEhwZ0FmbFVTbnMva3pIRnVFL1VYcDc0V0NpYWNSeCt2dm9BYm9abVV5enVobnFGZE1mTlVRV3ZvYUJPZlJvZW83QWxGKzlVdkdwWHkyQklNNzk1TnExQlgwV2hjNDVocGZKSm9MQXB1aGFpVzFZM01yQnJZbFkyL3BXUS9iVnI0Q3dlY1FFcmVBSkZXYkVMamdZUkNKSzcrQjYyZzgrdCtsSzBHV1RLazI1MVc1Ym8rMm1JdVVXbFd6YVdxenQwR3ZzbGg3ZFVOaWdKWS81cVVpTWRnWWNsOVk1c2xCRk11MEwycDJZVTlScTYiLCJtYWMiOiI2OWRlYzE5M2NlZjNlYzk0YzAyY2IwNDVkMmM5YjZkYTQ2NTBiM2I4NTk4MmEzMDNkMTg3MzQ3MGVjNDIzN2NhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:28:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620568054\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-966222017 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GGEX4FINBviOpWOk9TnKmkRZl47DRIUmf1zRN4pw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966222017\", {\"maxDepth\":0})</script>\n"}}