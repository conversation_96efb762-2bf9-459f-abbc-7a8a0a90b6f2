{"__meta": {"id": "X5f85014cfca1e980dc4c543584304705", "datetime": "2025-08-02 09:15:58", "utime": **********.620909, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754126157.824251, "end": **********.620954, "duration": 0.7967031002044678, "duration_str": "797ms", "measures": [{"label": "Booting", "start": 1754126157.824251, "relative_start": 0, "end": **********.540761, "relative_end": **********.540761, "duration": 0.7165100574493408, "duration_str": "717ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.540777, "relative_start": 0.****************, "end": **********.620958, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "80.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "spRMk46i6xR5mHBBJBfGMAGgeu2ugWsuVGL0cjm0", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1370445616 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1370445616\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2017404094 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2017404094\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1730742854 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1730742854\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1572404263 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572404263\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-167365757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-167365757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1414128584 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:15:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZEVnFqVmhRTk1WQkFVUmVBRER0dnc9PSIsInZhbHVlIjoiY3hOUGZDT2lxU3VIQ2EyUUtIdEZmSElTUlFkV2h5QUJkMGVkUHR6V1k3angzblYxa29YWllQSFBnaGQxcmVRNXlGZ01GejhIMjlPR3VsSGRNQ0d2ZEg0a01YR1R0QXk2UkV4ZElsa0pFT1ZqQnNVS1g2dVNnZFd3WHNxUGNLMGpBak1IQmRXSVJyUFVFVG5ObE9KMjNEcCtHRXF2bkFXeUVpbTZIU3NpVWE4VStrMVVNQTJyZ0RYSUFzNUFQaFRPMjRwbXlYY3RSd3l5N1M0T0JsT3Y4QVNzRnF0Q0c5Y2VNM3NZMEpUanJwSlZGczBZSGYrUDRLQWNxd3BMem56QXBhUkorQ1A1aTRBcjZlT24xZmc2eE1jQm9PTlNNbER1MEFvR3hDMjVhc0cvMksyVzJhV25QNG4wM0NCcFNjcVE2bnJBeVFvQ05oYlgzRVJHR2ZxY0RUajVYczZiamREUW90YUJBMTF2M0JKdkRzS2ZGOCthb2E5eU12Q1ZzMTFBblREQksrRkJ6WTdYOHpkZDhDWUNxc0lTYkJ3QmdDRUJ4THJvQ3FxNnFpUHQ4cWRqMm5BMkJnc0wwVmlBNUIzdTlLZ0NhWURyOVBoN2F6OFd1Y2FMaXUvOExYc0ZuOTN0OFlzTDQ4UStjR0FsYW5xeUFPZXY5NWptQkVuaGEvZEQiLCJtYWMiOiI1OGYwMmQ4ZjEzNzhlMjIwYmU1YTM2MTc5NjhhNzA0ZDIyZWY1Yzg1NGZkMTM5YzI2ZWFlYzg0ZGJlZDUyNWNlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:15:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImdCRjFDaCtWcFliMFRNVjRCeG5LV1E9PSIsInZhbHVlIjoiZmZDTnB5WDdHZkJFV2VxckdtVnFIVWJ0YWdnLzJvc2xaeUk5bEQ0SGtCM0EvT2xNdjludzJvOE1lZ3hDN3hFVnhjSzN0OVpRc2J5UXFqYmhGbXl5R2NpQnVFTVJXYUR0MHg4T0ZXdm5uejR3NHpuQUdmTWNVUG96ZlN0MmZSZ0JLQnA0anNhTEp6UGEvWXlRWWRVbmpYaVhwdU0vcm1GWjhoTHVHZGRVVFJ3NkhlR1d4QmlwTisrM3VRREw2eDlPOGpoNUhlM3Q1NFJMRllzOXBEK0Yvc2J5YXVSYzFyZm5GYUNRZVJ6SHlhZ2xNdkhwYkJ2RlFvQzlMSFRMOGhTQmpGVzdUekNlWTYzbHV0OEtMckJ3dnZQNUEyVDNYcW1QQWV3dlMvYlhLbFFHMmpBL25mbjNoNWJFcDRDRGpaTnp0UXkwbUIxVjFQQ2l1Zk9LN3lhNm1XU2p0clFnTzlJbVM2N3dwQ2Q5Rm0rMWVlUTYveEVhZVRSb0c3ZE5ycGM4RHVOZ0tNaWJCdFBENjFsSGRPQ2ZpenlKaE5zYXcwZVZvOUJ3bEtsY1hZVXBFL2VmT3FGQXQ4TFdaUHUwTm1Lb210MGdES1c1WXZsTU9lVkl0K2Y0S2VVSHVhM3o4bUZOMWRrVzdRbXBpMTZ4RGhZQnZ5Y25KTXBFYllLRTdXZ20iLCJtYWMiOiI5ZGZkN2NlMmVmNDczMjE4MDJlZjA5Y2VkZTdhMWYwY2Y4Mzk5OGNhOTJhMTg0ZGFjY2I2NmNkYmM2NjgwNWRiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:15:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZEVnFqVmhRTk1WQkFVUmVBRER0dnc9PSIsInZhbHVlIjoiY3hOUGZDT2lxU3VIQ2EyUUtIdEZmSElTUlFkV2h5QUJkMGVkUHR6V1k3angzblYxa29YWllQSFBnaGQxcmVRNXlGZ01GejhIMjlPR3VsSGRNQ0d2ZEg0a01YR1R0QXk2UkV4ZElsa0pFT1ZqQnNVS1g2dVNnZFd3WHNxUGNLMGpBak1IQmRXSVJyUFVFVG5ObE9KMjNEcCtHRXF2bkFXeUVpbTZIU3NpVWE4VStrMVVNQTJyZ0RYSUFzNUFQaFRPMjRwbXlYY3RSd3l5N1M0T0JsT3Y4QVNzRnF0Q0c5Y2VNM3NZMEpUanJwSlZGczBZSGYrUDRLQWNxd3BMem56QXBhUkorQ1A1aTRBcjZlT24xZmc2eE1jQm9PTlNNbER1MEFvR3hDMjVhc0cvMksyVzJhV25QNG4wM0NCcFNjcVE2bnJBeVFvQ05oYlgzRVJHR2ZxY0RUajVYczZiamREUW90YUJBMTF2M0JKdkRzS2ZGOCthb2E5eU12Q1ZzMTFBblREQksrRkJ6WTdYOHpkZDhDWUNxc0lTYkJ3QmdDRUJ4THJvQ3FxNnFpUHQ4cWRqMm5BMkJnc0wwVmlBNUIzdTlLZ0NhWURyOVBoN2F6OFd1Y2FMaXUvOExYc0ZuOTN0OFlzTDQ4UStjR0FsYW5xeUFPZXY5NWptQkVuaGEvZEQiLCJtYWMiOiI1OGYwMmQ4ZjEzNzhlMjIwYmU1YTM2MTc5NjhhNzA0ZDIyZWY1Yzg1NGZkMTM5YzI2ZWFlYzg0ZGJlZDUyNWNlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:15:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImdCRjFDaCtWcFliMFRNVjRCeG5LV1E9PSIsInZhbHVlIjoiZmZDTnB5WDdHZkJFV2VxckdtVnFIVWJ0YWdnLzJvc2xaeUk5bEQ0SGtCM0EvT2xNdjludzJvOE1lZ3hDN3hFVnhjSzN0OVpRc2J5UXFqYmhGbXl5R2NpQnVFTVJXYUR0MHg4T0ZXdm5uejR3NHpuQUdmTWNVUG96ZlN0MmZSZ0JLQnA0anNhTEp6UGEvWXlRWWRVbmpYaVhwdU0vcm1GWjhoTHVHZGRVVFJ3NkhlR1d4QmlwTisrM3VRREw2eDlPOGpoNUhlM3Q1NFJMRllzOXBEK0Yvc2J5YXVSYzFyZm5GYUNRZVJ6SHlhZ2xNdkhwYkJ2RlFvQzlMSFRMOGhTQmpGVzdUekNlWTYzbHV0OEtMckJ3dnZQNUEyVDNYcW1QQWV3dlMvYlhLbFFHMmpBL25mbjNoNWJFcDRDRGpaTnp0UXkwbUIxVjFQQ2l1Zk9LN3lhNm1XU2p0clFnTzlJbVM2N3dwQ2Q5Rm0rMWVlUTYveEVhZVRSb0c3ZE5ycGM4RHVOZ0tNaWJCdFBENjFsSGRPQ2ZpenlKaE5zYXcwZVZvOUJ3bEtsY1hZVXBFL2VmT3FGQXQ4TFdaUHUwTm1Lb210MGdES1c1WXZsTU9lVkl0K2Y0S2VVSHVhM3o4bUZOMWRrVzdRbXBpMTZ4RGhZQnZ5Y25KTXBFYllLRTdXZ20iLCJtYWMiOiI5ZGZkN2NlMmVmNDczMjE4MDJlZjA5Y2VkZTdhMWYwY2Y4Mzk5OGNhOTJhMTg0ZGFjY2I2NmNkYmM2NjgwNWRiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:15:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414128584\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-343461579 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">spRMk46i6xR5mHBBJBfGMAGgeu2ugWsuVGL0cjm0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343461579\", {\"maxDepth\":0})</script>\n"}}