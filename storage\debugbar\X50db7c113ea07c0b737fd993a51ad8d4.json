{"__meta": {"id": "X50db7c113ea07c0b737fd993a51ad8d4", "datetime": "2025-08-02 10:30:15", "utime": **********.011908, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130613.884312, "end": **********.011941, "duration": 1.127629041671753, "duration_str": "1.13s", "measures": [{"label": "Booting", "start": 1754130613.884312, "relative_start": 0, "end": 1754130614.926211, "relative_end": 1754130614.926211, "duration": 1.0418992042541504, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754130614.926227, "relative_start": 1.****************, "end": **********.011944, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "85.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XtrFNEvBYK68X7VKoC9DLTer0N1NfFLEHUijaO6d", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-635844569 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-635844569\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-614916692 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-614916692\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1641660721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1641660721\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1989680444 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989680444\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1047414453 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1047414453\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1136333477 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:30:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxtQ1MrYmpjMnhSa0szcXVsWlFSQVE9PSIsInZhbHVlIjoiejc5NGs2alJuUjFqb1dwbTNyVURHUkhhK3B6aGdSNm9pdkUvTnp3dWNDR0hQR09jVVZiVjEybFhYQWs0dm42TFIxU0QyYkhGaGtkMFRrMm5WTnAvQ2RpRmRCVFo4cncrKzIrTzRYdjRlZnFWL2ZLUlJNcjhyS3NCME1JbTE1Mm5GTlhkOThUM1ZscVJCRHRmSmpHZUgxYlBGaGhocUttWWF6S1BUTXRGd3BPMFkrVTUyeUJ4QVlZWGdUMmJlWUlTRXNydjh0bGErUlZ1YVYwcWVPRW1uNHk1VThwRjk5R1ZKa3hVaWZaZUhvYkJqT29iUmNDVGRvTmZmU29HSENvaVBpV3hyN0ZZNVBjWURTZEN5QUF0UzlubEJlRGJ0MUtvWSthblJMT1l4b1pVSEVDZjFCRExaOStldldjNWZ4ZGtPSmNuaHpCSXhLaDI1K2d5TXZpTHdPZW9JczQ3Y0JiN3B4M1Vzc2hPeko1bDFEU1ZhV1dmeFp3N0x0NFhyL1d2cHpOTWhSVUlLc0d2SXNpOTFVT1g4MzIxb1VEbHY3NVBtQU11K2E5NXJ0ZE5NYStUWW5tdDdham9QOGx4MXlMUkFCNmxWeXJ0TzJsNE1mcWxmN1NwMEdMK1cxdVpVSlVtUTJzVmVJVWFvVVBodXg2ZVVKTjNoeWxKNzRSN2M5d3MiLCJtYWMiOiI4OTZhOTYxYjI3NDhkODllYmFhMDAyYzA0YjU3OGE5OWIzMzM2OTMwMjY4MDlkNGNjZWU2ZjFmZmQ4OTg5NDk5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:30:14 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InVwN1ZyUFFEODNuUjU5bEJQTzB4Y3c9PSIsInZhbHVlIjoicVRMcnl4djZGTGxmZDcwbjg5RmtPbXNOeThmTjRUd3pRbnptbHF5RFIzSGFoeUkvaGsycjBsZmp2WE1PY0ZGZGMxZVFQdFR0L3cwSjF4UElKMjhuWEVUQnhXRGhCUWFJcDYvNFNhNk9kRHNrNitLRGlyNHZZSmVCNGZpVXUxRTQzTnJqZW9kMUVIbGZLd1NxaEkzUDEwVTVDa0FyOVJsMG5yaThZQ2x4OEpCaUpHa25LRkZMR3IwZTBpNW9SU29oYzJaeW5DVjhoNVNkdEtxRzh6YTlhMitZME5oS1pLSDVvZVl6TjVsSG9MUlVhRTBRNW9kdnZ2V2ltdTJxc0ZRT0phWEpVZVpxTmpCMFJNZkJQTGZ1NGVQc2VHVmppd1k1KzBpQlFDT2dlVG8vdU5rdTRkK091aHNUSjZLOTRKeFhpMzg3aisvb1ZXZHdpYjMzVkltNFBzek9EWmI5M3NadGZHYlJjcWpnbHdZdURBTm1YVmpuTFlyR0pBZ3M1WHVlTWMrRHhjTXdEeUtFOWpsSFIxWHN2VzY4TW5yWFBwMDhveVQ1QTJLeDUzQkRCQUJkdTZUNnFPZGJJUWZ0RHk5OHdwVGVOek4xa0x0dGpVemR3bzdNRUt6YWIzdUtGTzRSUS9GTURCaGxBd25oKzlWR0p2ZWxPSE1aRUxXa3ZMU1IiLCJtYWMiOiIyOGMwZDdiY2I5ZjFmZGVkOTNmYzYyN2M2ZjhjOTc2ZGExMTFjMjc2NjM4YzIwYjE5NDMwNWMwNTUyMzI5NWI0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:30:14 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxtQ1MrYmpjMnhSa0szcXVsWlFSQVE9PSIsInZhbHVlIjoiejc5NGs2alJuUjFqb1dwbTNyVURHUkhhK3B6aGdSNm9pdkUvTnp3dWNDR0hQR09jVVZiVjEybFhYQWs0dm42TFIxU0QyYkhGaGtkMFRrMm5WTnAvQ2RpRmRCVFo4cncrKzIrTzRYdjRlZnFWL2ZLUlJNcjhyS3NCME1JbTE1Mm5GTlhkOThUM1ZscVJCRHRmSmpHZUgxYlBGaGhocUttWWF6S1BUTXRGd3BPMFkrVTUyeUJ4QVlZWGdUMmJlWUlTRXNydjh0bGErUlZ1YVYwcWVPRW1uNHk1VThwRjk5R1ZKa3hVaWZaZUhvYkJqT29iUmNDVGRvTmZmU29HSENvaVBpV3hyN0ZZNVBjWURTZEN5QUF0UzlubEJlRGJ0MUtvWSthblJMT1l4b1pVSEVDZjFCRExaOStldldjNWZ4ZGtPSmNuaHpCSXhLaDI1K2d5TXZpTHdPZW9JczQ3Y0JiN3B4M1Vzc2hPeko1bDFEU1ZhV1dmeFp3N0x0NFhyL1d2cHpOTWhSVUlLc0d2SXNpOTFVT1g4MzIxb1VEbHY3NVBtQU11K2E5NXJ0ZE5NYStUWW5tdDdham9QOGx4MXlMUkFCNmxWeXJ0TzJsNE1mcWxmN1NwMEdMK1cxdVpVSlVtUTJzVmVJVWFvVVBodXg2ZVVKTjNoeWxKNzRSN2M5d3MiLCJtYWMiOiI4OTZhOTYxYjI3NDhkODllYmFhMDAyYzA0YjU3OGE5OWIzMzM2OTMwMjY4MDlkNGNjZWU2ZjFmZmQ4OTg5NDk5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:30:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InVwN1ZyUFFEODNuUjU5bEJQTzB4Y3c9PSIsInZhbHVlIjoicVRMcnl4djZGTGxmZDcwbjg5RmtPbXNOeThmTjRUd3pRbnptbHF5RFIzSGFoeUkvaGsycjBsZmp2WE1PY0ZGZGMxZVFQdFR0L3cwSjF4UElKMjhuWEVUQnhXRGhCUWFJcDYvNFNhNk9kRHNrNitLRGlyNHZZSmVCNGZpVXUxRTQzTnJqZW9kMUVIbGZLd1NxaEkzUDEwVTVDa0FyOVJsMG5yaThZQ2x4OEpCaUpHa25LRkZMR3IwZTBpNW9SU29oYzJaeW5DVjhoNVNkdEtxRzh6YTlhMitZME5oS1pLSDVvZVl6TjVsSG9MUlVhRTBRNW9kdnZ2V2ltdTJxc0ZRT0phWEpVZVpxTmpCMFJNZkJQTGZ1NGVQc2VHVmppd1k1KzBpQlFDT2dlVG8vdU5rdTRkK091aHNUSjZLOTRKeFhpMzg3aisvb1ZXZHdpYjMzVkltNFBzek9EWmI5M3NadGZHYlJjcWpnbHdZdURBTm1YVmpuTFlyR0pBZ3M1WHVlTWMrRHhjTXdEeUtFOWpsSFIxWHN2VzY4TW5yWFBwMDhveVQ1QTJLeDUzQkRCQUJkdTZUNnFPZGJJUWZ0RHk5OHdwVGVOek4xa0x0dGpVemR3bzdNRUt6YWIzdUtGTzRSUS9GTURCaGxBd25oKzlWR0p2ZWxPSE1aRUxXa3ZMU1IiLCJtYWMiOiIyOGMwZDdiY2I5ZjFmZGVkOTNmYzYyN2M2ZjhjOTc2ZGExMTFjMjc2NjM4YzIwYjE5NDMwNWMwNTUyMzI5NWI0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:30:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136333477\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-193704675 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XtrFNEvBYK68X7VKoC9DLTer0N1NfFLEHUijaO6d</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193704675\", {\"maxDepth\":0})</script>\n"}}