{"__meta": {"id": "Xbd2e65094d84dde9817f3e9294a00b64", "datetime": "2025-08-02 10:16:24", "utime": **********.216729, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754129783.025582, "end": **********.216758, "duration": 1.191175937652588, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1754129783.025582, "relative_start": 0, "end": **********.122116, "relative_end": **********.122116, "duration": 1.096534013748169, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.122157, "relative_start": 1.****************, "end": **********.216761, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "94.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "5ceDOVcEBa6d9aPmonk8QxoUClaFQRWu1AjWFQn7", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-991614326 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-991614326\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1645387126 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1645387126\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-318034579 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-318034579\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-463579370 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463579370\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-710543798 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-710543798\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-274694284 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:16:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR0UHkwdzBLK2VkWVRNYnNxTVh0ZlE9PSIsInZhbHVlIjoiMGxJWW5QdHplVDBLMFRxWTk0emJMVG50dWhlSlJLbzVYbUNtVWxJanpBbXFFNmZpanB1aTBYbWdwZC9ucWhZSkRjQmFFZ0tvMWhEc3pQbkQvOE1sUm05SjRQU2ZOWnk4R2xHWUg2Rk1GZFl0UTZUazdnSmZLSXVhWWdBS2RkcXQzb1h6TjczMjlNZWs3cWdzM0VGN3ZheC9nYUt3R3ZMbFMwRFZhMGpva2tVWU95Q2tWSmR5UXR5K3ZUVzFIVGZFaGFiOWgzNjdncm5DeUxSV1VWa1lPeW9EbVdSaXhnM2VsZEZweDhNeG1ydk0vSlJySHQ1RWM5MUMvSVRwaStzV0YzZkJmWFdSaVV1ZmQwQ3dXM0p3TkE0R2wrTVJmMnBncndEeSswaEpqYlJlbUxGZDZtRlY4NmlkUzRvM1N3NW5QUWxJVTFqcFMva1lSK1pNbkt0MWNHRHI1VWNwYnJTamwza0JrSmptN1puZE16UG5LRmFicFZWbm5HVHJlR0E5TjdISXJqaTE1cDRYZjA1dWFZU3JOaHpZZ2hsTUdZTGZzM1ZxVDZyMWNPNW84eFNiMG9UT1Boa3k0Z3BPamFoZmRBWTRZQ3FNRXN4dGRBc2ZhWDNHUjhyQmpKQlpYclFSckx1OW5TUnZEbmx5UU1wYUpjakcyampSOTVOOG9RZXAiLCJtYWMiOiIxMTM5YzA1Mzc1YWM1MDhhMmU5ZjVmNjU1MWEzNjY0NGVlMDc1NzcxNjIyMzc4NGU2MzBhOTA1MmI3NmY3YWZkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:16:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im9DNlpwSU1DTVBsL1o3dkt3SHNubFE9PSIsInZhbHVlIjoiQTc0TWJvbTNGbzY0ZWEvTnlCRitMV215eEllZVlHQ01GOUVPa1QwaVk2c1NUWllQaGxobkVqVW1jbTJ3ZlAwSnJMTEpDSmt2TjhUUDRINFhwWkczdENFSHJ1OUVMVVdkUkFycEszU1U2SzE4WmJiZ1RNc01kYWEyc2NDSkhsbWM0cWZaMlkzd3lCQkcrVVRYcVpiVmlxelhLMmNrakxUNnJDRThvVllxRDN6OEovOTdvYW1rSW5RYUhtOTFMa292QmZRUWJMMWRiSWdZZ2FtUExHRk8wcUYrSEY2WjIvOU1lR0FqbGsrL2MycVBSVUExZS9WQlcrVitUN0hJN3Bad3VrR2tqUE53Z05DSnJBbzJSdFd2YUU1aDRYSHpZMkFyS2RqYnpYdVlpR3hTcmZWUkxLRTViUis1MGFRdGJIUXlxNk1mY2hNcjZjYzhQTGN4SHhucXd5R0dSLzE0NjRIcEdJOTVOd2JRK1pFenZZMXNlcFE3OFFKdnZRdllVaFF3RlBFWG4rTHpTMTU5NXBRMXhORDVCc1RZRENnWHhKRGNZWjd1R2N1YTB6U2lKMWVSRUdicHNYZUJRSEVrVFlqRFVCRjhTY3R5SGg3VElXY2xLaXRXdXVCOVlnRDErZkdCWnkxMTFHUG1BZU51ditPakNra1N6YU9XZkIrV2E2cGQiLCJtYWMiOiJiMzRmYjE3YTE3MTI5YTkyYzhjZGU3ZDFjZTkxZTQ5ZjVjZDIzMDA1OTE0MGU0NDZkNWNlODU0MmM4MDY4YzhjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:16:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR0UHkwdzBLK2VkWVRNYnNxTVh0ZlE9PSIsInZhbHVlIjoiMGxJWW5QdHplVDBLMFRxWTk0emJMVG50dWhlSlJLbzVYbUNtVWxJanpBbXFFNmZpanB1aTBYbWdwZC9ucWhZSkRjQmFFZ0tvMWhEc3pQbkQvOE1sUm05SjRQU2ZOWnk4R2xHWUg2Rk1GZFl0UTZUazdnSmZLSXVhWWdBS2RkcXQzb1h6TjczMjlNZWs3cWdzM0VGN3ZheC9nYUt3R3ZMbFMwRFZhMGpva2tVWU95Q2tWSmR5UXR5K3ZUVzFIVGZFaGFiOWgzNjdncm5DeUxSV1VWa1lPeW9EbVdSaXhnM2VsZEZweDhNeG1ydk0vSlJySHQ1RWM5MUMvSVRwaStzV0YzZkJmWFdSaVV1ZmQwQ3dXM0p3TkE0R2wrTVJmMnBncndEeSswaEpqYlJlbUxGZDZtRlY4NmlkUzRvM1N3NW5QUWxJVTFqcFMva1lSK1pNbkt0MWNHRHI1VWNwYnJTamwza0JrSmptN1puZE16UG5LRmFicFZWbm5HVHJlR0E5TjdISXJqaTE1cDRYZjA1dWFZU3JOaHpZZ2hsTUdZTGZzM1ZxVDZyMWNPNW84eFNiMG9UT1Boa3k0Z3BPamFoZmRBWTRZQ3FNRXN4dGRBc2ZhWDNHUjhyQmpKQlpYclFSckx1OW5TUnZEbmx5UU1wYUpjakcyampSOTVOOG9RZXAiLCJtYWMiOiIxMTM5YzA1Mzc1YWM1MDhhMmU5ZjVmNjU1MWEzNjY0NGVlMDc1NzcxNjIyMzc4NGU2MzBhOTA1MmI3NmY3YWZkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:16:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im9DNlpwSU1DTVBsL1o3dkt3SHNubFE9PSIsInZhbHVlIjoiQTc0TWJvbTNGbzY0ZWEvTnlCRitMV215eEllZVlHQ01GOUVPa1QwaVk2c1NUWllQaGxobkVqVW1jbTJ3ZlAwSnJMTEpDSmt2TjhUUDRINFhwWkczdENFSHJ1OUVMVVdkUkFycEszU1U2SzE4WmJiZ1RNc01kYWEyc2NDSkhsbWM0cWZaMlkzd3lCQkcrVVRYcVpiVmlxelhLMmNrakxUNnJDRThvVllxRDN6OEovOTdvYW1rSW5RYUhtOTFMa292QmZRUWJMMWRiSWdZZ2FtUExHRk8wcUYrSEY2WjIvOU1lR0FqbGsrL2MycVBSVUExZS9WQlcrVitUN0hJN3Bad3VrR2tqUE53Z05DSnJBbzJSdFd2YUU1aDRYSHpZMkFyS2RqYnpYdVlpR3hTcmZWUkxLRTViUis1MGFRdGJIUXlxNk1mY2hNcjZjYzhQTGN4SHhucXd5R0dSLzE0NjRIcEdJOTVOd2JRK1pFenZZMXNlcFE3OFFKdnZRdllVaFF3RlBFWG4rTHpTMTU5NXBRMXhORDVCc1RZRENnWHhKRGNZWjd1R2N1YTB6U2lKMWVSRUdicHNYZUJRSEVrVFlqRFVCRjhTY3R5SGg3VElXY2xLaXRXdXVCOVlnRDErZkdCWnkxMTFHUG1BZU51ditPakNra1N6YU9XZkIrV2E2cGQiLCJtYWMiOiJiMzRmYjE3YTE3MTI5YTkyYzhjZGU3ZDFjZTkxZTQ5ZjVjZDIzMDA1OTE0MGU0NDZkNWNlODU0MmM4MDY4YzhjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:16:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274694284\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1323258661 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5ceDOVcEBa6d9aPmonk8QxoUClaFQRWu1AjWFQn7</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323258661\", {\"maxDepth\":0})</script>\n"}}