{{ Form::open(array('url' => 'leads', 'class'=>'needs-validation', 'novalidate', 'id' => 'create-lead-form')) }}
<div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['lead']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    
    <!-- Basic Information Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">{{ __('Basic Information') }}</h6>
        </div>  
        <div class="col-6 form-group">
            {{ Form::label('name', __('Name'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::text('name', null, array('class' => 'form-control','required'=>'required' , 'placeholder' => __('Enter Name'))) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('email', __('Email'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::text('email', null, array('class' => 'form-control','required'=>'required' , 'placeholder' => __('Enter email'))) }}
        </div>
        <div class="col-6 form-group">
            <x-mobile label="{{__('Phone')}}" name="phone" value="{{old('phone')}}" required placeholder="Enter Phone"></x-mobile>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('subject', __('Subject'),['class'=>'form-label']) }}
            {{ Form::text('subject', old('subject'), ['class' => 'form-control', 'placeholder' => __('Enter Subject (optional)')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('user_id', __('User'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::select('user_id', $users,null, array('class' => 'form-control select','required'=>'required')) }}
            <div class="text-xs mt-1">
                {{ __('Create user here.') }} <a href="{{ route('users.index') }}"><b>{{ __('Create user') }}</b></a>
            </div>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('date_of_birth', __('Date of Birth'),['class'=>'form-label']) }}
            {{ Form::date('date_of_birth', old('date_of_birth'), ['class' => 'form-control', 'placeholder' => __('Select date')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('next_follow_up_date', __('Next Follow-Up Date'),['class'=>'form-label']) }}
            {{ Form::date('next_follow_up_date', old('next_follow_up_date'), ['class' => 'form-control', 'placeholder' => __('Select date')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('type', __('Type'),['class'=>'form-label']) }}
            {{ Form::select('type', [
                'lead' => __('Lead'),
                'customer' => __('Customer'),
            ], old('type'), ['class' => 'form-control select', 'placeholder' => __('Select Type')]) }}
        </div>
    </div>

    <!-- Pipeline & Stage Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">{{ __('Pipeline & Stage') }}</h6>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('pipeline_id', __('Pipeline'),['class'=>'form-label']) }}
            {{ Form::select('pipeline_id', $pipelines ?? [], old('pipeline_id'), ['class' => 'form-control select', 'placeholder' => __('Select Pipeline')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('stage_id', __('Stage'),['class'=>'form-label']) }}
            {{ Form::select('stage_id', [], old('stage_id'), ['class' => 'form-control select', 'placeholder' => __('Select Stage'), 'id' => 'stage-select']) }}
            <small class="text-muted" id="stage-help-text">{{ __('Select a pipeline first to load stages') }}</small>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('status', __('Status'),['class'=>'form-label']) }}
            {{ Form::select('status', [
                'cold' => __('Cold Lead'),
                'warm' => __('Warm Lead'),
                'hot' => __('Hot Lead'),
                'won' => __('Won Lead'),
                'lost' => __('Lost Lead')       
            ], old('status'), ['class' => 'form-control select', 'placeholder' => __('Select Status')]) }}
        </div>
    </div>

    <!-- Opportunity Information Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">{{ __('Opportunity Information') }}</h6>
        </div>
        <div class="col-6 form-group">
            {{ Form::label('opportunity_info', __('Opportunity Info'),['class'=>'form-label']) }}
            {{ Form::text('opportunity_info', old('opportunity_info'), ['class' => 'form-control', 'placeholder' => __('Enter Opportunity Info')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('opportunity_source', __('Opportunity Source'),['class'=>'form-label']) }}
            {{ Form::select('opportunity_source', [
                'website' => __('Website'),
                'referral' => __('Referral'),
                'social_media' => __('Social Media'),
                'email' => __('Email'),
                'phone' => __('Phone'),
                'advertisement' => __('Advertisement'),
                'other' => __('Other')
            ], old('opportunity_source'), ['class' => 'form-control select', 'placeholder' => __('Select Source')]) }}
        </div>
        <div class="col-6 form-group">
            {{ Form::label('lead_value', __('Lead Value'),['class'=>'form-label']) }}
            {{ Form::number('lead_value', old('lead_value'), ['class' => 'form-control', 'placeholder' => __('Enter Value'), 'step' => '0.01', 'min' => '0']) }}
        </div>
        <div class="col-12 form-group">
            {{ Form::label('opportunity_description', __('Opportunity Description'),['class'=>'form-label']) }}
            {{ Form::textarea('opportunity_description', old('opportunity_description'), ['class' => 'form-control', 'rows' => '3', 'placeholder' => __('Enter Opportunity Description')]) }}
        </div>
    </div>

    <!-- Tags/Labels Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">
                <i class="fas fa-tags me-2"></i>{{ __('Tags & Labels') }}
            </h6>
        </div>
        <div class="col-12 form-group">
            {{ Form::label('labels', __('Tags/Labels'),['class'=>'form-label']) }}
            {{ Form::select('labels[]', $labels ?? [], old('labels'), ['class' => 'form-control select', 'multiple' => 'multiple', 'id' => 'labels-select']) }}
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                {{ __('You can select existing tags or type to create new ones. New tags will be created automatically.') }}
            </small>
            <!-- Debug info -->
            @if(isset($labels) && $labels->count() > 0)
                <small class="text-info">
                    <i class="fas fa-database me-1"></i>
                    {{ __('Available labels:') }} {{ $labels->count() }} {{ __('labels loaded from database') }}
                </small>
            @else
                <small class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    {{ __('No labels found. You can create new ones by typing.') }}
                </small>
            @endif
        </div>
    </div>

    <!-- Custom Fields Section -->
    @if(isset($customFields) && $customFields->count() > 0)
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">
                <i class="fas fa-cogs me-2"></i>{{ __('Custom Fields') }}
            </h6>
        </div>
        <div class="col-12">
            <div class="row">
                @include('customFields.formBuilder', ['customFields' => $customFields, 'customFieldValues' => $customFieldValues ?? []])
            </div>
        </div>
    </div>
    @endif
</div>

<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Create')}}" class="btn  btn-primary">
</div>

{{Form::close()}}

<style>
.select2-container--default .select2-results__option[aria-selected] {
    background-color: #e3f2fd;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2196f3;
    color: white;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #2196f3;
    border: 1px solid #2196f3;
    color: white;
    border-radius: 4px;
    padding-inline: 2px 8px;
    margin: 2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #f0f0f0;
}

/* Stage loading styles */
#stage-select:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

#stage-help-text {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

#stage-help-text i {
    font-size: 0.75rem;
}

/* Select2 styles for modal */
.select2-container {
    z-index: 9999;
}

.select2-dropdown {
    z-index: 9999;
}

.select2-container--default .select2-selection--multiple {
    min-height: 38px;
    border: 1px solid #ced4da;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #007bff;
    border: 1px solid #007bff;
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    margin: 2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
    padding-inline: 15px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
    font-weight: bold;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #f8f9fa;
}

/* Ensure clear button is always visible */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding-right: 30px;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    font-weight: bold;
    font-size: 16px;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear:hover {
    color: #333;
}
</style>

<script>
$(document).ready(function() {
    // Ensure Select2 is properly initialized in modal context
    $(document).on('shown.bs.modal', function() {
        // Reinitialize Select2 when modal is shown
        if ($('#labels-select').length) {
            $('#labels-select').select2('destroy');
            $('#labels-select').select2({
                placeholder: '{{ __("Select Tags/Labels") }}',
                allowClear: true,
                tags: true,
                width: '100%',
                dropdownParent: $('body'),
                minimumResultsForSearch: 0,
                closeOnSelect: false,
                
                createTag: function(params) {
                    if (params.term.trim() === '') {
                        return undefined;
                    }
                    
                    var existingOptions = $('#labels-select option');
                    for (var i = 0; i < existingOptions.length; i++) {
                        if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                            return undefined;
                        }
                    }
                    
                    return {
                        id: 'new_' + params.term,
                        text: params.term,
                        newTag: true
                    };
                },
                
                templateResult: function(data) {
                    if (data.newTag) {
                        return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
                    }
                    return data.text;
                },
                
                templateSelection: function(data) {
                    if (data.newTag) {
                        return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + '</span>');
                    }
                    return data.text;
                }
            }).on('select2:select', function(e) {
                if (e.params.data && e.params.data.newTag) {
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('success', 'New tag "' + e.params.data.text + '" will be created when you save the lead.', 'success');
                    }
                }
            }).on('select2:select select2:unselect', function() {
                // Show/hide clear button based on selections
                var $container = $(this).next('.select2-container');
                var $clearButton = $container.find('.select2-selection__clear');
                var hasSelections = $(this).val() && $(this).val() && $(this).val().length > 0;
                
                if (hasSelections) {
                    $clearButton.show();
                } else {
                    $clearButton.hide();
                }
            });
        }
    });
    
    // Initialize Select2 for labels with tag creation functionality
    try {
        $('#labels-select').select2({
            placeholder: '{{ __("Select Tags/Labels") }}',
            allowClear: true,
            tags: true, // Enable tag creation
            width: '100%',
            dropdownParent: $('body'), // Ensure dropdown appears above modal
            minimumResultsForSearch: 0,
            closeOnSelect: false,
        
        // Debug: Log the options being loaded
        initSelection: function(element, callback) {
            console.log('Select2 labels options:', $(element).find('option'));
            console.log('Labels data:', @json($labels ?? []));
        },
        
        createTag: function(params) {
            // Check if the term is empty
            if (params.term.trim() === '') {
                return undefined;
            }
            
            // Check if the term already exists
            var existingOptions = $('#labels-select option');
            for (var i = 0; i < existingOptions.length; i++) {
                if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                    return undefined;
                }
            }
            
            return {
                id: 'new_' + params.term, // Prefix with 'new_' to identify new tags
                text: params.term,
                newTag: true
            };
        },
        
        templateResult: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
            }
            return data.text;
        },
        
        templateSelection: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + '</span>');
            }
            return data.text;
        }
    }).on('select2:select', function(e) {
        // Show notification when a new tag is created
        if (e.params.data && e.params.data.newTag) {
            if (typeof show_toastr !== 'undefined') {
                show_toastr('success', 'New tag "' + e.params.data.text + '" will be created when you save the lead.', 'success');
            }
        }
    }).on('select2:open', function() {
        console.log('Select2 dropdown opened');
        console.log('Available options:', $('#labels-select option').length);
    }).on('select2:select select2:unselect', function() {
        // Show/hide clear button based on selections
        var $container = $(this).next('.select2-container');
        var $clearButton = $container.find('.select2-selection__clear');
        var hasSelections = $(this).val() && $(this).val().length > 0;

        if (hasSelections) {
            $clearButton.show();
        } else {
            $clearButton.hide();
        }
    });
    } catch (error) {
        console.error('Error initializing Select2 for labels:', error);
        // Fallback: Show a simple message
        if (typeof show_toastr !== 'undefined') {
            show_toastr('warning', 'Tag selection may not work properly. Please refresh the page.', 'warning');
        }
    }

    // Handle pipeline change to load stages
    $('select[name="pipeline_id"]').on('change', function() {
        var pipelineId = $(this).val();
        var stageSelect = $('select[name="stage_id"]');
        var stageHelpText = $('#stage-help-text');
        
        if (pipelineId) {
            // Clear current stages
            stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');
            
            // Show loading state
            stageSelect.prop('disabled', true);
            stageHelpText.html('<i class="fas fa-spinner fa-spin me-1"></i>{{ __("Loading stages...") }}');
            
            // Load stages for selected pipeline using direct database query
            var url = '{{ route("leads.pipelineStages") }}';
            console.log('Calling URL:', url, 'with pipeline_id:', pipelineId);
            
            $.ajax({
                url: url,
                type: 'GET',
                data: {
                    pipeline_id: pipelineId
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Stages loaded:', response);
                    
                    if (response.success && response.stages && response.stages.length > 0) {
                        response.stages.forEach(function(stage) {
                            stageSelect.append('<option value="' + stage.id + '">' + stage.name + '</option>');
                        });
                        
                        stageHelpText.html('<i class="fas fa-check-circle text-success me-1"></i>' + response.stages.length + ' {{ __("stages available") }}');
                        
                        // Show success message
                        if (typeof show_toastr !== 'undefined') {
                            show_toastr('success', response.stages.length + ' stages loaded for this pipeline.', 'success');
                        }
                    } else {
                        stageSelect.append('<option value="" disabled>{{ __("No stages found for this pipeline") }}</option>');
                        stageHelpText.html('<i class="fas fa-exclamation-triangle text-warning me-1"></i>{{ __("No stages found for this pipeline") }}');
                        
                        if (typeof show_toastr !== 'undefined') {
                            show_toastr('warning', 'No stages found for this pipeline.', 'warning');
                        }
                    }
                },
                error: function(xhr) {
                    console.error('Error loading stages:', xhr);
                    console.error('Status:', xhr.status);
                    console.error('Response:', xhr.responseText);
                    
                    var errorMessage = '{{ __("Error loading stages") }}';
                    if (xhr.status === 403) {
                        errorMessage = '{{ __("Permission denied") }}';
                    } else if (xhr.status === 404) {
                        errorMessage = '{{ __("Pipeline not found") }}';
                    } else if (xhr.status === 500) {
                        errorMessage = '{{ __("Server error") }}';
                    }
                    
                    stageSelect.append('<option value="" disabled>' + errorMessage + '</option>');
                    stageHelpText.html('<i class="fas fa-times-circle text-danger me-1"></i>' + errorMessage);
                    
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', errorMessage, 'error');
                    }
                },
                complete: function() {
                    // Re-enable the select
                    stageSelect.prop('disabled', false);
                }
            });
        } else {
            stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');
            stageHelpText.html('{{ __("Select a pipeline first to load stages") }}');
        }
    });

    // Handle form submission with AJAX
    $('#create-lead-form').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = form.find('input[type="submit"]');
        var originalText = submitBtn.val();

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).val('{{ __("Creating...") }}');

        // Clear previous errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Validate required fields
        var isValid = true;
        var requiredFields = ['name', 'email'];

        requiredFields.forEach(function(field) {
            var input = $('[name="' + field + '"]');
            if (!input.val() || input.val().trim() === '') {
                input.addClass('is-invalid');
                input.after('<div class="invalid-feedback">This field is required.</div>');
                isValid = false;
            }
        });

        if (!isValid) {
            submitBtn.prop('disabled', false).val(originalText);
            if (typeof show_toastr !== 'undefined') {
                show_toastr('error', '{{ __("Please fill in all required fields") }}', 'error');
            }
            return;
        }

        // Debug: Log form data
        var formData = form.serialize();
        var selectedLabels = $('#labels-select').val();
        console.log('Form data being submitted:', formData);
        console.log('Selected labels:', selectedLabels);
        console.log('Labels type:', typeof selectedLabels);
        console.log('Labels is array:', Array.isArray(selectedLabels));

        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Lead created successfully:', response);

                if (response.success) {
                    // Show success message
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('success', response.message || '{{ __("Lead created successfully!") }}', 'success');
                    }

                    // Log successful tag creation if any
                    var selectedLabels = $('#labels-select').val();
                    if (selectedLabels && selectedLabels.length > 0) {
                        console.log('Tags processed successfully:', selectedLabels);
                    }

                    // Close modal
                    $('.modal').modal('hide');

                    // Reload page or update leads list
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', response.message || '{{ __("Error creating lead") }}', 'error');
                    }
                }
            },
            error: function(xhr) {
                console.error('Error creating lead:', xhr);
                console.error('Status:', xhr.status);
                console.error('Response:', xhr.responseText);

                var errorMessage = '{{ __("Server error occurred. Please try again.") }}';

                if (xhr.status === 422) {
                    // Validation errors
                    var errors = xhr.responseJSON && xhr.responseJSON.errors ? xhr.responseJSON.errors : null;
                    if (errors) {
                        $.each(errors, function(field, messages) {
                            var input = $('[name="' + field + '"]');
                            input.addClass('is-invalid');
                            input.after('<div class="invalid-feedback">' + messages[0] + '</div>');
                        });
                        errorMessage = '{{ __("Please fix the validation errors") }}';
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                } else if (xhr.status === 403) {
                    errorMessage = '{{ __("Permission denied") }}';
                } else if (xhr.status === 500) {
                    // Try to get specific error message from response
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            // If JSON parsing fails, use default message
                            errorMessage = '{{ __("Server error occurred. Please check the logs for details.") }}';
                        }
                    }
                } else if (xhr.status === 0) {
                    errorMessage = '{{ __("Network error. Please check your connection.") }}';
                }

                if (typeof show_toastr !== 'undefined') {
                    show_toastr('error', errorMessage, 'error');
                }
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).val(originalText);
            }
        });
    });
});
</script>

