{"__meta": {"id": "X0f109715b7b8ce9bba66286b57605749", "datetime": "2025-08-02 09:44:29", "utime": **********.06027, "method": "POST", "uri": "/leads", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[09:44:28] LOG.info: Lead creation started {\n    \"user_id\": 79,\n    \"request_data\": {\n        \"_token\": \"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL\",\n        \"name\": \"<PERSON><PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"+916895452012\",\n        \"subject\": null,\n        \"user_id\": \"81\",\n        \"date_of_birth\": null,\n        \"next_follow_up_date\": null,\n        \"type\": null,\n        \"pipeline_id\": \"23\",\n        \"stage_id\": \"89\",\n        \"status\": null,\n        \"opportunity_info\": null,\n        \"opportunity_source\": null,\n        \"lead_value\": null,\n        \"opportunity_description\": null,\n        \"labels\": [\n            \"new_pa\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.800666, "xdebug_link": null, "collector": "log"}, {"message": "[09:44:29] LOG.info: Creating lead with data {\n    \"pipeline_id\": 23,\n    \"stage_id\": 89,\n    \"user_id\": \"81\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.032873, "xdebug_link": null, "collector": "log"}, {"message": "[09:44:29] LOG.error: Error in lead creation {\n    \"error\": \"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (pa, primary, 79, 1, 2025-08-02 09:44:29, 2025-08-02 09:44:29))\",\n    \"trace\": \"#0 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(779): Illuminate\\\\Database\\\\Connection->runQueryCallback('insert into `ta...', Array, Object(Closure))\\n#1 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\MySqlConnection.php(42): Illuminate\\\\Database\\\\Connection->run('insert into `ta...', Array, Object(Closure))\\n#2 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Query\\\\Processors\\\\MySqlProcessor.php(35): Illuminate\\\\Database\\\\MySqlConnection->insert('insert into `ta...', Array, 'id')\\n#3 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Query\\\\Builder.php(3754): Illuminate\\\\Database\\\\Query\\\\Processors\\\\MySqlProcessor->processInsertGetId(Object(Illuminate\\\\Database\\\\Query\\\\Builder), 'insert into `ta...', Array, 'id')\\n#4 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder.php(2038): Illuminate\\\\Database\\\\Query\\\\Builder->insertGetId(Array, 'id')\\n#5 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(1358): Illuminate\\\\Database\\\\Eloquent\\\\Builder->__call('insertGetId', Array)\\n#6 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(1323): Illuminate\\\\Database\\\\Eloquent\\\\Model->insertAndSetId(Object(Illuminate\\\\Database\\\\Eloquent\\\\Builder), Array)\\n#7 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(1162): Illuminate\\\\Database\\\\Eloquent\\\\Model->performInsert(Object(Illuminate\\\\Database\\\\Eloquent\\\\Builder))\\n#8 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder.php(1079): Illuminate\\\\Database\\\\Eloquent\\\\Model->save()\\n#9 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\helpers.php(380): Illuminate\\\\Database\\\\Eloquent\\\\Builder->Illuminate\\\\Database\\\\Eloquent\\\\{closure}(Object(App\\\\Models\\\\Tag))\\n#10 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder.php(1078): tap(Object(App\\\\Models\\\\Tag), Object(Closure))\\n#11 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\Traits\\\\ForwardsCalls.php(23): Illuminate\\\\Database\\\\Eloquent\\\\Builder->create(Array)\\n#12 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(2367): Illuminate\\\\Database\\\\Eloquent\\\\Model->forwardCallTo(Object(Illuminate\\\\Database\\\\Eloquent\\\\Builder), 'create', Array)\\n#13 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model.php(2379): Illuminate\\\\Database\\\\Eloquent\\\\Model->__call('create', Array)\\n#14 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\app\\\\Http\\\\Controllers\\\\LeadController.php(436): Illuminate\\\\Database\\\\Eloquent\\\\Model::__callStatic('create', Array)\\n#15 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Controller.php(54): App\\\\Http\\\\Controllers\\\\LeadController->store(Object(Illuminate\\\\Http\\\\Request))\\n#16 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\ControllerDispatcher.php(43): Illuminate\\\\Routing\\\\Controller->callAction('store', Array)\\n#17 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(262): Illuminate\\\\Routing\\\\ControllerDispatcher->dispatch(Object(Illuminate\\\\Routing\\\\Route), Object(App\\\\Http\\\\Controllers\\\\LeadController), 'store')\\n#18 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(208): Illuminate\\\\Routing\\\\Route->runController()\\n#19 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(808): Illuminate\\\\Routing\\\\Route->run()\\n#20 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(144): Illuminate\\\\Routing\\\\Router->Illuminate\\\\Routing\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#21 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\app\\\\Http\\\\Middleware\\\\XSS.php(65): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#22 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): App\\\\Http\\\\Middleware\\\\XSS->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#23 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Auth\\\\Middleware\\\\EnsureEmailIsVerified.php(41): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#24 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Auth\\\\Middleware\\\\EnsureEmailIsVerified->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#25 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\app\\\\Http\\\\Middleware\\\\EnsurePermissionsAfterPost.php(21): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#26 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): App\\\\Http\\\\Middleware\\\\EnsurePermissionsAfterPost->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#27 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\app\\\\Http\\\\Middleware\\\\FilterRequest.php(26): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#28 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): App\\\\Http\\\\Middleware\\\\FilterRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#29 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings.php(51): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#30 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#31 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Auth\\\\Middleware\\\\Authenticate.php(64): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#32 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Auth\\\\Middleware\\\\Authenticate->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#33 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken.php(88): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#34 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#35 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#36 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#37 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#38 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#39 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#40 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse.php(37): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#41 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#42 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(75): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#43 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#44 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(75): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#45 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#46 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(119): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#47 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(807): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#48 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(786): Illuminate\\\\Routing\\\\Router->runRouteWithinStack(Object(Illuminate\\\\Routing\\\\Route), Object(Illuminate\\\\Http\\\\Request))\\n#49 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(750): Illuminate\\\\Routing\\\\Router->runRoute(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Routing\\\\Route))\\n#50 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(739): Illuminate\\\\Routing\\\\Router->dispatchToRoute(Object(Illuminate\\\\Http\\\\Request))\\n#51 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(201): Illuminate\\\\Routing\\\\Router->dispatch(Object(Illuminate\\\\Http\\\\Request))\\n#52 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(144): Illuminate\\\\Foundation\\\\Http\\\\Kernel->Illuminate\\\\Foundation\\\\Http\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#53 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php(66): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#54 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#55 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php(21): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#56 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(51): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#57 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#58 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(110): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#59 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#60 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\TrustProxies.php(58): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#61 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#62 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php(21): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#63 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull.php(31): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#64 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#65 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest.php(21): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#66 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(51): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#67 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#68 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\ValidatePostSize.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#69 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\ValidatePostSize->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#70 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(110): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#71 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#72 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\HandleCors.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#73 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\HandleCors->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#74 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Http\\\\Middleware\\\\TrustProxies.php(58): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#75 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(183): Illuminate\\\\Http\\\\Middleware\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#76 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(119): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#77 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(176): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#78 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(145): Illuminate\\\\Foundation\\\\Http\\\\Kernel->sendRequestThroughRouter(Object(Illuminate\\\\Http\\\\Request))\\n#79 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\public\\\\index.php(51): Illuminate\\\\Foundation\\\\Http\\\\Kernel->handle(Object(Illuminate\\\\Http\\\\Request))\\n#80 C:\\\\xampp\\\\htdocs\\\\omx-new-saas\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\resources\\\\server.php(23): require_once('C:\\\\\\\\xampp\\\\\\\\htdocs...')\\n#81 {main}\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.040383, "xdebug_link": null, "collector": "log"}, {"message": "[09:44:29] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.055257, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754127867.906806, "end": **********.060299, "duration": 1.1534929275512695, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": 1754127867.906806, "relative_start": 0, "end": **********.725065, "relative_end": **********.725065, "duration": 0.8182590007781982, "duration_str": "818ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.725095, "relative_start": 0.818289041519165, "end": **********.060301, "relative_end": 2.1457672119140625e-06, "duration": 0.3352060317993164, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54985704, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads", "middleware": "web, verified, auth, XSS", "as": "leads.store", "controller": "App\\Http\\Controllers\\LeadController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=256\" onclick=\"\">app/Http/Controllers/LeadController.php:256-621</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.03104, "accumulated_duration_str": "31.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.769738, "duration": 0.01531, "duration_str": "15.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 49.323}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.796037, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 49.323, "width_percent": 2.738}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.809156, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 52.062, "width_percent": 2.513}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.815851, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 54.575, "width_percent": 2.771}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8207269, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 57.345, "width_percent": 8.119}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.851896, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 65.464, "width_percent": 7.474}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.876267, "duration": 0.0059900000000000005, "duration_str": "5.99ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 72.938, "width_percent": 19.298}, {"sql": "select * from `pipelines` where `created_by` = 79 and `id` = '23' limit 1", "type": "query", "params": [], "bindings": ["79", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0264611, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:322", "source": "app/Http/Controllers/LeadController.php:322", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=322", "ajax": false, "filename": "LeadController.php", "line": "322"}, "connection": "radhe_same", "start_percent": 92.236, "width_percent": 1.997}, {"sql": "select * from `lead_stages` where `id` = '89' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["89", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 323}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0300019, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:323", "source": "app/Http/Controllers/LeadController.php:323", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=323", "ajax": false, "filename": "LeadController.php", "line": "323"}, "connection": "radhe_same", "start_percent": 94.233, "width_percent": 1.385}, {"sql": "select * from `tags` where `name` = 'pa' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["pa", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 430}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.034908, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:430", "source": "app/Http/Controllers/LeadController.php:430", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=430", "ajax": false, "filename": "LeadController.php", "line": "430"}, "connection": "radhe_same", "start_percent": 95.619, "width_percent": 2.352}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.049693, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 97.97, "width_percent": 2.03}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2782, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-856830790 data-indent-pad=\"  \"><span class=sf-dump-note>create lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856830790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008113, "xdebug_link": null}]}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads", "status_code": "<pre class=sf-dump id=sf-dump-480423761 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-480423761\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1607373564 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1607373564\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-892138044 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><PERSON><PERSON> Roy Barman</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"13 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+916895452012</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>next_follow_up_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>type</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>stage_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">89</span>\"\n  \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>opportunity_info</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>opportunity_source</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>lead_value</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>opportunity_description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>labels</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">new_pa</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892138044\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">305</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijc4d3pWUTFuaGIrYVd6U1VvZ203RFE9PSIsInZhbHVlIjoidXJzVEJzQWlXK3pHL1BLeElQWTNsNWw3cEpnZkk1K3ZsblhXRUZsNUwvNmZmSGF0VW92cUptVWlxRHp0YUJYbDVhVlJDbkxwb0NlVEZMd1ozb3pjOWdsSDJtTUo2R3NNYlpLWmtPM2hEUC9qd0V0RXhyVTA2cERxYU4ya1VFKzc1ZytSNU5xaGhkcnhmbG9LWVFORnQzR1hyL1lldjdMT2xEL0ZWWUpDYWlNVmpoQmhlWThxUUR0VG5HZ1dSVkZQR2tIZGYrRHNCQVB2TFhSMjlFWGs0dWhFSVdFVk5wdTZpRVd3TXVtY2ovcVozUzB1UG84OUovaGpIczdSV0FCM090VHBRdHgvZTZWMnV4SDYzZHpaL1p0VFU1a3VITm9iNFU5S1FNMnVJM0RWbUdLQTRuVFFkTU4vTmR4OWdoZHR1VFpqRmxjZGpvV3p2TVJuditNQnhVRjc0UHVpdTRJc2ZySVgyTURvVUZFTm8yaElVL24wTWVYQlZjRW54dU15S2ZJK0Z0WEZPUWxUTU5TVSs3OENhaTQ1cjBGSVdkYk12UGFJU3BsQ1h1WlAzckZFeURHNDF1djJEQkp2SFNvM3NkbXpsWnVtUWc1Um1oNDZqMk80VUNqaTNYcW0rRWtIdFNRcXByTWI1YUxUeHNmVkdzZEwvQnlnUHptQVBtbVIiLCJtYWMiOiJlOGMxMTE5ZWI2Y2VmMDhiZjFlZDg3YjU5ZDBlM2FmMTBiZmZjNmRmNThmNTAzOTg1YWFmNGY5NDNjZTJmZjIyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Inl1WnEzTm9yQkEzcGZ5ZnBGVGE4dFE9PSIsInZhbHVlIjoiQWZHZ0RMcGpLdDFDRjgxbGRWMlAyNWtDaFB0L2RaR0NKSi9TS3ZTUHZ6ZDY5ZHNDdlVTdm9nWi9GZElkV0xBdkQxMjZxQ2o2dTNuN3hTdWNmeTJMMXJCR1lQcWlLNCtzNW9Pc0oyVXhUYmE3TTRwbTdpTjBFd241ZHFmbGs3eHM1Mk1UeDVPNU9FZFN6TjdnektoZTZXTGc0elFHZ1hLTHZBZ05ib2s4M1lBMUZoUUo0R1RVcWVVekRUNE9lTnBMYVRBbkpYeFJkczNvYVU5N0xob2xLdkxZd1V2NVZUYnp5ZVo0RjZ6R3k3ZHpCYWpHdWlIQ1hoc0k1UDBFZzJyODFnM3d1MXc1Nm40Yk9zMmF6aWgxTisvWGdiRERRbGpYTWpvMVdHVjk1Qk5LMGNvRkUwWVhZRitBR0kweTBTbDN0YjlXSEF4d2R3WlU3UE9mRnpIVndJeG1PMld4VHpIMlEyOHdETXl3N0k0ZTUzU1FHeWRrUHB3OGY2S08rZy9EWUwydTN4WHRWWXo0L0o5YTRFc1U5L09Sd1ZJUE9tWThyVlc3Q3huMjNVdmZNTjM2elN1NTVwK2JjNlg0cEMzMnBrK0dJUklDU3dEWFUxMUZUNXVDU1FxUCt2bVpTZ0J5MThwUFlEL0VZb1daRjh0ZEttVEFnTjV4bXFPY3ZkUjQiLCJtYWMiOiJkM2RmYTdlYTYyZDlhNzBhYjNmZTM2NGE5NjFiYTA2OWU1ZmYwMjllNzQ3NmFiOWIwNWU0OWIxZTQzNTE0MjhhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-543382564 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:44:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImF4S3RYTTFibXBOTFIzYWpxNDFaclE9PSIsInZhbHVlIjoiRFkvam5sNHIzczNBdVhkQ2oreWhRakRRTENsSUk1b21rSWpxQXh1eDRGNmhiV0NWbkFXTjNVNjcwdFlneG5XdTVEbjBBdzA5T1NhSlhVdndXSjBVZ0xQRTYwV1ZkcFpQMFM0SVZNOVp1MkpqWUROaHNseWJTeE9uSnpLSjRvSUVmYnlWNkZIbG82K09PWW9uY1ArSm10SVhLOGJRam40TU5lYVRkUDhwenpzcUlTK1ptZmJPNUNDTVFZOUMwZDY3YU5xVFFaYTc0bGhoV0NVUUVoOW1rODJQMnBaTzBkRm5rUzkvZERURXhwaEs0aWNaaHpEUDZPc3BxdnFjc0x0aExBS0ZXWjdadWJzTDU5b2NBeGo3RXNTbGZyNXUxd2tHYUZJdWduTGdJTHNySFFzbG1WRURsVWdkRFE0WFhiS3pJQ3NMd3pESFVvRy8wcmRwcjFieVJvSVhiVmJEd1o3WXIvSmRNSHVxMUVIS0Y0OFRYdGRmdUs4OE05bk43TFloZ3FWdVFLYlI4K1BTZFA3VDl4V2ZRTTExQlFVV0xBbzBpQjdZT3pLSno2OWxiZVQ4aTB1L0FOdDFpRktpSDgwTmhZRDE2QVh4TjhETC9aVFprY1I1SEtScCt1M0VzRkdhNWw4RzhtL2pCWlFCcmF4MjV0aXh4cWx2NEN0NVZuTlMiLCJtYWMiOiJmNGRmY2YzY2JhZjY2ZWUyYzNkMDBkZDM0OGM1OWMwNTRlNTVjNTQ3NDU0NTNmOGIwM2U2ZjFlMjVjM2M1NzQwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:44:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik4xMzJyQ0FNUnVJZHdsRnNlMlE2dVE9PSIsInZhbHVlIjoicjAyb0t5SGtzbSt0b3JpSnRxb3kxTkppNFoyTHBoNEdDTXduWnFGN3ZZNVZQek5GMjlYSHdaY0hBMldzMWpYRjg5M1Jnc1J0MmdtTzl1TEJLdTBUV2luVlR0SlV0OUdEdWNzZElNTm1OU21rS1FzSnpNTTNZb3JCeTF3VDQ0QWx4VG9GL01XUXl4ZU14ZWR3a0NaN1RxRFNIZ3pqNnZaKzJRRkxrUFdKclZGeVdTeDhrSHJmZ2huY25IWUNhbElQT0V2V0xKNzA5YXdveUJQTE1HMDh4U1BHak1rcXA0TTNPT2ZKL3BtczQ3bGtwUFRaQThBcDM4c0xBRldMblNwTStjOEQ4aGFmNC9pNEg5cUIwd1lQVk9MYkhpa2NieTJ4eVcrREtBVjhPaFpqR093OGNYeEN5RktjUVIzbmFmcnFOV3ZrREpsOEU3d0NFaUZsc1c4YVRMQ1dUM05BeENrUWdYbkJtRHd5aFhrUmVzTVl6OEFKV29VOWVhcFdVNm9tYTV3NENsQlFhTFVWTFh3MUwvT084bjZzODhBQ0NXUzB4L3NWU2prS0ZIbTM2VlJ3WjZsOWFiRStxTmRIVlZjbjR6Y1Y5cVl5TVEwbFBRU2YyMGRSNi9neEFKeVk3YVpCay9yZEJ1MXRuVWZuMzhlZlljeXEwRjh0YVFTTlJmM1MiLCJtYWMiOiI5YmRkOTVjOWUxM2U0ZjY0NDhmOGZjNjFhOWVjZDlhNTNlZmViZDU2MjI4YWM2NDFhNjI2Nzc2NWRmM2U3MzJkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:44:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImF4S3RYTTFibXBOTFIzYWpxNDFaclE9PSIsInZhbHVlIjoiRFkvam5sNHIzczNBdVhkQ2oreWhRakRRTENsSUk1b21rSWpxQXh1eDRGNmhiV0NWbkFXTjNVNjcwdFlneG5XdTVEbjBBdzA5T1NhSlhVdndXSjBVZ0xQRTYwV1ZkcFpQMFM0SVZNOVp1MkpqWUROaHNseWJTeE9uSnpLSjRvSUVmYnlWNkZIbG82K09PWW9uY1ArSm10SVhLOGJRam40TU5lYVRkUDhwenpzcUlTK1ptZmJPNUNDTVFZOUMwZDY3YU5xVFFaYTc0bGhoV0NVUUVoOW1rODJQMnBaTzBkRm5rUzkvZERURXhwaEs0aWNaaHpEUDZPc3BxdnFjc0x0aExBS0ZXWjdadWJzTDU5b2NBeGo3RXNTbGZyNXUxd2tHYUZJdWduTGdJTHNySFFzbG1WRURsVWdkRFE0WFhiS3pJQ3NMd3pESFVvRy8wcmRwcjFieVJvSVhiVmJEd1o3WXIvSmRNSHVxMUVIS0Y0OFRYdGRmdUs4OE05bk43TFloZ3FWdVFLYlI4K1BTZFA3VDl4V2ZRTTExQlFVV0xBbzBpQjdZT3pLSno2OWxiZVQ4aTB1L0FOdDFpRktpSDgwTmhZRDE2QVh4TjhETC9aVFprY1I1SEtScCt1M0VzRkdhNWw4RzhtL2pCWlFCcmF4MjV0aXh4cWx2NEN0NVZuTlMiLCJtYWMiOiJmNGRmY2YzY2JhZjY2ZWUyYzNkMDBkZDM0OGM1OWMwNTRlNTVjNTQ3NDU0NTNmOGIwM2U2ZjFlMjVjM2M1NzQwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:44:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik4xMzJyQ0FNUnVJZHdsRnNlMlE2dVE9PSIsInZhbHVlIjoicjAyb0t5SGtzbSt0b3JpSnRxb3kxTkppNFoyTHBoNEdDTXduWnFGN3ZZNVZQek5GMjlYSHdaY0hBMldzMWpYRjg5M1Jnc1J0MmdtTzl1TEJLdTBUV2luVlR0SlV0OUdEdWNzZElNTm1OU21rS1FzSnpNTTNZb3JCeTF3VDQ0QWx4VG9GL01XUXl4ZU14ZWR3a0NaN1RxRFNIZ3pqNnZaKzJRRkxrUFdKclZGeVdTeDhrSHJmZ2huY25IWUNhbElQT0V2V0xKNzA5YXdveUJQTE1HMDh4U1BHak1rcXA0TTNPT2ZKL3BtczQ3bGtwUFRaQThBcDM4c0xBRldMblNwTStjOEQ4aGFmNC9pNEg5cUIwd1lQVk9MYkhpa2NieTJ4eVcrREtBVjhPaFpqR093OGNYeEN5RktjUVIzbmFmcnFOV3ZrREpsOEU3d0NFaUZsc1c4YVRMQ1dUM05BeENrUWdYbkJtRHd5aFhrUmVzTVl6OEFKV29VOWVhcFdVNm9tYTV3NENsQlFhTFVWTFh3MUwvT084bjZzODhBQ0NXUzB4L3NWU2prS0ZIbTM2VlJ3WjZsOWFiRStxTmRIVlZjbjR6Y1Y5cVl5TVEwbFBRU2YyMGRSNi9neEFKeVk3YVpCay9yZEJ1MXRuVWZuMzhlZlljeXEwRjh0YVFTTlJmM1MiLCJtYWMiOiI5YmRkOTVjOWUxM2U0ZjY0NDhmOGZjNjFhOWVjZDlhNTNlZmViZDU2MjI4YWM2NDFhNjI2Nzc2NWRmM2U3MzJkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:44:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-543382564\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1580635855 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580635855\", {\"maxDepth\":0})</script>\n"}}