{"__meta": {"id": "X45eec05e725791fad1b05d25b53b872f", "datetime": "2025-08-02 08:29:13", "utime": **********.528551, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754123351.822984, "end": **********.528665, "duration": 1.7056810855865479, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": 1754123351.822984, "relative_start": 0, "end": **********.296472, "relative_end": **********.296472, "duration": 1.4734880924224854, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.296542, "relative_start": 1.***************, "end": **********.528861, "relative_end": 0.00019598007202148438, "duration": 0.*****************, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MmIuDFR33o0HNE50gma6q96PTiipu4phDSwK1hlG", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1327123367 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1327123367\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1004785947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1004785947\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-864098163 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-864098163\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1124977450 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124977450\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2090606237 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2090606237\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1024642264 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:29:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InMvUE1yMzhoQmMzL1piVmVQTXhiNFE9PSIsInZhbHVlIjoiN0dnNXNlTGtjSzBLbzJlTWxNVlR1MTkrYUlTVXdhczRSTWU2dWhiVkQxc2Nad3QwdnV5bGpYM1RzNEs1Yjh5NGFVUDAvVnVpUG5NWGRwWW95Uk5ia0c0enlpOG9IVTVUdXFUWm15VG4wa0F0YXdCZDhtZWg3VU45YVV3bFBNTDZSWFFRNWk3aDVBaU44N2N0N2w5aGthOWo5YkUzckt3UXBMeHdRQUhnMVNzNHI3anJlWmZRSHdxVTlQUEQvd05hc2FCbUwzMFlxTGs0TW5YQWNsbXd5UmFqRVZKbnFCVHRuK1VnNXpiZStwdWdCQThwb1dqZ2hPKzlvSi9PZWc1dEZVcWs2NCttWkRqN2VhL093aldrVFlzRGovMExaTDVVR2JiSFVMMVdUUHB6NGtRWW5rKy95UWNtNTVoZ04zV294dzhKUThBWENJK1pPRS9pUEphRWxVbHNuQWY3WlpacFlpTVRtbkpZMmdac29aL2REYlIweU1ldWdPUlFkNXllZUpzd1FWOGxpRFBBZFErSkIzbFE1eWhjZHhZSG9KS3NHbnZNa0hNc29pNkMrdTI4TzR4Z3ZaZDh2aTRNaC9ISk9sL1o3S0RmT3cyUUk0VlhwaE0yTVduVjBjY3JqQVZLYThSNitKRzhDUFZpQVpBUTJWOHBnQVhNaXpTT2VSY0siLCJtYWMiOiJlNTIyMTlmYjljMjlhNWJiZmQ3YTc0YjU5MWRkZTQzNjhiMjBmOTg5ZDZmZTQ2YzY1YTQ3ZjFmNjJlMWEwMDQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:29:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IitRVlFkb0JKWkdQTGlUYWk2OWducnc9PSIsInZhbHVlIjoid2JEazFFc2dnb3hvZnA2UnM5c1NTb01QbzdXY1VWYTRZQkdoQnZPamN5MTVqd2MzZDFOd2FYYm56S2ZYcjFyS01TTWh5SzBVaTN5dEd5VlVYdmV1VFAyNUxvTlAyaVBiNG8vQ2Fobm1HM1hnVDIrQzk1VUMrRnlXaG53WWUwTXlWckhuNklqaS8rU2U1YlYyajN6UEQzNmVtK0MyVGRFYlpFTGVlN2pVb2F2WWpEendwd0NCU1RFYW1ja3RveTZRSEtHaGNFV29IbGFZR3Zlb09vN3RtLy92NTNYRHRyV2ZTc2FIcGlEeFJMOEtQc3ozSnVzQnYrRGY0enppMXFaKzFNL0dnU3hFNm0ydWR2OVJsSHA3cjdjbUd3MkR6bk5SU2FwSVAxbnVpTk4yRE9qT0lwUjJnc0NGM3hXdHkybWdHcjJ0YnRaYnlqS0V3VThXbmgvenU5bkZ5Wk5yc0NoOWYvb3owdFpWRW9vT2l4Nkl6TDI1UThtQW9abEN0Ni9JSXhNdWEycDFlWDJ3eEFvZVZRUkZWWnc2RXM4SnVydjZML0dwTkN5RVlRRGxmeVprY2ZtSEhFRVJScDZNZ1k1aUFtRUFQa0NUeVZESmJUVGxYSUVNcytYSXZnVE9KR2oza2crQ04wNTVweE4xN3kwcGM3Mzl1UlBhY0M3LzVTSUMiLCJtYWMiOiJjNGQxZDZiZGUwMmE2MjI1MTVjMDRjYTM3ZmRhMWVjMjdmNGU1OTk0MzBkMWYzNDVkZWZjZGE2M2M4YWY5NDllIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:29:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InMvUE1yMzhoQmMzL1piVmVQTXhiNFE9PSIsInZhbHVlIjoiN0dnNXNlTGtjSzBLbzJlTWxNVlR1MTkrYUlTVXdhczRSTWU2dWhiVkQxc2Nad3QwdnV5bGpYM1RzNEs1Yjh5NGFVUDAvVnVpUG5NWGRwWW95Uk5ia0c0enlpOG9IVTVUdXFUWm15VG4wa0F0YXdCZDhtZWg3VU45YVV3bFBNTDZSWFFRNWk3aDVBaU44N2N0N2w5aGthOWo5YkUzckt3UXBMeHdRQUhnMVNzNHI3anJlWmZRSHdxVTlQUEQvd05hc2FCbUwzMFlxTGs0TW5YQWNsbXd5UmFqRVZKbnFCVHRuK1VnNXpiZStwdWdCQThwb1dqZ2hPKzlvSi9PZWc1dEZVcWs2NCttWkRqN2VhL093aldrVFlzRGovMExaTDVVR2JiSFVMMVdUUHB6NGtRWW5rKy95UWNtNTVoZ04zV294dzhKUThBWENJK1pPRS9pUEphRWxVbHNuQWY3WlpacFlpTVRtbkpZMmdac29aL2REYlIweU1ldWdPUlFkNXllZUpzd1FWOGxpRFBBZFErSkIzbFE1eWhjZHhZSG9KS3NHbnZNa0hNc29pNkMrdTI4TzR4Z3ZaZDh2aTRNaC9ISk9sL1o3S0RmT3cyUUk0VlhwaE0yTVduVjBjY3JqQVZLYThSNitKRzhDUFZpQVpBUTJWOHBnQVhNaXpTT2VSY0siLCJtYWMiOiJlNTIyMTlmYjljMjlhNWJiZmQ3YTc0YjU5MWRkZTQzNjhiMjBmOTg5ZDZmZTQ2YzY1YTQ3ZjFmNjJlMWEwMDQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:29:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IitRVlFkb0JKWkdQTGlUYWk2OWducnc9PSIsInZhbHVlIjoid2JEazFFc2dnb3hvZnA2UnM5c1NTb01QbzdXY1VWYTRZQkdoQnZPamN5MTVqd2MzZDFOd2FYYm56S2ZYcjFyS01TTWh5SzBVaTN5dEd5VlVYdmV1VFAyNUxvTlAyaVBiNG8vQ2Fobm1HM1hnVDIrQzk1VUMrRnlXaG53WWUwTXlWckhuNklqaS8rU2U1YlYyajN6UEQzNmVtK0MyVGRFYlpFTGVlN2pVb2F2WWpEendwd0NCU1RFYW1ja3RveTZRSEtHaGNFV29IbGFZR3Zlb09vN3RtLy92NTNYRHRyV2ZTc2FIcGlEeFJMOEtQc3ozSnVzQnYrRGY0enppMXFaKzFNL0dnU3hFNm0ydWR2OVJsSHA3cjdjbUd3MkR6bk5SU2FwSVAxbnVpTk4yRE9qT0lwUjJnc0NGM3hXdHkybWdHcjJ0YnRaYnlqS0V3VThXbmgvenU5bkZ5Wk5yc0NoOWYvb3owdFpWRW9vT2l4Nkl6TDI1UThtQW9abEN0Ni9JSXhNdWEycDFlWDJ3eEFvZVZRUkZWWnc2RXM4SnVydjZML0dwTkN5RVlRRGxmeVprY2ZtSEhFRVJScDZNZ1k1aUFtRUFQa0NUeVZESmJUVGxYSUVNcytYSXZnVE9KR2oza2crQ04wNTVweE4xN3kwcGM3Mzl1UlBhY0M3LzVTSUMiLCJtYWMiOiJjNGQxZDZiZGUwMmE2MjI1MTVjMDRjYTM3ZmRhMWVjMjdmNGU1OTk0MzBkMWYzNDVkZWZjZGE2M2M4YWY5NDllIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:29:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024642264\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-203972951 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MmIuDFR33o0HNE50gma6q96PTiipu4phDSwK1hlG</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203972951\", {\"maxDepth\":0})</script>\n"}}