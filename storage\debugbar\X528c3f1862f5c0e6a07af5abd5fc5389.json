{"__meta": {"id": "X528c3f1862f5c0e6a07af5abd5fc5389", "datetime": "2025-08-02 09:27:58", "utime": **********.953983, "method": "POST", "uri": "/leads/18/labels", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[09:27:58] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\\/18\\/labels\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.948101, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754126877.603749, "end": **********.954057, "duration": 1.3503079414367676, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1754126877.603749, "relative_start": 0, "end": **********.485264, "relative_end": **********.485264, "duration": 0.8815150260925293, "duration_str": "882ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.485279, "relative_start": 0.8815300464630127, "end": **********.954059, "relative_end": 1.9073486328125e-06, "duration": 0.4687798023223877, "duration_str": "469ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55005832, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads/{id}/labels", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@labelStore", "namespace": null, "prefix": "", "where": [], "as": "leads.labels.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1345\" onclick=\"\">app/Http/Controllers/LeadController.php:1345-1400</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.0463, "accumulated_duration_str": "46.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.549106, "duration": 0.018260000000000002, "duration_str": "18.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 39.438}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.583549, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 39.438, "width_percent": 1.836}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1347}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.599563, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 41.274, "width_percent": 2.052}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1347}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.606982, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 43.326, "width_percent": 3.153}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1347}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.615001, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 46.479, "width_percent": 7.624}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.670335, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 54.104, "width_percent": 8.251}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.7034209, "duration": 0.009880000000000002, "duration_str": "9.88ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 62.354, "width_percent": 21.339}, {"sql": "select * from `leads` where `leads`.`id` = '18' limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1349}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.886513, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:1349", "source": "app/Http/Controllers/LeadController.php:1349", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1349", "ajax": false, "filename": "LeadController.php", "line": "1349"}, "connection": "radhe_same", "start_percent": 83.693, "width_percent": 2.16}, {"sql": "select count(*) as aggregate from `tags` where `name` = 'Paaa' and `created_by` = '79'", "type": "query", "params": [], "bindings": ["Paaa", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.9088678, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 85.853, "width_percent": 1.987}, {"sql": "insert into `tags` (`name`, `created_by`, `is_active`, `updated_at`, `created_at`) values ('Paaa', 79, 1, '2025-08-02 09:27:58', '2025-08-02 09:27:58')", "type": "query", "params": [], "bindings": ["Paaa", "79", "1", "2025-08-02 09:27:58", "2025-08-02 09:27:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1372}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.914886, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:1372", "source": "app/Http/Controllers/LeadController.php:1372", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1372", "ajax": false, "filename": "LeadController.php", "line": "1372"}, "connection": "radhe_same", "start_percent": 87.84, "width_percent": 4.644}, {"sql": "update `leads` set `tags` = '88,90,91,92,93,94,96,97', `leads`.`updated_at` = '2025-08-02 09:27:58' where `id` = 18", "type": "query", "params": [], "bindings": ["88,90,91,92,93,94,96,97", "2025-08-02 09:27:58", "18"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1387}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9205642, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:1387", "source": "app/Http/Controllers/LeadController.php:1387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1387", "ajax": false, "filename": "LeadController.php", "line": "1387"}, "connection": "radhe_same", "start_percent": 92.484, "width_percent": 4.752}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.940171, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 97.235, "width_percent": 2.765}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2781, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2033610799 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033610799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.884538, "xdebug_link": null}]}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "success": "Tags successfully updated!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/leads/18/labels", "status_code": "<pre class=sf-dump id=sf-dump-1651973029 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1651973029\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-193071044 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-193071044\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-514892031 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">91</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">93</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"2 characters\">96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>edit_tag_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>edit_tag_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>new_tag_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paaa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514892031\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1014056589 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">191</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/leads/18</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ii9pUFMxREFERnUvKzlyMkZML21Sd1E9PSIsInZhbHVlIjoiZE9nSDdOUXcxVFgrVHAyUWdDdnZET1VGdEVUSTZkSWRHZC8va3UzL3ltbUFCWmRiYnRkbFIvTGw2dTMrM3YyRmsrUkZ1a201T2w2dVNKSkVpbTdQZ3NKc2xCblZwTHhueURZZW1BOWpzRVdONVJTVnFnU2szS0tuMW93UkQ1amlsM0lKeTFPejVObGhQeE1aVXZDM1VNVmxCM0dNZkE5UnQ2QWZXTWZOeFNEZW12aGtrSzBaRzE0LzFWbnZJenFrTEhtSzhCL01IZDdkMHFtTFUrc3RCVlhLTmF6dmk0WHVsWjBHeWljckVYMWQyQk5hTUtXSUp2YVJxemZRUzZiS1VVZ1dUdUhBUEliR2JpbjRoVzBZMGZaQjlFaUNiZHI0SU5aTU9odU8vcFViZVFVNmdtWC9yK3h3NVE1Wmt3dExpYjlpSm83Uzg2VHZ2ZHNDU29wRWpHanE1V2xaMVl1T1pxUXdVWFYxTHBBdytVVzJGN2oxY2liL2YyTjRNWDhpcWtOeXBraXpnWGU4cjlqNFBTajNJeHhrbE9EdXBONkFVM1hnNVRtUmVSSlRoTlloR2xrTFZMeHJNeElKZ3l2RzZScytHWE9nSDFNRE53OFVuSXpmMWpGMFlGWlRjdTcxTFdUck4vendCM3dBOHVubmUweFJXSGozTlBsamgrZDUiLCJtYWMiOiJkZDMwYWY4YWNhZjk3Y2NhNDVkM2ZmMTY1MzQxNGI3ZjBjMzkwM2JjMmZmNTRjYzU3NTgzYTZkYTZiZTQ1MzgxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9weTFLTWRWUU0xVFQzb3R2c0pjVUE9PSIsInZhbHVlIjoia21yUTV1TC9uaUhmb2N5YklGMWRuN0xGRVRTcXhiV2xUdXVzU0N3MDNWSlppZGYvUEorVE1EQWNpTmNscW5yNHVldERuVDhCaElLbFFKY1EzWnF5cktTUDJ1aDliUmV2U1J0bEdyR2JPZXVVYW5RWkFSYytUbjZCd2FsN1NKWVlTZ05wWGp3YXpxQXlCQ3piYUMwVnBxOUNUd1V1TEFsM3NnY1VWOXlMRllPVWpvMkFSWW4zYlBTRTg1b3VIdWVqUEw3UkFmWEFKOEswamhlY25VaDZqY1BhZ2ZoVUtZZ1hFL0YzdkNoNFppaG91ZzB1UzFwRWExQUxwaVYzSi9DRmNMVkpsc3RKTU8vOEZOR2g4YmRkV1ZrMTlIMmthN3ZJUEF5WlVVY04ybFhsMmo2aWxkM1J2elZFeDlBeWJpbC9wZFdMeU1abW9mdTRjOHMrUDhsU1V2cXl4QlAyTnNKRW8rZ3N4QjdoL1VkZVVIM1lneFhBR0NiTk8reG02MWxrWDhQZGZ2RGJRNHBkTGF0VE1zWURQZS81UnN4TkEwNW5LZ0RQM0E0VzhoYkkxeU1NdmUzdzBNWGVOc0g1YjkzRjhoZWVQNmhFbnBFM1lER1FiQVVXM2pKQ0Vab3lqazgvdEVpOW1QU2tvMmlLMzRzci8xMnhTQVp2cnlqLzkveDMiLCJtYWMiOiIyY2ZjOTBjMTM3ZDZjZGNiYWI0MDM1NTk3MTFhNWFkNTBlYjk3NTI4MWIyMzc5YTVhZGRlYjM2NDU5YzRmNWZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014056589\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-538930413 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538930413\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1375633899 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:27:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/leads/18</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZ6UGdyamErc1o1QWpQZkhTcXJ5ZXc9PSIsInZhbHVlIjoieEo4YWRwVzFmbzNJUFVqbWtpUW1tbnFpT0x6Vk1jamRLd2xlUEhNZEUzejUvcEh3VlFWNUVpU3crZEVmQ2tpb1ltUHBpM3NaZjIwUnZ0RmgxTmszSERkMzdXZTloZnV0Mi94SXJxY2tGc2txbm5vM1pTeDM2RmVpdHRUU1RBMlA1b2FGdzFoZlFXK1lzK1RwUkpSb3dTVTgrclExRm52Z3c5Z1VybDk2Y2E3TnYyMEo2S3VNT1ozTlVWaE1uZ25zZEJwcEp1RHdqVTFsNGtzVUlVUzNJdUhzbGE0Qk5pY3IxNHdDdDFVS3R5RkprYWNzZXh0NGxwQytTMjl3WjRGZ0lVRGkwaVRVbHF6Ykh4L0tGLzV3bnJvVjMwSmMyaXA1QW85UlZJNWNtVzJJMlBRQXo3cHFJdWNvcTN3UzdYVWpCWkRUc0hYb1FGYU9oM00wdklJYzUvZVBkYlIvL2VpdlFDeGx5ZzdNOTZQSWFGY1dJZ3pnY2M5RDBIZkhmVlkrcFE3eG82c0l0dEJ2TGxpV1cwb2J1VnZhYmJ2MEZlcHJRbFV5MUdqSkhsQTlpR1h0OU52amhyVnNkVHRCNWxwOVpCL2gzeFIzaWQwdXFvelkrUkNKSUJnb256UnR0alVBSHk1U0VTZm4wbWR0aVpBRVFXaDlRY09LeTE5eE9ObEoiLCJtYWMiOiI0MzMxNGMyZjY3MDViOTEyZmEyOTE5ZDA1YjBiNWMxN2ZjZDg3MzVkMzhkN2M3ZTdjYWJmZmE3NDU5ZDVkYzkyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:27:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImZyaWdFOWpJWmR5QWswWHI3SzVUeEE9PSIsInZhbHVlIjoiUFVQdGxSWXBJczFweldGQkxhcm5sRThieXBhakxVY0lZazhRVUJzU0tZVUFBZktxZXVUcDVvMnQ3T3VmaEI2ZUdHMnBucXFiblJyc2pIVUtWWWlqZ2VjL1lHZEdTM3gxWEM5SDdFaEJLR0ROVlBCcHc2ejc3MGtjUW5rRzFWWEpURG90Y0ZlQVVIUE5abHZBQkdESnZXcGJHWUdmcWJFbXhoTTN2QnJvUXg1ZGp4UzBqSGJrY2o0U0JkVVF4VlRoQkp5V2dzQUFxWVI5SGw2anZTSmFLSUJXbk5oV0NURmZzalNOMy9UZ0dodUdWM0huTVhQejc1UkNITEZ2L0hwUGpMQUhYckdKY1pVWWcwUFdoUHFid2l4SmtqWktnakQvZmllVkVPdVkweTFOa3pKTnVnODkrcys3d2hQUmNjeFpjZDZiL2tGaml3NGNiSklaWXdOVTZYZFFvb1Y3S0s4Zk9qWFM1ZEl2RnJReG9BRFUvZTgvRmkrcEdvM1FHVk9GMzM2RnVpeDV1NjRMdW83RjVhYXZNNytEaVF1cnk1KzJTZjRDS2JBaEw5N3ZaUjZQdG1CaVNCdis5WGI5eVNBL2gvVTRndVczdFBYQXVyQWJFNGpkZmFZOGlCUVBkSmhGRmtPNElQSnVsZXA1V0xMVHVKM3F1SWR5N3R0RUs3anciLCJtYWMiOiI3NjE2ZjNkZDQ5Zjc5OWUxNjQxZTJmZTI0MjI0NWFlZDk0Y2Y4YjUyNmY2NjBkODEzMWYxM2ZlNDFiZThkMTBlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:27:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZ6UGdyamErc1o1QWpQZkhTcXJ5ZXc9PSIsInZhbHVlIjoieEo4YWRwVzFmbzNJUFVqbWtpUW1tbnFpT0x6Vk1jamRLd2xlUEhNZEUzejUvcEh3VlFWNUVpU3crZEVmQ2tpb1ltUHBpM3NaZjIwUnZ0RmgxTmszSERkMzdXZTloZnV0Mi94SXJxY2tGc2txbm5vM1pTeDM2RmVpdHRUU1RBMlA1b2FGdzFoZlFXK1lzK1RwUkpSb3dTVTgrclExRm52Z3c5Z1VybDk2Y2E3TnYyMEo2S3VNT1ozTlVWaE1uZ25zZEJwcEp1RHdqVTFsNGtzVUlVUzNJdUhzbGE0Qk5pY3IxNHdDdDFVS3R5RkprYWNzZXh0NGxwQytTMjl3WjRGZ0lVRGkwaVRVbHF6Ykh4L0tGLzV3bnJvVjMwSmMyaXA1QW85UlZJNWNtVzJJMlBRQXo3cHFJdWNvcTN3UzdYVWpCWkRUc0hYb1FGYU9oM00wdklJYzUvZVBkYlIvL2VpdlFDeGx5ZzdNOTZQSWFGY1dJZ3pnY2M5RDBIZkhmVlkrcFE3eG82c0l0dEJ2TGxpV1cwb2J1VnZhYmJ2MEZlcHJRbFV5MUdqSkhsQTlpR1h0OU52amhyVnNkVHRCNWxwOVpCL2gzeFIzaWQwdXFvelkrUkNKSUJnb256UnR0alVBSHk1U0VTZm4wbWR0aVpBRVFXaDlRY09LeTE5eE9ObEoiLCJtYWMiOiI0MzMxNGMyZjY3MDViOTEyZmEyOTE5ZDA1YjBiNWMxN2ZjZDg3MzVkMzhkN2M3ZTdjYWJmZmE3NDU5ZDVkYzkyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:27:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImZyaWdFOWpJWmR5QWswWHI3SzVUeEE9PSIsInZhbHVlIjoiUFVQdGxSWXBJczFweldGQkxhcm5sRThieXBhakxVY0lZazhRVUJzU0tZVUFBZktxZXVUcDVvMnQ3T3VmaEI2ZUdHMnBucXFiblJyc2pIVUtWWWlqZ2VjL1lHZEdTM3gxWEM5SDdFaEJLR0ROVlBCcHc2ejc3MGtjUW5rRzFWWEpURG90Y0ZlQVVIUE5abHZBQkdESnZXcGJHWUdmcWJFbXhoTTN2QnJvUXg1ZGp4UzBqSGJrY2o0U0JkVVF4VlRoQkp5V2dzQUFxWVI5SGw2anZTSmFLSUJXbk5oV0NURmZzalNOMy9UZ0dodUdWM0huTVhQejc1UkNITEZ2L0hwUGpMQUhYckdKY1pVWWcwUFdoUHFid2l4SmtqWktnakQvZmllVkVPdVkweTFOa3pKTnVnODkrcys3d2hQUmNjeFpjZDZiL2tGaml3NGNiSklaWXdOVTZYZFFvb1Y3S0s4Zk9qWFM1ZEl2RnJReG9BRFUvZTgvRmkrcEdvM1FHVk9GMzM2RnVpeDV1NjRMdW83RjVhYXZNNytEaVF1cnk1KzJTZjRDS2JBaEw5N3ZaUjZQdG1CaVNCdis5WGI5eVNBL2gvVTRndVczdFBYQXVyQWJFNGpkZmFZOGlCUVBkSmhGRmtPNElQSnVsZXA1V0xMVHVKM3F1SWR5N3R0RUs3anciLCJtYWMiOiI3NjE2ZjNkZDQ5Zjc5OWUxNjQxZTJmZTI0MjI0NWFlZDk0Y2Y4YjUyNmY2NjBkODEzMWYxM2ZlNDFiZThkMTBlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:27:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375633899\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-635013707 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Tags successfully updated!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635013707\", {\"maxDepth\":0})</script>\n"}}