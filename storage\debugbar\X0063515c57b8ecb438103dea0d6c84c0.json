{"__meta": {"id": "X0063515c57b8ecb438103dea0d6c84c0", "datetime": "2025-08-02 09:09:22", "utime": **********.555034, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754125761.14528, "end": **********.555081, "duration": 1.4098010063171387, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1754125761.14528, "relative_start": 0, "end": **********.414969, "relative_end": **********.414969, "duration": 1.2696890830993652, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.415008, "relative_start": 1.***************, "end": **********.555086, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rBChwyiH59zG6MSTBc2LOvGctI10GnV3PSlwB8D8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-346162904 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-346162904\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1259460692 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1259460692\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-775747413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-775747413\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-615892157 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615892157\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2060066844 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2060066844\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:09:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBCODhSOEprZjZzM3R3REJNK2lSTUE9PSIsInZhbHVlIjoibW1WMDV4OExVcTFiWkRON04wejFWTTBEOXp5eE1KUWM4emcwcEVld2NnNFdjSlB0VWRwNGthQTlvbHpQRVhUZEhzQlcyWWVUZkhGT3JmbS9OTXJDMkJoTEIraUgvVFg2V1ovbFg1SFlMSk11elNsNStLSTlwNWx4L3Q1bFFxdHp3NUhpZTd0Ryt2UVoxTC9uYU0xNmVISENlR0JOUFROcnFrUjU5SDJyZ1J5UG83cUlpZ0tJOXZ0amxrQ0hSWHAxYW9reURoZVFnanZ5UzBGVkg0U3VOZ3drQXc4YXowMVhFdlRCbmxLS1ZxaG9id0o2SWxNSXNiRkUxYitEcE01S0kzZ2xYK1dNMkdrbHhpWWlxVi8wNGVsNTc3dUZnUnhFRG5QWkNIOVdGMk1EMjNNZldlcWpNL2RRSW1yQ3lKa1JpeGpqbkdLcm1mOTVqckNheTFYOUJYQ3V3MTh4MXZMaWNINHd5THBUUEFwZHR2YTZrbkVWeEt0NDljSmFiN0FzaTVMTCtuT0JNSU1RUDNPOUcvSGdSeThaWElVUHRtT1VaaHp5UjdjWldiQ0JMTlNIU1BBRDVwTGhrNi9xWThpRjdTMzRTY0xjaXlwL1QyNmxjM3lKajNJRS9zWUV2Q0JpNEdINHhTZDVGcDhIUmZJcmg4L0VUSlJadmFZL245dnMiLCJtYWMiOiI5NTQ4MTFlYzVkN2UxMGNmOTRkNDI4ZWViYTY4MTFhYjE5ZThhOWU0ZWY3MmJlZjI4YzM5MGVlN2VlZjM5NjA3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:09:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVJUHFYVzVxMnl4d05oRitTeHVXVlE9PSIsInZhbHVlIjoiREhkR2xtUFJOMHdPa2liZStxRU9wOHhCOVFOTS8rcld1U2dBVGRkcXQ4MFJhcW0rN3NJRFZOdkRTeHpuMVYzdmpmTVNtemw4amp5UC80WEduMk1KbEU1TnBSMFIvblJZdGh3VTNZb1VUV0czYWtkb1VlR0ZTVU92RzZ0c0JaNldMdE1meURaeDlHbTR2NzlaTGxlU2syem1yRmJ3RDRxZS9DVEZwdTJycE5mVU9JUXo1K3RSL3ZNR1E0eUpndGF4R2tvYUpxMVVkSmVwanl3VmI1QU5Yc2xSazdwc0lvcnNFOGcvbllCeUJuUHVzV0kybWhTZTJmQ2lwNTdxT3FIakgra2xVaWszeS9GZEhqeUxVRFVHaXgxN1dJTmpWSVFGTnBYN0R2RFhDbURQZThya3ZjTlFEWFlseEdrMThXenBTa2YzazA1QWhTOTJweGlQRlFSWTFZVWNwcEhnWTc1VTJnUkVIL3A3cUJGV2VOdGhDTEJNenM0aEFuQzA0VlA0aitrUUNNK01VeUxZU2p5N2poSlpycDJieXh1dVhIMTdrZnF3VSszUmE2citBR0paeWdiaHNLQ2swSU9LcXdCOERpVStWeTZ0NVllUmNyM1VYa0J4ZVJva2xlUkFnTW1Ca0pCdHBDWHFvaXZONzRBSTcwcDFDYVVCNDFJTHd6NzciLCJtYWMiOiJhNjJiNzA3ZGQ3NjdjYjY0YzBmOTEyODBhZGYzZWY2MGI0YmUwMzY1MDI3NTY0ZDUxNzI5MmViZWRmOTkyODU2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:09:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBCODhSOEprZjZzM3R3REJNK2lSTUE9PSIsInZhbHVlIjoibW1WMDV4OExVcTFiWkRON04wejFWTTBEOXp5eE1KUWM4emcwcEVld2NnNFdjSlB0VWRwNGthQTlvbHpQRVhUZEhzQlcyWWVUZkhGT3JmbS9OTXJDMkJoTEIraUgvVFg2V1ovbFg1SFlMSk11elNsNStLSTlwNWx4L3Q1bFFxdHp3NUhpZTd0Ryt2UVoxTC9uYU0xNmVISENlR0JOUFROcnFrUjU5SDJyZ1J5UG83cUlpZ0tJOXZ0amxrQ0hSWHAxYW9reURoZVFnanZ5UzBGVkg0U3VOZ3drQXc4YXowMVhFdlRCbmxLS1ZxaG9id0o2SWxNSXNiRkUxYitEcE01S0kzZ2xYK1dNMkdrbHhpWWlxVi8wNGVsNTc3dUZnUnhFRG5QWkNIOVdGMk1EMjNNZldlcWpNL2RRSW1yQ3lKa1JpeGpqbkdLcm1mOTVqckNheTFYOUJYQ3V3MTh4MXZMaWNINHd5THBUUEFwZHR2YTZrbkVWeEt0NDljSmFiN0FzaTVMTCtuT0JNSU1RUDNPOUcvSGdSeThaWElVUHRtT1VaaHp5UjdjWldiQ0JMTlNIU1BBRDVwTGhrNi9xWThpRjdTMzRTY0xjaXlwL1QyNmxjM3lKajNJRS9zWUV2Q0JpNEdINHhTZDVGcDhIUmZJcmg4L0VUSlJadmFZL245dnMiLCJtYWMiOiI5NTQ4MTFlYzVkN2UxMGNmOTRkNDI4ZWViYTY4MTFhYjE5ZThhOWU0ZWY3MmJlZjI4YzM5MGVlN2VlZjM5NjA3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:09:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVJUHFYVzVxMnl4d05oRitTeHVXVlE9PSIsInZhbHVlIjoiREhkR2xtUFJOMHdPa2liZStxRU9wOHhCOVFOTS8rcld1U2dBVGRkcXQ4MFJhcW0rN3NJRFZOdkRTeHpuMVYzdmpmTVNtemw4amp5UC80WEduMk1KbEU1TnBSMFIvblJZdGh3VTNZb1VUV0czYWtkb1VlR0ZTVU92RzZ0c0JaNldMdE1meURaeDlHbTR2NzlaTGxlU2syem1yRmJ3RDRxZS9DVEZwdTJycE5mVU9JUXo1K3RSL3ZNR1E0eUpndGF4R2tvYUpxMVVkSmVwanl3VmI1QU5Yc2xSazdwc0lvcnNFOGcvbllCeUJuUHVzV0kybWhTZTJmQ2lwNTdxT3FIakgra2xVaWszeS9GZEhqeUxVRFVHaXgxN1dJTmpWSVFGTnBYN0R2RFhDbURQZThya3ZjTlFEWFlseEdrMThXenBTa2YzazA1QWhTOTJweGlQRlFSWTFZVWNwcEhnWTc1VTJnUkVIL3A3cUJGV2VOdGhDTEJNenM0aEFuQzA0VlA0aitrUUNNK01VeUxZU2p5N2poSlpycDJieXh1dVhIMTdrZnF3VSszUmE2citBR0paeWdiaHNLQ2swSU9LcXdCOERpVStWeTZ0NVllUmNyM1VYa0J4ZVJva2xlUkFnTW1Ca0pCdHBDWHFvaXZONzRBSTcwcDFDYVVCNDFJTHd6NzciLCJtYWMiOiJhNjJiNzA3ZGQ3NjdjYjY0YzBmOTEyODBhZGYzZWY2MGI0YmUwMzY1MDI3NTY0ZDUxNzI5MmViZWRmOTkyODU2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:09:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1308601124 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rBChwyiH59zG6MSTBc2LOvGctI10GnV3PSlwB8D8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308601124\", {\"maxDepth\":0})</script>\n"}}