{"__meta": {"id": "X10b982d31da3cee2ccf8b288c0ee5895", "datetime": "2025-08-02 10:52:57", "utime": **********.805212, "method": "GET", "uri": "/api/leads/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754131976.629619, "end": **********.805246, "duration": 1.1756272315979004, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1754131976.629619, "relative_start": 0, "end": **********.634267, "relative_end": **********.634267, "duration": 1.004648208618164, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.634281, "relative_start": 1.004662036895752, "end": **********.805248, "relative_end": 1.9073486328125e-06, "duration": 0.17096710205078125, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46360288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=658\" onclick=\"\">app/Http/Controllers/ContactController.php:658-697</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02086, "accumulated_duration_str": "20.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.697607, "duration": 0.01576, "duration_str": "15.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 75.551}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.745287, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 75.551, "width_percent": 4.41}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["11", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 664}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.753345, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:664", "source": "app/Http/Controllers/ContactController.php:664", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=664", "ajax": false, "filename": "ContactController.php", "line": "664"}, "connection": "radhe_same", "start_percent": 79.962, "width_percent": 4.458}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (88)", "type": "query", "params": [], "bindings": ["88"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 664}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.76964, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:664", "source": "app/Http/Controllers/ContactController.php:664", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=664", "ajax": false, "filename": "ContactController.php", "line": "664"}, "connection": "radhe_same", "start_percent": 84.42, "width_percent": 3.452}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (23)", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 664}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.773893, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:664", "source": "app/Http/Controllers/ContactController.php:664", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=664", "ajax": false, "filename": "ContactController.php", "line": "664"}, "connection": "radhe_same", "start_percent": 87.872, "width_percent": 3.979}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 164}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 680}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7826579, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Lead.php:164", "source": "app/Models/Lead.php:164", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=164", "ajax": false, "filename": "Lead.php", "line": "164"}, "connection": "radhe_same", "start_percent": 91.85, "width_percent": 4.362}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 684}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7878568, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 96.213, "width_percent": 3.787}]}, "models": {"data": {"App\\Models\\Tag": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 14, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts/export\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/api/leads/11", "status_code": "<pre class=sf-dump id=sf-dump-198629855 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-198629855\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1455060946 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1455060946\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-668853941 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-668853941\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2081013352 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlI2MlA5NkNnR3QzMFV3eFlBbVNUNUE9PSIsInZhbHVlIjoiZFAyWFQ4TzdhejdQVWJYTURQUFJVdkFrdjdqM2JJKzNteTZSdW03NC9ucWRzNHhPeDVPYlZkaUtTbHVVYXFCZ2hyaHJxWk5ybGdFaTdidmlwZG1mYUpWclM3RFV0N1cvODN2R01FejdEZ2crd1ZIY0dBRXlVcFlHdG1tTElYWS9RQ2NpSnkxRXBaeXhMUHZCQ0J3V3BoMlM3YVZNREpkNGtLeHNBc1hQWm9MamUzMzREbTZzblJKS2IwYldtSkUyc1hmWmNDRmp4cE9Jd2pJY1Y4M1h4b1RmTDlNbUxyUERoSmh1STNRd2hCZ3BOUjF3SEgrMmYrODdpbzFpbG9MRTVJbW00RmlYT3ByZG5VUENrOWlNTmd1U3BjN2RnNWZYRit3TWNCRXBKaGJYemhTQ2xaRXBFWDVMWDUyUzNHam9rSzlWTHhrdmJJU3lLTW9GQWJ6aXQ4NzUxcDJvMTNLT0xTRkloRmx3MVM2SVVaTE9DYzlBS1JOY1g5T0Z4a09hc09uRTBZampJMkdDZ21GSGlCUVkyKzE4d3d1ZVNTbHU4dTBOenI4YkI4aW1xd20rNzlyVVBEZjJWZ3g2Y2U2dDhVb3JrMTJjdDRFVzh2ZHRoNVRZb04rQ1ZheW0zOHp5U04reGJaQi9RZ0VMSXpNYktQbU4xU296emlULzNVTTEiLCJtYWMiOiIwNjllMWEwZTU2Mzk3MjY0ZGM2NjdjNjJhMWYyZjkzZmQ4ZDczY2FiNDMyYzY0ZTE5Y2IwMWRmYzY4OWIwOGJlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IklmVmxBdXRqdnY3Q1MzZlB3aFJuWFE9PSIsInZhbHVlIjoiVWtDS20yeFJtaDJmTS9UcWZWOEVRNGZsb2NYNEFkSnZIOWZkZVJSQU82clIrT1NLLzRTU1lQVlJIWkxKUEtRanBUNldham9UUHcrTUpmZXk3Umx5bDZ2QllDaldibmxVUTc0UmhVN3VKMXhOcytwVTMxUE8rZ09uSUo4c1QyS1BHUnFqcjBqRTNwNytQQkRLZHp2eVY4dFRZbkJ5a2pxdHlWWlA3MnNHZ2xHZ0lwMFM2SGFEeEsvS3J3NkJWdUQzc0EvaXVHTzgxUG5HM3p0U3RiUW9mS2FhQTdzRVNCbm1XY3luYmpTQjc3NzlTbzJpZXE2bUl5aWVKNE44Z3NPQnFpVnIyU0ZMalFHSmluWmFSM241UHZEOTRSbC85aEoxSFVuNFZhSGYvZnZjNnJlRFJxdVAxdjBBRHZ2UEgxZG14K2pyckplakJTM29KSmFIWG1PRXBHRTdjRWhCYW5idVJLQk9yY1U2WHY1eVJVY25qZmNaaFhpK3BBaTNBYy9yNFc1RFFxQTdTd1NPb2R4OFZMdmFzY1F5T2laekRQdUUrdFFhN0pMUXdBUVY2aVpwZVdycG1RUVZMcGo3ZldOZmlyU0JKTncxaWpQUzJXbjc5Smo1Z1VsODJkdFBNUTZYMVdXSDJvS2RXYTFIT2p3cTNTSWFZVzRwYlpUNkxLSm0iLCJtYWMiOiJhNTNmNTQ0ZTY4NzA5MmY2NWEyZmM2ZWUxZGMzZGNlMWJmZDUwNzk3MWM4YWI5NDI0MzQ5YjVmNGQ2ZjUxNmJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081013352\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-445503755 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445503755\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1175164796 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:52:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdLVk5nZ3NnaWZweTFrWHBqNytYOHc9PSIsInZhbHVlIjoiRnFQYUFaUFZGeGcyL2hGKzhmYnoyNW15R1pOSkdhM0Z5L3g5NVpzK0pJQWcrcXRhbXFrMi9UcUdZUk9Rb2s1MGtBYlB2aEpXQUxlWHY5c1VydnEzWE4zWFBFMEdSL2FLMDBJVDd5TVpDRWQ0VG1CSG9DQ1hBeHk0YUhDMklnYVB6NGN1bU9VNloxMExIa1pCOTgrM0V6TXdpdUsxOHVMVlM0ZmtCWXV5U0p5SEp2QlhhaDZJNGZjSEhGNGZqWm03ckJHeWJ2T1IxV0hyMUlKV09Kc0NPN0I5akdCUXU0UEtWbkp5Vk11THh6UXlCZFpzUVlIRldpaFlzTDk1aUU0ekxDUUhuTjkyc1NNUHAzV2U3bVRzUENTeldXdHRUdlJ3RTl4TzlyZXJqaWJ1TkdrNVhoRFFnRVBkS0FvckdTeEpHejNKYmlFaDB4QjhzZi9NNk1sSWZDcTVaTU1CN3hFbTYvL2VDVVBTdy9RNHdqNFBRL2JRcTlEd3ZJVDZqYWFlMHZqOFU2WFJIaFVvYmhjMnNJdUpYdjk3eStVTHhHbjN2eXBHYlpaazdxT0dOdVE3aUdWbnRDR1NBRGtIYW5YWXJwWXBtNytSU1VoSnR5bi8vczl6YlVXeStienFha3V6ditFYkJlMEVhTjVhY0o2VXFWS3ZXVzBPQkFtTldQbnkiLCJtYWMiOiI2ZTE0ZWFkNzNmOGNhMThkMDRkMWI1MzE3OThmZGQ0ZGI0NzUzN2RmMzU0MGY2NDQzZGM2YTBkMThmNmI3ZWRmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:52:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImpNRjZWNW1sYUhGWjNHVEZqQ0lwcnc9PSIsInZhbHVlIjoiS3JCWjEvWU1LbCs3VTkvVnNOeHpjeEtxeFVaa1Q2WmRSdWpRbWMwL2wvMEFQTEszaUJZK2M3czBHcWN3Wng3MzFTWTcrNHNNSERGNXdoSVh1T2NrSXcyU0JDeEhxVDI0NWNIWThQOUJIeks3d0h4UzVMTnR1dnNEYzFyck80RFhEbldFc3RvS3VvNldsTVhFNUo2VWl2Qk1iVlZuekxaY2RXUmtlQWtFc0RtMHNDeExjNmlZUEx0NittaThjWFVhT2k2RmFiS0hXMFArKzZsYjRjRTBHeXN1Y21jVVh2TmVqRWlsbk1TQUpOeVBuR2hEY0NaQTJMbDU5dFJ4OHZxN0M3SG1NTVA4OEpzanZqcWFpb0xuN3BVb242OUpWcnBOa1I0S29rb2dDblM5bVJPQktBZmxlL0VTTld4bEtKbEFra1ZiUFF5cG91TWhDY05vQkxwYmp2alRMUXhLVGJLMzRKL0hFdkY4aWI2Y1M4QjBTeVFXSjFJUTd3TnJISTJLcWZwOHRmalhwM0xOMFM4Q2lWb1VhZlM4RHh4QmV1MkJDMEJKd2thTmFZbHhVTkI4a2FoSXZQankzTFpNSmFWdi9kSVdNblk0U3B1b3M3VUVtSzJtWGxNZ3VaSlQ4OEcySFBSbklabVdoRUFDS29zanVLUkV2VXQ4eUZwakN5c1AiLCJtYWMiOiJiYzUyZDBiN2ZmYzliOTY4ZTc2YmE2NGMxOTc5NjNkZTJiNTUxOGU4NGU3Y2IwNGRhM2I2ZDk5YWRmNTg4ZjUwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:52:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdLVk5nZ3NnaWZweTFrWHBqNytYOHc9PSIsInZhbHVlIjoiRnFQYUFaUFZGeGcyL2hGKzhmYnoyNW15R1pOSkdhM0Z5L3g5NVpzK0pJQWcrcXRhbXFrMi9UcUdZUk9Rb2s1MGtBYlB2aEpXQUxlWHY5c1VydnEzWE4zWFBFMEdSL2FLMDBJVDd5TVpDRWQ0VG1CSG9DQ1hBeHk0YUhDMklnYVB6NGN1bU9VNloxMExIa1pCOTgrM0V6TXdpdUsxOHVMVlM0ZmtCWXV5U0p5SEp2QlhhaDZJNGZjSEhGNGZqWm03ckJHeWJ2T1IxV0hyMUlKV09Kc0NPN0I5akdCUXU0UEtWbkp5Vk11THh6UXlCZFpzUVlIRldpaFlzTDk1aUU0ekxDUUhuTjkyc1NNUHAzV2U3bVRzUENTeldXdHRUdlJ3RTl4TzlyZXJqaWJ1TkdrNVhoRFFnRVBkS0FvckdTeEpHejNKYmlFaDB4QjhzZi9NNk1sSWZDcTVaTU1CN3hFbTYvL2VDVVBTdy9RNHdqNFBRL2JRcTlEd3ZJVDZqYWFlMHZqOFU2WFJIaFVvYmhjMnNJdUpYdjk3eStVTHhHbjN2eXBHYlpaazdxT0dOdVE3aUdWbnRDR1NBRGtIYW5YWXJwWXBtNytSU1VoSnR5bi8vczl6YlVXeStienFha3V6ditFYkJlMEVhTjVhY0o2VXFWS3ZXVzBPQkFtTldQbnkiLCJtYWMiOiI2ZTE0ZWFkNzNmOGNhMThkMDRkMWI1MzE3OThmZGQ0ZGI0NzUzN2RmMzU0MGY2NDQzZGM2YTBkMThmNmI3ZWRmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:52:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImpNRjZWNW1sYUhGWjNHVEZqQ0lwcnc9PSIsInZhbHVlIjoiS3JCWjEvWU1LbCs3VTkvVnNOeHpjeEtxeFVaa1Q2WmRSdWpRbWMwL2wvMEFQTEszaUJZK2M3czBHcWN3Wng3MzFTWTcrNHNNSERGNXdoSVh1T2NrSXcyU0JDeEhxVDI0NWNIWThQOUJIeks3d0h4UzVMTnR1dnNEYzFyck80RFhEbldFc3RvS3VvNldsTVhFNUo2VWl2Qk1iVlZuekxaY2RXUmtlQWtFc0RtMHNDeExjNmlZUEx0NittaThjWFVhT2k2RmFiS0hXMFArKzZsYjRjRTBHeXN1Y21jVVh2TmVqRWlsbk1TQUpOeVBuR2hEY0NaQTJMbDU5dFJ4OHZxN0M3SG1NTVA4OEpzanZqcWFpb0xuN3BVb242OUpWcnBOa1I0S29rb2dDblM5bVJPQktBZmxlL0VTTld4bEtKbEFra1ZiUFF5cG91TWhDY05vQkxwYmp2alRMUXhLVGJLMzRKL0hFdkY4aWI2Y1M4QjBTeVFXSjFJUTd3TnJISTJLcWZwOHRmalhwM0xOMFM4Q2lWb1VhZlM4RHh4QmV1MkJDMEJKd2thTmFZbHhVTkI4a2FoSXZQankzTFpNSmFWdi9kSVdNblk0U3B1b3M3VUVtSzJtWGxNZ3VaSlQ4OEcySFBSbklabVdoRUFDS29zanVLUkV2VXQ4eUZwakN5c1AiLCJtYWMiOiJiYzUyZDBiN2ZmYzliOTY4ZTc2YmE2NGMxOTc5NjNkZTJiNTUxOGU4NGU3Y2IwNGRhM2I2ZDk5YWRmNTg4ZjUwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:52:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175164796\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-792915019 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/contacts/export</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792915019\", {\"maxDepth\":0})</script>\n"}}