{"__meta": {"id": "X9f39657ee5db07b72d827de02dcea41d", "datetime": "2025-08-02 10:28:04", "utime": **********.677847, "method": "GET", "uri": "/api/leads/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130483.151637, "end": **********.677881, "duration": 1.5262439250946045, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1754130483.151637, "relative_start": 0, "end": **********.521819, "relative_end": **********.521819, "duration": 1.3701820373535156, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.521841, "relative_start": 1.370203971862793, "end": **********.677885, "relative_end": 4.0531158447265625e-06, "duration": 0.15604400634765625, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46362632, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=660\" onclick=\"\">app/Http/Controllers/ContactController.php:660-699</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00881, "accumulated_duration_str": "8.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.592513, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 50.851}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6156201, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 50.851, "width_percent": 11.805}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["12", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 666}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.622297, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:666", "source": "app/Http/Controllers/ContactController.php:666", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=666", "ajax": false, "filename": "ContactController.php", "line": "666"}, "connection": "radhe_same", "start_percent": 62.656, "width_percent": 8.4}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (86)", "type": "query", "params": [], "bindings": ["86"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 666}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.636934, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:666", "source": "app/Http/Controllers/ContactController.php:666", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=666", "ajax": false, "filename": "ContactController.php", "line": "666"}, "connection": "radhe_same", "start_percent": 71.056, "width_percent": 7.037}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (23)", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 666}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6434588, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:666", "source": "app/Http/Controllers/ContactController.php:666", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=666", "ajax": false, "filename": "ContactController.php", "line": "666"}, "connection": "radhe_same", "start_percent": 78.093, "width_percent": 6.356}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '89', '92', '88', '93', '91', '90')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "89", "92", "88", "93", "91", "90"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 164}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 682}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.65393, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Lead.php:164", "source": "app/Models/Lead.php:164", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=164", "ajax": false, "filename": "Lead.php", "line": "164"}, "connection": "radhe_same", "start_percent": 84.449, "width_percent": 7.832}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '89', '92', '88', '93', '91', '90')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "89", "92", "88", "93", "91", "90"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 686}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.660653, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 92.281, "width_percent": 7.719}]}, "models": {"data": {"App\\Models\\Tag": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 16, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/api/leads/12", "status_code": "<pre class=sf-dump id=sf-dump-1914534927 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1914534927\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1447210368 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1447210368\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-991130552 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-991130552\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-27977045 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkNUNXdReERvNE4xSUZQdTg5b0g0alE9PSIsInZhbHVlIjoiWjVPdlAzVDd1OEZtbDBhQi9aZHN1ejU5SVFPdnJ1bW8zYWJTUUdITGZNRnA5NFlKbk0wTmlDaTk1UzF2RTBaajdBOFZnczZGbWhHTnJTV1pYQ2taQWExNGNHRkZFd1ljVlB6ejhzbzdZbVQxdTBWYmhVVjhzL3FBbkFNdmR4NmJDMDVNV1ErTk1DdGt2bDVHUlYrS0FSeEQ2Y01YZlYyTGRySnk3cVc1SThweFJyR1Y0NlkyVFZOSVdKRm56L1EzV1hWSVZLMXZzamIwYklZKzY3aUFrcmhORzYzdEpxb2VtWnJZWGNka29JNjVDQTFqVlFBVnZ3OTNUTVFBU2xoRzUzK1gwYmhBUUVFMGVsYy9aRUxQOVNuSTFIeW81a0tFdzNXRzg0d2dnbFU3d0cxYkN0VUk4MWdDTXA5Z2lWZERZdDVQSlE1OGI0ZVFGOXl0UVBsd3o5MTdHRmloNjk0dUtEMFp6aDMwZE9OZjZGZW5nTDVxNlFZbGI2bjhOUmlTUmJ1MFJHUnNPNFgzM3grYlVyYUhudlBQNkxxSmFqaVhrWmtnaWhmMHE2bUkrc3R6UWNPU1cwQTZUSjdNVE4wcUs0MWRCTzMyVEswVHFSNXM2N051QVo5WDh6L013eFAvQjdjejJLMFk3WVRvd0R3Sjc0SlFZYkpjU0txZmkyQk4iLCJtYWMiOiI2M2U3N2NjM2JmOTVjZTEwOTg4ZWJhYWNjMmVlMmRiOGNiZjdjY2FkMDNmY2Y1OWRlNWMxMmYxNzE0ZmRkODYwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImNEQU9PYmo1K21NakRDcC9uTE94M1E9PSIsInZhbHVlIjoiNExYT21NUnRoWHJkbktqcWc1SlhuZ1htbzg1WjdMNDR5T1cyYWRxQXpPSjB2TnQrajRQTkgrbHR4K1pMck1zcFpUWXNSVUdyQ2lJWHlFOWxXWWU2ZytkTkpNMHZpQ2doN1JqSnFKcEpNS1RyOXJFRmtONldieE5qOHNHUFk3bGFiWU1nZW92dFowRTdqTHcyMldpay9JOEFQZFB4ek5iT0IvbE02cHk4akJ6TzRYSlp3b2k1VHRiQ1o2bjJCZVlsYzJtT0ljOUpKbjljREZDVzQ0bm5FUFpPRzk1ckN4K0ZHeWYzMlJNWXlacWluVGk5L1RWdjV1ZWNCbUEyMEVvSWhtN0t5eDI4cUJlMzVWSGhVcTdqY0o0OWVwSFR3RXM5VUhDYytJbStqU0tDcEN4TWFsOEk4UEpXUHpQL3pOQy9ZTzlXWWV6QWpNckZHWlJwQ2NOOEFHNlA4bVZoY1IwbmFFdkVVdnBDeVRCVWFrVno1Z3pvV3RVaWJ1a1hEb3k5c1A2Q0NoOTVjWWZVL3JHajBrZFlYMThWZjFSaDlFTXFGM0FIUTk5SEE2eE1ZajBVbTlpUUg5OE1FY2taQk9mZnZKV013OU5rNjFsUFNqRzFqT2xvTkRxWElmdndFeUhhYWcyWlV2ZzRGTGFCMm9hcmcxNzdQQWg1RDRRYXZ5cUwiLCJtYWMiOiJhZDYyZjEwZGJiNGE5NDE1NWVhM2MyZTM5Y2ZhYzZmN2VmYjMxN2M2OWY3Y2M0NjU1Yzc5ZDcwOTg5YjNjYjc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27977045\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1992312425 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992312425\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1156218080 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:28:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9INkNsUVVxU3NOaDdzOFVFbmxPcUE9PSIsInZhbHVlIjoiVzk3eGRhS2Z1c1laSzNyKzl3K3V6aWF2UGxRa0QyT2tocEY1Tk1JN0FmOElUd0I2WUtRWWZJemY1dTFaczBnYm5tS1NzWHRhdzZuR3dDcmVKbkxialB0NklZaTFka3liNnpYVHJnakZZamlWMVBQWXBwSjU3Q2FNOTg4Z2hhSVZza3RDbnJyVnBvQU5iS09CMGtQcWxNaldTWWp3SG9hU1VsMXkwS1JCQjlBaGc4SWlXcnh2WFBLbStmMTcrU0NsSkw2b2x2NXRQdU1ibmdpWTNudUFyT0RNdmUxdHR0ZFJyNWluVmtackQ1RWUydGRSV2doMUlCSkNXdS9xUVN6clMwck1lU0N1R2NFNnlLeWx4VkUwUHRObDExa2Y5TjhUQUVOWlA1UWg1SVQ5OHFLalgvazVHNDNTR1RwK3pWeS92aHhVcTh5WmliNThDc0NYanVVNkhqczRPTWVZbStYS1E5ZnpJSFlqZzNTVGNKYTVXVlA0blpvVjFuQ3ZrWmVLQ1ZXUEV6U0YreEcvS3R1ZzhaM1NRa1BvSHVndWpFdVNBaXZlanBmUFU1S29aNWFobzM4RnV6blVDVGl0eEdwWERhTVBHcjA3RmxSb3pGWmF4Qm1xOUJmTUNsdTJRdDRFYldkZzFNaXhiM2ZwTVFjT2VZYWhuWjJScHRKZlZJcTAiLCJtYWMiOiJhOGI1ODAyODNjY2E4N2M5ZmQ4OGRkNTE0NGE0YjAyOGYwMWU3NDU2MzMxMmVjYmJkOWM1MTliMTI2NmM3NjFjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:28:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjR5R25PMlRkMXRPcHYrVWdoaktyaEE9PSIsInZhbHVlIjoiSWhNeUIrN0RnMnFTWXRtUUpnZ2dBaGttdmkxOUdtQWVOcTZpcWVLT3AvTnBZWTBzWkxpUDJpYm9JZTNLUWhscjVVYytiZTVmU2FsUDZCczNVdG9oK3p6WUp4RWdQY2ZDc2s4a09GL1FGbFlLNnc0dlpHTnpFU2JzZTRDekV3QzIzNHJ4M2s3ZDRsYjhFYXl0eHBnWlp5R1VqZ29LL3djWXVobExNL3lQVXBLTWtJWExjRWNJVXBDbDZqV1hOSGF2ZDI2bXE2eHBVamJiQW9SdXRWaXJlSC9JTmJhenA4blluenJRWU9nTjhDU2lsWE5RQUVrYXA3ZTZYMDNWRlQyTEIzVStBZVRUcEJld0hOM2xicGMwdjQ4OCtzdWJqUmRaSktsdFdTTjZyQWtlWWpNTEhWMUhMYnZKTXhwNWM1bzlqY3hyQnliMTVtTzBvRzJsSGtJVWtvdE1VcVN2UnRJWUlBUWtJVUt6UCtQdGRxd2UwcWFXbW1LR3NGMVRvc1ovaU1Lb1o2UW5zbVFvU3IxdkxPSUE4WncwTG1oamg5aEpRNzRpd29PUktyaGJVbGt1RFZEajJxZlViQ28yNVB3ZVNoYWdRUm5DY3NPZVRzVkc3YVlIb1VxbDBXOGZwa2hTd1RkcGZod1I4b0FSQTJacHdJZ3BzdEYxRnc0ZG95NzgiLCJtYWMiOiI1OGQ0ODZmMzhkODU3YjI1M2FmZDUyNzFjZDA4NzE3M2FlMzg1ZjgyNzY3MTcwMzFjZDhjOGY4NzYxNDZlYjZlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:28:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9INkNsUVVxU3NOaDdzOFVFbmxPcUE9PSIsInZhbHVlIjoiVzk3eGRhS2Z1c1laSzNyKzl3K3V6aWF2UGxRa0QyT2tocEY1Tk1JN0FmOElUd0I2WUtRWWZJemY1dTFaczBnYm5tS1NzWHRhdzZuR3dDcmVKbkxialB0NklZaTFka3liNnpYVHJnakZZamlWMVBQWXBwSjU3Q2FNOTg4Z2hhSVZza3RDbnJyVnBvQU5iS09CMGtQcWxNaldTWWp3SG9hU1VsMXkwS1JCQjlBaGc4SWlXcnh2WFBLbStmMTcrU0NsSkw2b2x2NXRQdU1ibmdpWTNudUFyT0RNdmUxdHR0ZFJyNWluVmtackQ1RWUydGRSV2doMUlCSkNXdS9xUVN6clMwck1lU0N1R2NFNnlLeWx4VkUwUHRObDExa2Y5TjhUQUVOWlA1UWg1SVQ5OHFLalgvazVHNDNTR1RwK3pWeS92aHhVcTh5WmliNThDc0NYanVVNkhqczRPTWVZbStYS1E5ZnpJSFlqZzNTVGNKYTVXVlA0blpvVjFuQ3ZrWmVLQ1ZXUEV6U0YreEcvS3R1ZzhaM1NRa1BvSHVndWpFdVNBaXZlanBmUFU1S29aNWFobzM4RnV6blVDVGl0eEdwWERhTVBHcjA3RmxSb3pGWmF4Qm1xOUJmTUNsdTJRdDRFYldkZzFNaXhiM2ZwTVFjT2VZYWhuWjJScHRKZlZJcTAiLCJtYWMiOiJhOGI1ODAyODNjY2E4N2M5ZmQ4OGRkNTE0NGE0YjAyOGYwMWU3NDU2MzMxMmVjYmJkOWM1MTliMTI2NmM3NjFjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:28:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjR5R25PMlRkMXRPcHYrVWdoaktyaEE9PSIsInZhbHVlIjoiSWhNeUIrN0RnMnFTWXRtUUpnZ2dBaGttdmkxOUdtQWVOcTZpcWVLT3AvTnBZWTBzWkxpUDJpYm9JZTNLUWhscjVVYytiZTVmU2FsUDZCczNVdG9oK3p6WUp4RWdQY2ZDc2s4a09GL1FGbFlLNnc0dlpHTnpFU2JzZTRDekV3QzIzNHJ4M2s3ZDRsYjhFYXl0eHBnWlp5R1VqZ29LL3djWXVobExNL3lQVXBLTWtJWExjRWNJVXBDbDZqV1hOSGF2ZDI2bXE2eHBVamJiQW9SdXRWaXJlSC9JTmJhenA4blluenJRWU9nTjhDU2lsWE5RQUVrYXA3ZTZYMDNWRlQyTEIzVStBZVRUcEJld0hOM2xicGMwdjQ4OCtzdWJqUmRaSktsdFdTTjZyQWtlWWpNTEhWMUhMYnZKTXhwNWM1bzlqY3hyQnliMTVtTzBvRzJsSGtJVWtvdE1VcVN2UnRJWUlBUWtJVUt6UCtQdGRxd2UwcWFXbW1LR3NGMVRvc1ovaU1Lb1o2UW5zbVFvU3IxdkxPSUE4WncwTG1oamg5aEpRNzRpd29PUktyaGJVbGt1RFZEajJxZlViQ28yNVB3ZVNoYWdRUm5DY3NPZVRzVkc3YVlIb1VxbDBXOGZwa2hTd1RkcGZod1I4b0FSQTJacHdJZ3BzdEYxRnc0ZG95NzgiLCJtYWMiOiI1OGQ0ODZmMzhkODU3YjI1M2FmZDUyNzFjZDA4NzE3M2FlMzg1ZjgyNzY3MTcwMzFjZDhjOGY4NzYxNDZlYjZlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:28:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156218080\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1964162479 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964162479\", {\"maxDepth\":0})</script>\n"}}