[2025-08-02 07:56:24] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T07:56:24.157404Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":14,"status":"dispatching"} 
[2025-08-02 07:56:29] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T07:56:29.558671Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":5305.0,"user_id":79,"entity_id":14,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T07:56:24.263860Z","data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"+918965878956","subject":"Lead from Jatndea Nath Singha","user_id":"81","pipeline_id":23,"stage_id":89,"created_by":79,"date":"2025-08-02","date_of_birth":"2025-08-15","type":null,"status":"warm","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"updated_at":"2025-08-02T07:56:24.000000Z","created_at":"2025-08-02T07:56:24.000000Z","id":14,"stage":{"id":89,"name":"Negotiation","pipeline_id":23,"created_by":79,"order":3,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 07:56:29] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T07:56:29.560513Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 08:10:05] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-08-02T08:10:05.361629Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"entity_type":"Lead","entity_id":11,"status":"dispatching"} 
[2025-08-02 08:10:07] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T08:10:07.485751Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2111.0,"user_id":79,"entity_id":11,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-08-02T08:10:05.380836Z","data":{"id":11,"name":"Parichay Singha","contact_type":"Lead","tags":[{"id":88,"name":"Hot Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+916896589568","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"subject":"Booking","user_id":81,"pipeline_id":23,"stage_id":87,"contact_group_id":2,"sources":[],"products":[],"notes":null,"labels":[],"custom_fields":null,"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-07-19","next_follow_up_date":null,"created_at":"2025-07-19T07:04:45.000000Z","updated_at":"2025-08-01T16:46:21.000000Z","stage":{"id":87,"name":"Qualified","pipeline_id":23,"created_by":79,"order":1,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"users":[{"id":79,"name":"Parichay Singha AI","email":"<EMAIL>","email_verified_at":"2025-07-19T06:00:35.000000Z","plan":11,"plan_expire_date":"2026-07-31","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"logo-dark-removebg-preview_1752904903.png","messenger_color":"#2180f3","lang":"en","default_pipeline":23,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-19T06:00:35.000000Z","updated_at":"2025-07-31T06:32:06.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":11,"user_id":79}},{"id":81,"name":"Gungun Rani","email":"<EMAIL>","email_verified_at":"2025-07-19T07:03:24.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":79,"created_at":"2025-07-19T07:03:24.000000Z","updated_at":"2025-07-19T07:03:24.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":11,"user_id":81}}],"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"old_stage_id":87,"new_stage_id":"88","triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 08:10:07] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-08-02T08:10:07.491181Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 08:19:54] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T08:19:54.033357Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":15,"status":"dispatching"} 
[2025-08-02 08:19:56] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T08:19:56.142828Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2100.0,"user_id":79,"entity_id":15,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T08:19:54.047789Z","data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"+918987895025","subject":"Lead from Jatndea Nath Singha","user_id":"81","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":"2025-07-30","type":"lead","status":"warm","opportunity_info":null,"opportunity_description":"ok","opportunity_source":"referral","lead_value":null,"next_follow_up_date":"2025-08-17","contact_type":"Lead","postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"tags":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"updated_at":"2025-08-02T08:19:53.000000Z","created_at":"2025-08-02T08:19:53.000000Z","id":15,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 08:19:56] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T08:19:56.144312Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 08:20:41] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-08-02T08:20:41.166023Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"entity_type":"Lead","entity_id":15,"status":"dispatching"} 
[2025-08-02 08:20:43] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T08:20:43.258925Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2077.0,"user_id":79,"entity_id":15,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-08-02T08:20:41.188202Z","data":{"id":15,"name":"Jatndea Nath Singha","contact_type":"Lead","tags":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+918987895025","date_of_birth":"2025-07-30","type":"","status":"","opportunity_info":null,"opportunity_description":"ok","opportunity_source":"referral","lead_value":null,"subject":"Lead from Jatndea Nath Singha","user_id":81,"pipeline_id":23,"stage_id":86,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"custom_fields":null,"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-08-02","next_follow_up_date":"2025-08-17","created_at":"2025-08-02T08:19:53.000000Z","updated_at":"2025-08-02T08:19:53.000000Z","stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"users":[{"id":79,"name":"Parichay Singha AI","email":"<EMAIL>","email_verified_at":"2025-07-19T06:00:35.000000Z","plan":11,"plan_expire_date":"2026-07-31","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"logo-dark-removebg-preview_1752904903.png","messenger_color":"#2180f3","lang":"en","default_pipeline":23,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-19T06:00:35.000000Z","updated_at":"2025-07-31T06:32:06.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":15,"user_id":79}},{"id":81,"name":"Gungun Rani","email":"<EMAIL>","email_verified_at":"2025-07-19T07:03:24.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":79,"created_at":"2025-07-19T07:03:24.000000Z","updated_at":"2025-07-19T07:03:24.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":15,"user_id":81}}],"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"old_stage_id":86,"new_stage_id":"87","triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 08:20:43] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-08-02T08:20:43.262481Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 08:24:12] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T08:24:12.238241Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":16,"status":"dispatching"} 
[2025-08-02 08:24:14] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2043 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T08:24:14.358619Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2114.0,"user_id":79,"entity_id":16,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T08:24:12.247809Z","data":{"name":"Raja Kumar Singh","email":"<EMAIL>","phone":"+916532658905","subject":"Lead from Raja Kumar Singh","user_id":"81","pipeline_id":23,"stage_id":87,"created_by":79,"date":"2025-08-02","date_of_birth":"2025-07-30","type":"lead","status":"hot","opportunity_info":null,"opportunity_description":null,"opportunity_source":"website","lead_value":"8900","next_follow_up_date":"2025-08-15","contact_type":"Lead","postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"tags":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"updated_at":"2025-08-02T08:24:12.000000Z","created_at":"2025-08-02T08:24:12.000000Z","id":16,"stage":{"id":87,"name":"Qualified","pipeline_id":23,"created_by":79,"order":1,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2043 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 08:24:14] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T08:24:14.359372Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2043 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 08:39:44] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T08:39:44.379782Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":17,"status":"dispatching"} 
[2025-08-02 08:39:46] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T08:39:46.608982Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2200.0,"user_id":79,"entity_id":17,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T08:39:44.424590Z","data":{"id":17,"name":"Mr Mukharji","contact_type":"Lead","tags":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+918975620126","date_of_birth":"2025-07-30","type":"","status":"","opportunity_info":null,"opportunity_description":"ok","opportunity_source":"phone","lead_value":"8900.00","subject":"Lead from Mr Mukharji","user_id":81,"pipeline_id":23,"stage_id":87,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"custom_fields":null,"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-08-02","next_follow_up_date":"2025-08-23","created_at":"2025-08-02T08:39:44.000000Z","updated_at":"2025-08-02T08:39:44.000000Z","stage":{"id":87,"name":"Qualified","pipeline_id":23,"created_by":79,"order":1,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 08:39:46] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T08:39:46.611633Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2049 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 08:40:42] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-08-02T08:40:42.375975Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"entity_type":"Lead","entity_id":17,"status":"dispatching"} 
[2025-08-02 08:40:44] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T08:40:44.542333Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2145.0,"user_id":79,"entity_id":17,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-08-02T08:40:42.408029Z","data":{"id":17,"name":"Mr Mukharji","contact_type":"Lead","tags":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+918975620126","date_of_birth":"2025-07-30","type":"","status":"","opportunity_info":null,"opportunity_description":"ok","opportunity_source":"phone","lead_value":"8900.00","subject":"Lead from Mr Mukharji","user_id":81,"pipeline_id":23,"stage_id":87,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"custom_fields":null,"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-08-02","next_follow_up_date":"2025-08-23","created_at":"2025-08-02T08:39:44.000000Z","updated_at":"2025-08-02T08:39:44.000000Z","stage":{"id":87,"name":"Qualified","pipeline_id":23,"created_by":79,"order":1,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"users":[{"id":79,"name":"Parichay Singha AI","email":"<EMAIL>","email_verified_at":"2025-07-19T06:00:35.000000Z","plan":11,"plan_expire_date":"2026-07-31","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"logo-dark-removebg-preview_1752904903.png","messenger_color":"#2180f3","lang":"en","default_pipeline":23,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-19T06:00:35.000000Z","updated_at":"2025-07-31T06:32:06.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":17,"user_id":79}},{"id":81,"name":"Gungun Rani","email":"<EMAIL>","email_verified_at":"2025-07-19T07:03:24.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":79,"created_at":"2025-07-19T07:03:24.000000Z","updated_at":"2025-07-19T07:03:24.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":17,"user_id":81}}],"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"old_stage_id":87,"new_stage_id":"86","triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 08:40:44] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-08-02T08:40:44.550990Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 09:15:05] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T09:15:05.504132Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":18,"status":"dispatching"} 
[2025-08-02 09:15:07] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T09:15:07.648640Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2135.0,"user_id":79,"entity_id":18,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T09:15:05.517015Z","data":{"id":18,"name":"Anurak Mukharji","contact_type":"Lead","tags":[{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":88,"name":"Hot Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":94,"name":"Ok AUI","created_by":79,"is_active":1,"created_at":"2025-08-02T07:52:49.000000Z","updated_at":"2025-08-02T07:52:49.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+918965201235","date_of_birth":"2025-07-30","type":"","status":"","opportunity_info":null,"opportunity_description":"ok","opportunity_source":"social_media","lead_value":null,"subject":"Lead from Anurak Mukharji","user_id":81,"pipeline_id":23,"stage_id":90,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"custom_fields":null,"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-08-02","next_follow_up_date":"2025-08-16","created_at":"2025-08-02T09:15:05.000000Z","updated_at":"2025-08-02T09:15:05.000000Z","stage":{"id":90,"name":"Won/Lost","pipeline_id":23,"created_by":79,"order":4,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 09:15:07] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T09:15:07.649571Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 09:31:52] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T09:31:52.732065Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":19,"status":"dispatching"} 
[2025-08-02 09:31:54] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T09:31:54.831525Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2092.0,"user_id":79,"entity_id":19,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T09:31:52.742958Z","data":{"id":19,"name":"Parichay Singha Parichay Singha","contact_type":"Lead","tags":[],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+918617555736","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"subject":"Lead from Parichay Singha Parichay Singha","user_id":81,"pipeline_id":23,"stage_id":86,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"custom_fields":null,"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-08-02","next_follow_up_date":null,"created_at":"2025-08-02T09:31:52.000000Z","updated_at":"2025-08-02T09:31:52.000000Z","stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 09:31:54] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T09:31:54.832585Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 09:36:19] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T09:36:19.533659Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":20,"status":"dispatching"} 
[2025-08-02 09:36:21] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T09:36:21.666828Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2118.0,"user_id":79,"entity_id":20,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T09:36:19.558380Z","data":{"name":"Rani Kumar","email":"<EMAIL>","phone":"+9168984585980","subject":"Lead from Rani Kumar","user_id":"81","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":null,"type":null,"status":"won","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"updated_at":"2025-08-02T09:36:19.000000Z","created_at":"2025-08-02T09:36:19.000000Z","id":20,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 09:36:21] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T09:36:21.669180Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 09:42:03] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T09:42:03.677245Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":21,"status":"dispatching"} 
[2025-08-02 09:42:05] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T09:42:05.754868Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2068.0,"user_id":79,"entity_id":21,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T09:42:03.690887Z","data":{"name":"Ranu","email":"<EMAIL>","phone":"+915896584789","subject":"Lead from Ranu","user_id":"81","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"updated_at":"2025-08-02T09:42:03.000000Z","created_at":"2025-08-02T09:42:03.000000Z","id":21,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 09:42:05] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T09:42:05.755827Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 09:56:11] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T09:56:11.132676Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":22,"status":"dispatching"} 
[2025-08-02 09:56:13] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T09:56:13.244218Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2101.0,"user_id":79,"entity_id":22,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T09:56:11.146755Z","data":{"name":"Anuradha Sen","email":"<EMAIL>","phone":"+918695201235","subject":"Lead from Anuradha Sen","user_id":"81","pipeline_id":23,"stage_id":89,"created_by":79,"date":"2025-08-02","date_of_birth":null,"type":null,"status":"warm","opportunity_info":null,"opportunity_description":null,"opportunity_source":"social_media","lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"updated_at":"2025-08-02T09:56:11.000000Z","created_at":"2025-08-02T09:56:11.000000Z","id":22,"stage":{"id":89,"name":"Negotiation","pipeline_id":23,"created_by":79,"order":3,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 09:56:13] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T09:56:13.245138Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 10:05:14] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T10:05:14.255353Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":23,"status":"dispatching"} 
[2025-08-02 10:05:16] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T10:05:16.347253Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2085.0,"user_id":79,"entity_id":23,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T10:05:14.265428Z","data":{"id":23,"name":"Anupam Singha","contact_type":"Lead","tags":null,"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"email":"<EMAIL>","phone":"+915897865202","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"subject":"Lead from Anupam Singha","user_id":81,"pipeline_id":23,"stage_id":89,"contact_group_id":null,"sources":[],"products":[],"notes":null,"labels":[],"custom_fields":null,"order":0,"created_by":79,"is_deleted":0,"is_active":1,"is_converted":0,"date":"2025-08-02","next_follow_up_date":null,"created_at":"2025-08-02T10:05:14.000000Z","updated_at":"2025-08-02T10:05:14.000000Z","stage":{"id":89,"name":"Negotiation","pipeline_id":23,"created_by":79,"order":3,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 10:05:16] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T10:05:16.347965Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 10:14:58] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T10:14:58.912197Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":24,"status":"dispatching"} 
[2025-08-02 10:15:01] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T10:15:01.014776Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2093.0,"user_id":79,"entity_id":24,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T10:14:58.925219Z","data":{"name":"Anudua Sen","email":"<EMAIL>","phone":"+918654523569","subject":"Lead from Anudua Sen","user_id":"81","pipeline_id":23,"stage_id":90,"created_by":79,"date":"2025-08-02","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[],"postal_code":null,"city":null,"state":null,"country":null,"business_name":null,"business_gst":null,"business_state":null,"business_postal_code":null,"business_address":null,"dnd_settings":null,"updated_at":"2025-08-02T10:14:58.000000Z","created_at":"2025-08-02T10:14:58.000000Z","id":24,"stage":{"id":90,"name":"Won/Lost","pipeline_id":23,"created_by":79,"order":4,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 10:15:01] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T10:15:01.015508Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 10:22:52] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T10:22:52.870744Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":25,"status":"dispatching"} 
[2025-08-02 10:22:54] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2014 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T10:22:54.973300Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2093.0,"user_id":79,"entity_id":25,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T10:22:52.883646Z","data":{"name":"mistry sen","email":"<EMAIL>","phone":"8956425359","subject":"New Contact","user_id":"79","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":94,"name":"Ok AUI","created_by":79,"is_active":1,"created_at":"2025-08-02T07:52:49.000000Z","updated_at":"2025-08-02T07:52:49.000000Z"},{"id":95,"name":"ok bot","created_by":79,"is_active":1,"created_at":"2025-08-02T08:28:42.000000Z","updated_at":"2025-08-02T08:28:42.000000Z"},{"id":97,"name":"Paaa","created_by":79,"is_active":1,"created_at":"2025-08-02T09:27:58.000000Z","updated_at":"2025-08-02T09:27:58.000000Z"}],"postal_code":"734429","city":"DARJILING","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJ

naxalbari","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","updated_at":"2025-08-02T10:22:52.000000Z","created_at":"2025-08-02T10:22:52.000000Z","id":25,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2014 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 10:22:54] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T10:22:54.976792Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2014 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 10:26:45] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T10:26:45.596514Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":26,"status":"dispatching"} 
[2025-08-02 10:26:47] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T10:26:47.696067Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2089.0,"user_id":79,"entity_id":26,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T10:26:45.609640Z","data":{"name":"mama bar","email":"<EMAIL>","phone":"956256466856","subject":"New Contact","user_id":"79","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":null,"type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[{"id":88,"name":"Hot Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":94,"name":"Ok AUI","created_by":79,"is_active":1,"created_at":"2025-08-02T07:52:49.000000Z","updated_at":"2025-08-02T07:52:49.000000Z"},{"id":95,"name":"ok bot","created_by":79,"is_active":1,"created_at":"2025-08-02T08:28:42.000000Z","updated_at":"2025-08-02T08:28:42.000000Z"},{"id":96,"name":"ok bot ai","created_by":79,"is_active":1,"created_at":"2025-08-02T09:16:35.000000Z","updated_at":"2025-08-02T09:16:35.000000Z"},{"id":97,"name":"Paaa","created_by":79,"is_active":1,"created_at":"2025-08-02T09:27:58.000000Z","updated_at":"2025-08-02T09:27:58.000000Z"},{"id":98,"name":"OK BOT AI DEMON","created_by":79,"is_active":1,"created_at":"2025-08-02T10:06:37.000000Z","updated_at":"2025-08-02T10:06:37.000000Z"}],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","updated_at":"2025-08-02T10:26:45.000000Z","created_at":"2025-08-02T10:26:45.000000Z","id":26,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 10:26:47] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T10:26:47.697028Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 10:29:23] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T10:29:23.711645Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":27,"status":"dispatching"} 
[2025-08-02 10:29:25] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T10:29:25.818848Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2098.0,"user_id":79,"entity_id":27,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T10:29:23.724809Z","data":{"name":"My AI BOT","email":"<EMAIL>","phone":"8965231205","subject":"New Contact","user_id":"79","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":"2025-08-01","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":94,"name":"Ok AUI","created_by":79,"is_active":1,"created_at":"2025-08-02T07:52:49.000000Z","updated_at":"2025-08-02T07:52:49.000000Z"},{"id":95,"name":"ok bot","created_by":79,"is_active":1,"created_at":"2025-08-02T08:28:42.000000Z","updated_at":"2025-08-02T08:28:42.000000Z"},{"id":96,"name":"ok bot ai","created_by":79,"is_active":1,"created_at":"2025-08-02T09:16:35.000000Z","updated_at":"2025-08-02T09:16:35.000000Z"},{"id":97,"name":"Paaa","created_by":79,"is_active":1,"created_at":"2025-08-02T09:27:58.000000Z","updated_at":"2025-08-02T09:27:58.000000Z"},{"id":98,"name":"OK BOT AI DEMON","created_by":79,"is_active":1,"created_at":"2025-08-02T10:06:37.000000Z","updated_at":"2025-08-02T10:06:37.000000Z"}],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734001","business_address":"Siliguri","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","updated_at":"2025-08-02T10:29:23.000000Z","created_at":"2025-08-02T10:29:23.000000Z","id":27,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 10:29:25] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T10:29:25.820045Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 10:31:27] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T10:31:27.631670Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":28,"status":"dispatching"} 
[2025-08-02 10:31:29] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T10:31:29.726808Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2086.0,"user_id":79,"entity_id":28,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T10:31:27.644285Z","data":{"name":"miona dlj","email":"<EMAIL>","phone":"8965421025","subject":"New Contact","user_id":"79","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":"2025-07-30","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","tags":[{"id":88,"name":"Hot Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":94,"name":"Ok AUI","created_by":79,"is_active":1,"created_at":"2025-08-02T07:52:49.000000Z","updated_at":"2025-08-02T07:52:49.000000Z"}],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJ

naxalbari","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","updated_at":"2025-08-02T10:31:27.000000Z","created_at":"2025-08-02T10:31:27.000000Z","id":28,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 10:31:29] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T10:31:29.729627Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-02 10:46:27] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-08-02T10:46:27.438494Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"entity_type":"Lead","entity_id":29,"status":"dispatching"} 
[2025-08-02 10:46:29] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-02T10:46:29.556511Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2108.0,"user_id":79,"entity_id":29,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-08-02T10:46:27.451927Z","data":{"name":"demo my ai","email":"<EMAIL>","phone":"8654215890","subject":"New Contact","user_id":"79","pipeline_id":23,"stage_id":86,"created_by":79,"date":"2025-08-02","date_of_birth":"2025-07-30","type":null,"status":"active","opportunity_info":null,"opportunity_description":null,"opportunity_source":null,"lead_value":null,"next_follow_up_date":null,"contact_type":"Lead","postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJ

naxalbari","dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}","tags":[{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}],"updated_at":"2025-08-02T10:46:27.000000Z","created_at":"2025-08-02T10:46:27.000000Z","id":29,"stage":{"id":86,"name":"New","pipeline_id":23,"created_by":79,"order":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:03.000000Z"},"pipeline":{"id":23,"name":"OMX Digital Bot","created_by":79,"is_deleted":0,"created_at":"2025-07-19T06:02:03.000000Z","updated_at":"2025-07-19T06:02:42.000000Z"},"labels":[],"sources":[],"products":[],"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"}},"user_id":79,"triggered_by":{"user_id":79,"email":"<EMAIL>","name":"Parichay Singha AI","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-02 10:46:29] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-08-02T10:46:29.558332Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":79,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
