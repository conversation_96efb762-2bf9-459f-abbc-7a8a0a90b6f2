{"__meta": {"id": "X1a4b180c0d2961bf531f3cffd504ec2c", "datetime": "2025-08-02 09:15:25", "utime": **********.597792, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754126124.500471, "end": **********.597833, "duration": 1.0973618030548096, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": 1754126124.500471, "relative_start": 0, "end": **********.532979, "relative_end": **********.532979, "duration": 1.0325078964233398, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.532992, "relative_start": 1.****************, "end": **********.597836, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "64.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fqMSWAhQkBiwuhWJMqzGNsMCd6mhvMrU46e2NwoK", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1424539338 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1424539338\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-862229942 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-862229942\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1570690048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1570690048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1430860852 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430860852\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-788500853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-788500853\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1306271269 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:15:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhPSlJzUkNHZ3EyRWZQZ2tKU1o5VFE9PSIsInZhbHVlIjoiWnMvS29TOWxqWXVvOE0yeDlQN0g4c0djd1V3MkV0TXRlcGx1QXdYK0o2amNmYURvMmlENWtvUHp0RS95U2tFK3BIZVVrVXpETHpmSW1ESWNOM1RZZVBKczhFZnRnTnBOTE9hQkhjU2VmZ203WndNVW5RRFZ6SW9zMWxvb0ZxWGhpYVd6RGdIdzlmSXIyYzlwWExzbHh5QTZzSVZlbjNmNkpXRHdDc0NTYVBnWmd4eUF1UzZLVnRYUjNEUXB6SThNMFVjemF3Sm1TUXpRUktpdVpaQ1dpZWFNak5rck01Q1lyV2psMkVDMGd2VGdsbzN4T3k4VDJuZldKQk05TUNQUzI1TmJ2cEFYRzhla3pKZ3c0azFrWnZ2S2lNbld5TEJmclZnZGNQeEZaeksxZ0RKdnNSc0d4ZDhydHVRc2R2Ry85aFJqOENTdEFlQVhSWld3V3hDTzNLTlp1TU1GL3Q2cDNWYUF2Q2Q5YVFqcElvaXBSeVV3VnNIYWN4bGorbFE1WXBidkptZGVSUmxRTDVsVlZGSzRBRm5ja20xZVhoVndQcmdUTGxTNUk2SVY1dUVhTzNuVEhIdkNEUFBYNFJHbysxNGtxbUc3OU5GRXROam9HcmNid2NMWjJZZ0FwYk1oUmxIdDlCcGw5NkFCNVdneWJQeGJSbjZ2Q0grajZkbEIiLCJtYWMiOiI1NDljNDdkMzcxY2EwMjAwMzViODk4MjZjNzU2NWM0YjFhYzc5MWQ1NDhhYTBiNGE3Y2RmNjAyY2FkY2IzZWYxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:15:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImxTZnFORFJ5NkN0SnRuWE54SUR0dEE9PSIsInZhbHVlIjoiUXVXR2l2czZzQWRUZEM2RUNwQVVsTWJGd1MrMUJsS2YxU1g0WEVVYmVRcmphNE9RWWpUYU1la2x3dXg2cWpFNDdDZnVRTXcyYWgzc0pxcGg1ZlBWdmJSTysxSGxYODhiaWMrcTVHbXlkZlVZdm11KzZKZVdwRmpLSHJ6MGVtU0F2RWRleDE5TG5qUnVVUlF2VEFtdTZ5YXpoTjY2TzR6RHY5N1JZVlU0R2xqcElwVDFSVXdCK2tZOGJsMUNMY1Y0T0NNa0gxdFRwRllKMkQ4MjlNYjlCWVZlUGtRNzhTdVJZZjQ1MEUwRC9XZHZadzhpTDM2cmNuZnNaY1RRWnhieEF1RDlNSWN1YTdyek13T2NXczdaYm1HVVZXV1RCVWFEbW1WRGlpazRKTjlpSXp1QTg4S2tFcm85dzFMaElCMWZYaFRmTHorL1pyS2NqN3dDTlRmTnRWU1htcFpDWjFVWHNKY29iR012OS9yVEJrV3FIWFo4TDNTQnBhcERvdGQ3anBqSGJOa01WVGxMK0M1dzFIcE42RENRUzhKOTFQTURjVmVMcW0vNHNWL0ZxQ3RTZHEzSzhqMmU4M3Z5VGs3QngwUk51VEtpdTJVNWczNjB3QWJpeDMzWlJ2Wm8vT0tWWG9Ja240SGphSzdEWGhFUTdvMy95N0tPSEZtZW5ia0EiLCJtYWMiOiIxODQzNGExMTIzYjhiYTc1YzJiYjg0YzVkZGZjNzQzOGZiMzdiMGMzMjFlZTQwZGE4ZWYzN2JlYjhlMWUxODAzIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:15:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhPSlJzUkNHZ3EyRWZQZ2tKU1o5VFE9PSIsInZhbHVlIjoiWnMvS29TOWxqWXVvOE0yeDlQN0g4c0djd1V3MkV0TXRlcGx1QXdYK0o2amNmYURvMmlENWtvUHp0RS95U2tFK3BIZVVrVXpETHpmSW1ESWNOM1RZZVBKczhFZnRnTnBOTE9hQkhjU2VmZ203WndNVW5RRFZ6SW9zMWxvb0ZxWGhpYVd6RGdIdzlmSXIyYzlwWExzbHh5QTZzSVZlbjNmNkpXRHdDc0NTYVBnWmd4eUF1UzZLVnRYUjNEUXB6SThNMFVjemF3Sm1TUXpRUktpdVpaQ1dpZWFNak5rck01Q1lyV2psMkVDMGd2VGdsbzN4T3k4VDJuZldKQk05TUNQUzI1TmJ2cEFYRzhla3pKZ3c0azFrWnZ2S2lNbld5TEJmclZnZGNQeEZaeksxZ0RKdnNSc0d4ZDhydHVRc2R2Ry85aFJqOENTdEFlQVhSWld3V3hDTzNLTlp1TU1GL3Q2cDNWYUF2Q2Q5YVFqcElvaXBSeVV3VnNIYWN4bGorbFE1WXBidkptZGVSUmxRTDVsVlZGSzRBRm5ja20xZVhoVndQcmdUTGxTNUk2SVY1dUVhTzNuVEhIdkNEUFBYNFJHbysxNGtxbUc3OU5GRXROam9HcmNid2NMWjJZZ0FwYk1oUmxIdDlCcGw5NkFCNVdneWJQeGJSbjZ2Q0grajZkbEIiLCJtYWMiOiI1NDljNDdkMzcxY2EwMjAwMzViODk4MjZjNzU2NWM0YjFhYzc5MWQ1NDhhYTBiNGE3Y2RmNjAyY2FkY2IzZWYxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:15:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImxTZnFORFJ5NkN0SnRuWE54SUR0dEE9PSIsInZhbHVlIjoiUXVXR2l2czZzQWRUZEM2RUNwQVVsTWJGd1MrMUJsS2YxU1g0WEVVYmVRcmphNE9RWWpUYU1la2x3dXg2cWpFNDdDZnVRTXcyYWgzc0pxcGg1ZlBWdmJSTysxSGxYODhiaWMrcTVHbXlkZlVZdm11KzZKZVdwRmpLSHJ6MGVtU0F2RWRleDE5TG5qUnVVUlF2VEFtdTZ5YXpoTjY2TzR6RHY5N1JZVlU0R2xqcElwVDFSVXdCK2tZOGJsMUNMY1Y0T0NNa0gxdFRwRllKMkQ4MjlNYjlCWVZlUGtRNzhTdVJZZjQ1MEUwRC9XZHZadzhpTDM2cmNuZnNaY1RRWnhieEF1RDlNSWN1YTdyek13T2NXczdaYm1HVVZXV1RCVWFEbW1WRGlpazRKTjlpSXp1QTg4S2tFcm85dzFMaElCMWZYaFRmTHorL1pyS2NqN3dDTlRmTnRWU1htcFpDWjFVWHNKY29iR012OS9yVEJrV3FIWFo4TDNTQnBhcERvdGQ3anBqSGJOa01WVGxMK0M1dzFIcE42RENRUzhKOTFQTURjVmVMcW0vNHNWL0ZxQ3RTZHEzSzhqMmU4M3Z5VGs3QngwUk51VEtpdTJVNWczNjB3QWJpeDMzWlJ2Wm8vT0tWWG9Ja240SGphSzdEWGhFUTdvMy95N0tPSEZtZW5ia0EiLCJtYWMiOiIxODQzNGExMTIzYjhiYTc1YzJiYjg0YzVkZGZjNzQzOGZiMzdiMGMzMjFlZTQwZGE4ZWYzN2JlYjhlMWUxODAzIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:15:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306271269\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fqMSWAhQkBiwuhWJMqzGNsMCd6mhvMrU46e2NwoK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}