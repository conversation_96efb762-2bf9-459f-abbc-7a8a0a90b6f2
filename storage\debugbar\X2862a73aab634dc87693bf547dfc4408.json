{"__meta": {"id": "X2862a73aab634dc87693bf547dfc4408", "datetime": "2025-08-02 08:24:27", "utime": **********.538702, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754123066.240447, "end": **********.53873, "duration": 1.2982828617095947, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1754123066.240447, "relative_start": 0, "end": **********.461032, "relative_end": **********.461032, "duration": 1.2205848693847656, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.461073, "relative_start": 1.***************, "end": **********.538732, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "77.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tNVac1YYPnUlWCrdPcTX7NVXxp6D8JKJpzQWZGAZ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-468055821 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-468055821\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1848554390 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1848554390\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-229353848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-229353848\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1251069714 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251069714\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1085516762 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1085516762\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1529325538 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:24:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpQZTFLd3R2aTF0Z3RIT1FSZ2t3V3c9PSIsInZhbHVlIjoiNWQ2QTZ5MUpSWnVWQzNET3l3OHI4d3NJcDVVT3VuTzV3Mm0wbko0OVdqQkhlRnp0d3g1Wis4dG9xQnBVTjhGUWw0THNBL3NOYzVtZXJUS3FzQlVnT3NEbWtobUQ3Y1FXNW1hZjg2N242Q0FOQ1owRTVNTGdrWWNBWTdoV3R4cHlmMmNiVllVdm1UZHQ0VmNQQlJmbW5SUW5odmFTczVIdDFWVTBaeTJXMFdZTmxRek11TmRnZTg3by8remQ2RndqTkQvNzZTS1NyZ1hjUE14MWF4ODV0dmttenNzL3RjUnNFN3BndEhzVWVvcitvcWxqcUNhTDQ0TldvL214ZHY1YnBHY1o3cDkvVE1aUVZSa3ZIckFEK3FkeC9IUTJWNnF3SnBUSUw3S0k5U0REVmZ1NHNvWlBtZnkyZmtlRWpOczM4V1N0N0ZiZUdCQjZhUjM2MFhyUGJRL3ZoSDJkdUFUaUNVd2hDNGo0cEVadUJYL3VUUzA3MG1Ua09UTGQ4STd0THRzZDRzZjVaNERPdFdTblArU0VpL0Z1c3NSVmQ0SUhLbFVSVCswL2dYYUxERHhsb01DOUt2WjZ6UVlUa0F6ZFllSEMvcFp3MlBvc3lLVjM2a0gyQTErb0RRaGdVclhjNHFDZDZKZjJ4cG82SjlqZElPNGtIdnJKUDJvQjZ6RWoiLCJtYWMiOiI3ODYyYTcyMzEwNTE3YTllNGM3ZWZjYmZiZWI5ZjE1NWU3NTUzNzY1NTZkZjA2MTBlYmFjYTQzZjIxNWQwOTQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:24:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlM1QU1XUFIvR3ZQQXJ6UXBUWEloL2c9PSIsInZhbHVlIjoiejFBVjV6VC9aSGRrZlZ5aVY5WjRpZm5TSXovdzYrTC9uRkFyNlMvdys3S20zMTNLUW9ZeFdsSldkSStGWEYzQlRUSDZ0T2xqVy96NGxSZ09YZG83SjFsZjN5dVcwVFhMU0p4VGtNQlhBL0dxOG01MTh6SkJLQlR1SmpWcUpaZTFJbnowNWwwNERnSkZHQ1V5TEpwS2tVVWNDQng4WFJYNld6aVYydzltSGw1THBEa2NabHlqcDFmd0t0ZWJsUDljb3lubmVDTzd3VUtSWTZ4dyttc3l3YkEvRGYyak92dndubXdhNzNHd2xsdHFiOVdNemExaHFhM3RZdGN2UkcwU09PVlFCQ2RmakFoWHFZUDZkbGQ4VUJ2bjRuTGUyUlo2MWdaS3VVYitxbE5mUGRrWXhPRURTWW9FdGZMb3ZKQUlhclNLWktXU0tyTkxEV1gxNjhBa3phd2VVVDBqd3Q0aWgrR1l4Tk9zSjN5VHRURkMwbTUzcklSSzBmYlpKaWxyaHN6QXJiSU83Y1l3VGN5OXVnVWFsbmp2SGErYUZnS01JOFl4NDEzaTJwek9QdXRxRXhJU2RzMndWWk45WG1vOUxnVTdES1NsRW85YS96L1pOK0NvWVJKQWJUdmRoZkgxVjMvQUpLVmFCa2RHaEoySy9tNjJmekl0L3dmZHMvUVkiLCJtYWMiOiIzNjEwNWRkZWFkMDgzNGQzYTY0NzhiYmM2MWVkYzM2N2NjNWZjNzI4YWRjMzgwODlkMDEwOWNhZWE1OTc4MDgwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:24:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpQZTFLd3R2aTF0Z3RIT1FSZ2t3V3c9PSIsInZhbHVlIjoiNWQ2QTZ5MUpSWnVWQzNET3l3OHI4d3NJcDVVT3VuTzV3Mm0wbko0OVdqQkhlRnp0d3g1Wis4dG9xQnBVTjhGUWw0THNBL3NOYzVtZXJUS3FzQlVnT3NEbWtobUQ3Y1FXNW1hZjg2N242Q0FOQ1owRTVNTGdrWWNBWTdoV3R4cHlmMmNiVllVdm1UZHQ0VmNQQlJmbW5SUW5odmFTczVIdDFWVTBaeTJXMFdZTmxRek11TmRnZTg3by8remQ2RndqTkQvNzZTS1NyZ1hjUE14MWF4ODV0dmttenNzL3RjUnNFN3BndEhzVWVvcitvcWxqcUNhTDQ0TldvL214ZHY1YnBHY1o3cDkvVE1aUVZSa3ZIckFEK3FkeC9IUTJWNnF3SnBUSUw3S0k5U0REVmZ1NHNvWlBtZnkyZmtlRWpOczM4V1N0N0ZiZUdCQjZhUjM2MFhyUGJRL3ZoSDJkdUFUaUNVd2hDNGo0cEVadUJYL3VUUzA3MG1Ua09UTGQ4STd0THRzZDRzZjVaNERPdFdTblArU0VpL0Z1c3NSVmQ0SUhLbFVSVCswL2dYYUxERHhsb01DOUt2WjZ6UVlUa0F6ZFllSEMvcFp3MlBvc3lLVjM2a0gyQTErb0RRaGdVclhjNHFDZDZKZjJ4cG82SjlqZElPNGtIdnJKUDJvQjZ6RWoiLCJtYWMiOiI3ODYyYTcyMzEwNTE3YTllNGM3ZWZjYmZiZWI5ZjE1NWU3NTUzNzY1NTZkZjA2MTBlYmFjYTQzZjIxNWQwOTQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:24:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlM1QU1XUFIvR3ZQQXJ6UXBUWEloL2c9PSIsInZhbHVlIjoiejFBVjV6VC9aSGRrZlZ5aVY5WjRpZm5TSXovdzYrTC9uRkFyNlMvdys3S20zMTNLUW9ZeFdsSldkSStGWEYzQlRUSDZ0T2xqVy96NGxSZ09YZG83SjFsZjN5dVcwVFhMU0p4VGtNQlhBL0dxOG01MTh6SkJLQlR1SmpWcUpaZTFJbnowNWwwNERnSkZHQ1V5TEpwS2tVVWNDQng4WFJYNld6aVYydzltSGw1THBEa2NabHlqcDFmd0t0ZWJsUDljb3lubmVDTzd3VUtSWTZ4dyttc3l3YkEvRGYyak92dndubXdhNzNHd2xsdHFiOVdNemExaHFhM3RZdGN2UkcwU09PVlFCQ2RmakFoWHFZUDZkbGQ4VUJ2bjRuTGUyUlo2MWdaS3VVYitxbE5mUGRrWXhPRURTWW9FdGZMb3ZKQUlhclNLWktXU0tyTkxEV1gxNjhBa3phd2VVVDBqd3Q0aWgrR1l4Tk9zSjN5VHRURkMwbTUzcklSSzBmYlpKaWxyaHN6QXJiSU83Y1l3VGN5OXVnVWFsbmp2SGErYUZnS01JOFl4NDEzaTJwek9QdXRxRXhJU2RzMndWWk45WG1vOUxnVTdES1NsRW85YS96L1pOK0NvWVJKQWJUdmRoZkgxVjMvQUpLVmFCa2RHaEoySy9tNjJmekl0L3dmZHMvUVkiLCJtYWMiOiIzNjEwNWRkZWFkMDgzNGQzYTY0NzhiYmM2MWVkYzM2N2NjNWZjNzI4YWRjMzgwODlkMDEwOWNhZWE1OTc4MDgwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:24:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529325538\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2050297681 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tNVac1YYPnUlWCrdPcTX7NVXxp6D8JKJpzQWZGAZ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050297681\", {\"maxDepth\":0})</script>\n"}}