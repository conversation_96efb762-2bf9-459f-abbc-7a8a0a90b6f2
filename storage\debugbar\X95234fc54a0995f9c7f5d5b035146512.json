{"__meta": {"id": "X95234fc54a0995f9c7f5d5b035146512", "datetime": "2025-08-02 09:16:49", "utime": **********.875746, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754126208.607121, "end": **********.875773, "duration": 1.2686519622802734, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1754126208.607121, "relative_start": 0, "end": **********.804282, "relative_end": **********.804282, "duration": 1.1971609592437744, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.804296, "relative_start": 1.****************, "end": **********.875776, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "71.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "5l7Qxivph105p3ehot1ZlKYzFjdG861V7eVUN6Ap", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1778695737 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1778695737\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-3239461 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-3239461\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-54819281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-54819281\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-221193260 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221193260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1803769133 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1803769133\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1737519782 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:16:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB4WTN6cWR0TW41aXBHSFNPNmFiN2c9PSIsInZhbHVlIjoiZEJodVBTdGl0N3QrdzJxWTBzK3h4ZEZjdklBb0FhT2kwaDhWa282djMrN01DdXZrS3ZlSUFhUklCSkowWVpwaWRGV3JJVDdSU0ZMMEpXNnNUZlhZTW5HbDBEckhaTDZ3c3YzMXRGTUtpcDZCUFFNZHFqY2x6a3F5Z1N4VmRuVUdpZWhxVGp2cFB3MU51YU5kL01RZWRoV2FJOHdaaC9EdEcxdnVMcHB1dG1rM2hoTFluRldZaGp4NlNlODAyZ1BYeWF1ZzF3OHpETUhOaUpoY1dsaVhSS2VmaWJqRjAyODBwbHRrR0hoSVBlN2lsbmRzUmRRRXBORC9uQnZDbGxicTIzRGRUL3BwMTlBKzlDWmhycVRaWnJrN1VuRmJ2Undkc2dWVk52ak1keGRTZjVNQmx1M01rQysvZVZ4ay9tTTR4NlJ6YksreG9oTGR4SkpMbm9YUmgyMW81c2dVZnh4SGthU0k3VWlScU1ndFc5QzZkREdOVXgxNTFqY0s1VDdPSTVXd0Nsb21PbHZ2bjR5SXZhWHVCT09oSFFKS21UYitnazI2NnpaT1BPRDlaQ0Z1ZGFKSi9KMjlXeFYrNXRaMGFXN2JjQWsxRkxrVkQrVmd3V2pkRGZhTlkyemVsajhKUEh2dnJUTDJXVWxpbkRJeEVackk0Sk02YnIwUHFENFQiLCJtYWMiOiI3MGIxNjFlOWFmODY3M2I4NGYwZmFmZmVhNTg1OGUwODI5NTY0MWI4MjIwNGUzNTJkNzgzZmZjMGVjNTc3NWNiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:16:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZqQWgranJobkdYM09jYmhsWWxmTmc9PSIsInZhbHVlIjoiNEgrRDdNMVVPcWxZTTFnOGJ1dWVVYUJUTE1VZTVQaTg3SXovVU95dG5WMHEzYmpTUmVSbHhZNlpadFNoQ0N6U0JLWjlhaGdlOHJKZzZDMFdVRGhybEdkYThQcHhsMTdwdERNRFlIalZ4ZEFEbVhBNXVCdGFVbnBLL1Fab2ltZ1czaVVjc1dpcWdUOWxweXg1RFJBVkU0MUhYMG5ab0R1ZnhkS1VCdmJoUC9POXgvdkJCeWxObG1hVmh2OS9MNUU2M0M0RmJYQWtYSWR6YU1mZElBMzVZb1JSeFNaL25BWTUxbVAwK2ZsTTExeklkL3VQOW9JSlI2WE93c2tQWEp2UjVuZVk5NXJEL2Y3bjVMYVJPeG5YeXNyLytVbjJELzl2K3k4MVl4dkNnYXZwNFIxYjg4aFo5SUttOWxvK2NDRStTeTlQdjVVU0F0N0cwOUN2T3g5QUlURXNsc0RNS3dCOGh1NjVKSXg3UGR2eDlENXNXN24wOWo0Ym96UTJZaWxNSm1kYTZjTS8xVEllTXRMNW56azlvMFlyYVlrdnUxOW5jcjhuaVFOMkxnN3N6aGdGNWF2Y1lBd0djM3U4Y09kaGc5djM1ZFZMaUtCNUt5MHJRRmVOQk1EWlIyeW9Cak5kbCs2QmNrY3RQZGNmbkNRVTk5bS9FTmU2N2NtK3Y3YWMiLCJtYWMiOiI4MWVkNDRiMTRhZDIwOGQ4Nzg0MTI5MmNmYjNkNzhmMjY5ZWZhMWFmZjJlOTBiYjE2ZjE0ZWY2MzRhNGRkNTQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:16:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB4WTN6cWR0TW41aXBHSFNPNmFiN2c9PSIsInZhbHVlIjoiZEJodVBTdGl0N3QrdzJxWTBzK3h4ZEZjdklBb0FhT2kwaDhWa282djMrN01DdXZrS3ZlSUFhUklCSkowWVpwaWRGV3JJVDdSU0ZMMEpXNnNUZlhZTW5HbDBEckhaTDZ3c3YzMXRGTUtpcDZCUFFNZHFqY2x6a3F5Z1N4VmRuVUdpZWhxVGp2cFB3MU51YU5kL01RZWRoV2FJOHdaaC9EdEcxdnVMcHB1dG1rM2hoTFluRldZaGp4NlNlODAyZ1BYeWF1ZzF3OHpETUhOaUpoY1dsaVhSS2VmaWJqRjAyODBwbHRrR0hoSVBlN2lsbmRzUmRRRXBORC9uQnZDbGxicTIzRGRUL3BwMTlBKzlDWmhycVRaWnJrN1VuRmJ2Undkc2dWVk52ak1keGRTZjVNQmx1M01rQysvZVZ4ay9tTTR4NlJ6YksreG9oTGR4SkpMbm9YUmgyMW81c2dVZnh4SGthU0k3VWlScU1ndFc5QzZkREdOVXgxNTFqY0s1VDdPSTVXd0Nsb21PbHZ2bjR5SXZhWHVCT09oSFFKS21UYitnazI2NnpaT1BPRDlaQ0Z1ZGFKSi9KMjlXeFYrNXRaMGFXN2JjQWsxRkxrVkQrVmd3V2pkRGZhTlkyemVsajhKUEh2dnJUTDJXVWxpbkRJeEVackk0Sk02YnIwUHFENFQiLCJtYWMiOiI3MGIxNjFlOWFmODY3M2I4NGYwZmFmZmVhNTg1OGUwODI5NTY0MWI4MjIwNGUzNTJkNzgzZmZjMGVjNTc3NWNiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:16:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZqQWgranJobkdYM09jYmhsWWxmTmc9PSIsInZhbHVlIjoiNEgrRDdNMVVPcWxZTTFnOGJ1dWVVYUJUTE1VZTVQaTg3SXovVU95dG5WMHEzYmpTUmVSbHhZNlpadFNoQ0N6U0JLWjlhaGdlOHJKZzZDMFdVRGhybEdkYThQcHhsMTdwdERNRFlIalZ4ZEFEbVhBNXVCdGFVbnBLL1Fab2ltZ1czaVVjc1dpcWdUOWxweXg1RFJBVkU0MUhYMG5ab0R1ZnhkS1VCdmJoUC9POXgvdkJCeWxObG1hVmh2OS9MNUU2M0M0RmJYQWtYSWR6YU1mZElBMzVZb1JSeFNaL25BWTUxbVAwK2ZsTTExeklkL3VQOW9JSlI2WE93c2tQWEp2UjVuZVk5NXJEL2Y3bjVMYVJPeG5YeXNyLytVbjJELzl2K3k4MVl4dkNnYXZwNFIxYjg4aFo5SUttOWxvK2NDRStTeTlQdjVVU0F0N0cwOUN2T3g5QUlURXNsc0RNS3dCOGh1NjVKSXg3UGR2eDlENXNXN24wOWo0Ym96UTJZaWxNSm1kYTZjTS8xVEllTXRMNW56azlvMFlyYVlrdnUxOW5jcjhuaVFOMkxnN3N6aGdGNWF2Y1lBd0djM3U4Y09kaGc5djM1ZFZMaUtCNUt5MHJRRmVOQk1EWlIyeW9Cak5kbCs2QmNrY3RQZGNmbkNRVTk5bS9FTmU2N2NtK3Y3YWMiLCJtYWMiOiI4MWVkNDRiMTRhZDIwOGQ4Nzg0MTI5MmNmYjNkNzhmMjY5ZWZhMWFmZjJlOTBiYjE2ZjE0ZWY2MzRhNGRkNTQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:16:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737519782\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1178847993 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5l7Qxivph105p3ehot1ZlKYzFjdG861V7eVUN6Ap</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178847993\", {\"maxDepth\":0})</script>\n"}}