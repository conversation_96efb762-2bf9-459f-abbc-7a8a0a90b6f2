{"__meta": {"id": "X7f475fc372369f2c3e3de605d48e9439", "datetime": "2025-08-02 10:27:20", "utime": **********.097725, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130438.884616, "end": **********.097754, "duration": 1.2131381034851074, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1754130438.884616, "relative_start": 0, "end": 1754130439.991813, "relative_end": 1754130439.991813, "duration": 1.1071970462799072, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754130439.991876, "relative_start": 1.**************, "end": **********.097756, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VXXz95LOjJfMktN1P9gNbakv4FnC7xXbSz4UrGP5", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-789863239 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-789863239\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-611267074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-611267074\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1838465871 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1838465871\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1544162254 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544162254\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1630952476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1630952476\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-57124388 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:27:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkoxWjI4ZnE1R3dhOHZLVkQ1Q3F1Y1E9PSIsInZhbHVlIjoicVdZdldXS0s4dVhJckU2QzladEFSQjJidmdYQjQxcWhqU29OSDliV3FwcVlDT2dhdUN6eGlEUDIzK1JmcmRQeThtNXA3a1B0SVk5YWtjMmZzWWZUUlpoU1BFaDdTZUg1K2phaGdjSjVvb2ZVS3Q2b0thbDFMenFvV1dJV0RCdEwyTEFUcWx5TXpHbFpYVnRYenNXUU1pLytORjF0NU5CY1BibHdNSUtDUVJIZVlWREExYjBjVWd4Ly9mN3ZPLzhPUUdxOU1PZXJoaWd4NGFzM3ZOeG5zVURxYlJvZGdVMlVsdk9ZWGZvS2RPdGZCTDYvNkdqUmg2UGpaVGtOcHJZUEF2TVVwMVUvbERKVi9DcStKL21tTWRRVGRBdEJnSE5xdHhvVnJwdDNFV3JpWHBMUFFTMzZvQXpFSzBuNWEwOTJBQUpEcnRHUFljalovWHBwaXZNZm1vcjA1cUYwa3V6NWlMS3dMdW9tcFRaQzdQRnVWRFM1T2xEV2hQZkVMdFhlTUpBNWdNS1RCMnBERXdvVUlDWk5wenV3VFNYUHBRSGxXREtwSkd4OVFESGMxeWdrSXlSODAzSkc5L1ZTRU52aEkxWlNqWkp2NkdraXIvdUx2cjRZV21qdlBvVWJpa0p2NjhGVExkeERwZkh5ZnBOQ0V1bEdpZlZ4Z2Z2eE05d3UiLCJtYWMiOiIxMjUxYjljNDQ1NjFjYzNjYTA3ZjdhNzFmMDQyNjE4NjNhNDFlNmE5YzUxOGNmY2RlYmU0Y2U0Yzc2YzU4ODYyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:27:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImFGT283Vm05N0IxaHFEUGZkdG1uMnc9PSIsInZhbHVlIjoiVXZ2a1B4UkJWQ09pNCtHbnBLNk1HOFAyTE5DOWtGaEcrakpXRWRtYjFVM1VvU250MEZ0VUJlM1VGaXQ5WTgvbURQMk80RURyaERaSy92SkJ5OUVyemlYaGFVL3Zlc01YUDhYRkRTT0NEalJoOFg3UVdhbHNpbGhId2pUdys4OUx5TnJyanJWOEI0TlIxTEkrcVZuUW1iaUJsVFFLOGVuVjdiYVozNTNWN0s2cjcxY1RUUm9HQW41QkVuNi9PQk5uamJUaGwyRko5NlZyeFh3VzM2aUZ4Uk1tRFMzZGVzVWg3anpjRUVicCtxSW1SU1dsUmUwTTEvRVNsWHVtQm50S3FBMmp0TE1jRXdPZ1o2cTQyY2x3SE9pRkhXVnF1bW56NG96NUxQTHZ3K0RGSUR0eDRpYWpQdmZhVFlIaUlVY1hZN3doN1pTQ24xa0FQcTgvQ0VxZ2srSFdsYUhSSThaZDhpdTF1U2txQkh5cHYwbUdWRmFNZ2lNRmxkNzJUU2xacTdQc2EwZ3pjZWxpRVYrUzQ4Qmt5OXVieERKMGIvNmRoV1VzWUNKNWlNY0FLeTh1U0U1dTlKS3Q0VTdCdjFNRS9rLzY0Z0tOc0hOcDdHZmVFU1RqdW9uV1JRWFcrSDhEQmFoWTljZlN2YTM4K3hPcEVtbGdaWHROc3IzZHBhcTAiLCJtYWMiOiJkMDhjYjQ5MzgwMTUyZjgwYzlhZDg0MDA4MzA0ZTVhMTk3NDhhMTRkZDhiMzVhNGUyYmU4MTUzYmRlNmY5M2I2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:27:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkoxWjI4ZnE1R3dhOHZLVkQ1Q3F1Y1E9PSIsInZhbHVlIjoicVdZdldXS0s4dVhJckU2QzladEFSQjJidmdYQjQxcWhqU29OSDliV3FwcVlDT2dhdUN6eGlEUDIzK1JmcmRQeThtNXA3a1B0SVk5YWtjMmZzWWZUUlpoU1BFaDdTZUg1K2phaGdjSjVvb2ZVS3Q2b0thbDFMenFvV1dJV0RCdEwyTEFUcWx5TXpHbFpYVnRYenNXUU1pLytORjF0NU5CY1BibHdNSUtDUVJIZVlWREExYjBjVWd4Ly9mN3ZPLzhPUUdxOU1PZXJoaWd4NGFzM3ZOeG5zVURxYlJvZGdVMlVsdk9ZWGZvS2RPdGZCTDYvNkdqUmg2UGpaVGtOcHJZUEF2TVVwMVUvbERKVi9DcStKL21tTWRRVGRBdEJnSE5xdHhvVnJwdDNFV3JpWHBMUFFTMzZvQXpFSzBuNWEwOTJBQUpEcnRHUFljalovWHBwaXZNZm1vcjA1cUYwa3V6NWlMS3dMdW9tcFRaQzdQRnVWRFM1T2xEV2hQZkVMdFhlTUpBNWdNS1RCMnBERXdvVUlDWk5wenV3VFNYUHBRSGxXREtwSkd4OVFESGMxeWdrSXlSODAzSkc5L1ZTRU52aEkxWlNqWkp2NkdraXIvdUx2cjRZV21qdlBvVWJpa0p2NjhGVExkeERwZkh5ZnBOQ0V1bEdpZlZ4Z2Z2eE05d3UiLCJtYWMiOiIxMjUxYjljNDQ1NjFjYzNjYTA3ZjdhNzFmMDQyNjE4NjNhNDFlNmE5YzUxOGNmY2RlYmU0Y2U0Yzc2YzU4ODYyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:27:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImFGT283Vm05N0IxaHFEUGZkdG1uMnc9PSIsInZhbHVlIjoiVXZ2a1B4UkJWQ09pNCtHbnBLNk1HOFAyTE5DOWtGaEcrakpXRWRtYjFVM1VvU250MEZ0VUJlM1VGaXQ5WTgvbURQMk80RURyaERaSy92SkJ5OUVyemlYaGFVL3Zlc01YUDhYRkRTT0NEalJoOFg3UVdhbHNpbGhId2pUdys4OUx5TnJyanJWOEI0TlIxTEkrcVZuUW1iaUJsVFFLOGVuVjdiYVozNTNWN0s2cjcxY1RUUm9HQW41QkVuNi9PQk5uamJUaGwyRko5NlZyeFh3VzM2aUZ4Uk1tRFMzZGVzVWg3anpjRUVicCtxSW1SU1dsUmUwTTEvRVNsWHVtQm50S3FBMmp0TE1jRXdPZ1o2cTQyY2x3SE9pRkhXVnF1bW56NG96NUxQTHZ3K0RGSUR0eDRpYWpQdmZhVFlIaUlVY1hZN3doN1pTQ24xa0FQcTgvQ0VxZ2srSFdsYUhSSThaZDhpdTF1U2txQkh5cHYwbUdWRmFNZ2lNRmxkNzJUU2xacTdQc2EwZ3pjZWxpRVYrUzQ4Qmt5OXVieERKMGIvNmRoV1VzWUNKNWlNY0FLeTh1U0U1dTlKS3Q0VTdCdjFNRS9rLzY0Z0tOc0hOcDdHZmVFU1RqdW9uV1JRWFcrSDhEQmFoWTljZlN2YTM4K3hPcEVtbGdaWHROc3IzZHBhcTAiLCJtYWMiOiJkMDhjYjQ5MzgwMTUyZjgwYzlhZDg0MDA4MzA0ZTVhMTk3NDhhMTRkZDhiMzVhNGUyYmU4MTUzYmRlNmY5M2I2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:27:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57124388\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-83142316 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXXz95LOjJfMktN1P9gNbakv4FnC7xXbSz4UrGP5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83142316\", {\"maxDepth\":0})</script>\n"}}