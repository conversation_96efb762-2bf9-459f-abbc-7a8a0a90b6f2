{"__meta": {"id": "Xb620d2f826400f961488907308a314c2", "datetime": "2025-08-02 10:14:41", "utime": **********.170773, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:14:41] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.141517, "xdebug_link": null, "collector": "log"}, {"message": "[10:14:41] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.151045, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754129679.611667, "end": **********.170827, "duration": 1.5591599941253662, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1754129679.611667, "relative_start": 0, "end": 1754129680.926975, "relative_end": 1754129680.926975, "duration": 1.3153080940246582, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754129680.926998, "relative_start": 1.315330982208252, "end": **********.170832, "relative_end": 5.0067901611328125e-06, "duration": 0.2438340187072754, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46525704, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3006\" onclick=\"\">app/Http/Controllers/LeadController.php:3006-3062</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01034, "accumulated_duration_str": "10.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.077833, "duration": 0.00774, "duration_str": "7.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 74.855}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.13063, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 74.855, "width_percent": 13.153}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3033}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.142286, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:3033", "source": "app/Http/Controllers/LeadController.php:3033", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3033", "ajax": false, "filename": "LeadController.php", "line": "3033"}, "connection": "radhe_same", "start_percent": 88.008, "width_percent": 11.992}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-1728996002 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1728996002\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-726166621 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726166621\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-958886736 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-958886736\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-749592125 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im5ueHhRSGZDUFByNzJWb0VkM1BhdVE9PSIsInZhbHVlIjoibzhoY3RWSTN6Z2JjeThTR3FYM3RyVVZGeDJYeTEyMnAvSnVDNTBocG1BMjNJSnNTMTRzdHBjNFpqYUdwdU5EV3dxaldqN0F3ZlcxL1pOQmR4QlYxc2hjWDNiNUREb0J3bk9YOThrQW1SNXVCQk44Rk54RmcvTGpwYXUxMGZnZTJTalhFWHFxVWFTcWx5c2sxOHo0UlU0bmg0QmREUk5MUFcrZk5XSi9TOWQ1K25tdmZ2a1cyc3dLYzl5bUNWalJFa3dNTFVpc21vaHdZYTcyenR4M1hsNFE4NUJ5YXdiTEJjeHdza1k0a291azRUMUM3d2RDTTRmOWt6R3htR2hnZE10cWo1Rk9aR2pXUW9mcXJnY1Nja0JaWFplV20reWNkUkgyL083bVpBVXFBZTFHQnBzeE9kTmxRTkhXaHF0ZE40aGc1MVQxVXZETUl5cDJuc0ZTa0lIaW15K21IRzBHTVNZdUlHbUdvTUVvTVRoQlhDby82RDRzMTd2aXlpN2R5U2ZBOHBUU1BmT1U1d2toUkdBOTIwZHMvNTI4YUk5SHlrL3ZxSFBRLyttbkxUMUVuQnVObTVUTkpTOWkzYkRtMU9WVEY4dHFYb1AwRjd1T0RZeHRtUk9kVzZHaVk5cXQzOU1pQ1g1ZytGd2kvREJvdDU4TUQvWm1ucVJ0eVNWdHoiLCJtYWMiOiIzY2MyZjRiMmE2NThiMzM1NGFjYzIxNGYzZDk3MjY3MzAxYjQyMWY3ZmFmZDI5MWY0OWQ3MmQ0ZDMzMDdiNWJiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjdvMUlQT0U3QWthQ1VlZmxFd0VER2c9PSIsInZhbHVlIjoiRTdaU1oxazd1OEZ2YndHU09XYTloeGViVlVsWUNhU0tRS2xDTGNodTF3MTgzL0F6Z0dvSWcvVms5M1M5REIyVnk1a2lZeC80K1dVa1R5OXFlbzNBRTNuTlMwM1hPeDZRM2FEODh6NEdqWG10cElrdTNUVk50R0U5MGRabkY3RzdQbVhlUVlHZ0FQRDZnWlhxQjFHWnc1YnhqTjFaU2I5SFBlUk5iOFRPWFRGMEVEZTFKa1NySlpROFVFdTlNRXpDM0l2SzZTbm5Cdk01a2UxQzVQcFpuV1M0OHBTZS9hcmpHL3ZOb1IyTU9qZmlMMy9HRWhNdzkxMDZOSmJ6MFNDcmlTa3RtMGM5dU83N1hvYlFnRVZ6SUg4UWJDakRDZmp4L1BFc0huWUc5Y01ZSXZDT2l0RSs5RkZ0YUVaWTZ0ZlFPY2pPcmtxMFJZVjRVRnExN0FRWUFtV0dFR3dxRnQ5YkU5TUVJNTl6ZzVYd0tIUE1HTlZ3YXlIb01lR3A4WmZJZ0J2bzcwSS8yd1FZSXBBMUdFTlRvMFdzUy9NMnJ6SllrUWJ4UGNLM09GUWtvZW5sZXhJeWZtQTMxQ29NYnRZSkM3S0dvMnJOMkcxalo5NXA4Y0p3U29SQlB1MHhqcWlhNStZUWZ1S1IvMDlmdEk5VVgrRi8vbmZuaXJCWWhhTVkiLCJtYWMiOiI4MjU5MzNkNTcyM2M5YWVlNWJmMmYzYzI4MzE2ODQ4ZmIzODkwNWU0ZmFmNWU0Y2FiODVlOTUxOWNkMDA4MjRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749592125\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1632564770 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632564770\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1728100645 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:14:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImU0MHErdlozcEkybmJyNEJYcmh1a1E9PSIsInZhbHVlIjoiSGtUUXlxMExRcENPOFYrdnRIaURlK1JBU0FEbWo4VVpOQ3ZZaklCeldWc1c0OTFjRnV4RWd4bnRoMjZyTTdqMVZXdDJFVjVsRk9IdlR0VHdkbjZFWTBzNWZaWURTTFN2UVlIMkY2Z3N1enE3K2lzS2lVWHk4czhhUXNmeXFZSGdkbGFKZDdNUlYwY1FRanJSTmNrdWhJWlBZM2tNdEEzVkFrR1JLUDlrbHBaYUFNODZhTGxpa0pidU1ZRkdkQ0JmNXRNeXVNTHdDZXY1dW9CYnVPbldMdnBnUXpiRk54OG9rTE9aNzliNitZKzRVZTJzWkl1NlJMUFozTEJRKzFPcHhSL2E2T3VMaXk2Z1hwQ0tZZ0ZnYS9xZWVyYXQvK0dtQmoyS213Uk5jM0w2RkJ5TXA2UXRTTGlhNlZYSTI1dS9Za1Z0YmNLNFdNZjZucDBsRG5SWENhamVkZnR0SDF0N2pwb2FPZlVsZngyUS9xQ0VZRXh3N0tnbXExa09MTHFDRHJDbWltZGJIVFhiNzhXRThjdmFOUXBhMGYzWFA2UFZrQUpFT3FMbUJBRVQ3RS9XakxKUTMrcHZOcVhBMEw1U1JCNHNDNitRTll2a3RTdXMvQlB6OG9SU3dzMmpsMEtjUjhVZ2xOQU1RV1dZelJnSm1ITHJsb3VqeU5tVTBmNlMiLCJtYWMiOiIzYzY2ZTc5YjZjMjc2YjFkN2Y2MWRmNzdhYzRjOGRiYjUyYTBhMjYyYTg5ZTNiN2JhMmQwN2Y4NDNmZjZjN2NiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:14:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVTeHpMTmc4NmpLWkFUcW5oWUxiNGc9PSIsInZhbHVlIjoib2dVQXc2bG1ReTkreVlLd1BmSDUrRnpacS9hazZnaVc0VHNld3BEZlJGTnhTQ00yV0VzMzcrRGpnVW9pMUViU0ZDYlJQNmE0Tjdwd1ZzdDRRNi9SWVhLUHlTN1RReFU0NXk4QXN3RFRoRG95R0JmdDB4ZFNBTUpDb3JNYmVRVFpKN1pFUUVNMVk5MmZLTWd2cFlWamRzVHBNdndtTktDZFN6WVVEYVJYM0Z4WFgreTZORlg3SVdsSzJNQm1FRkQ5SWZhbFdBQ0h3THczdjQ4d1BXdnZVbmdwNE9oRzkyZ210NEMrOUlWUXdwdU5PQm44QzVUYjgrNXQ3NnRTTytmYWZxaElLdGxDWkU5QktyeXIxckE2U2RsM2ZDSmlmWjh6OGVvcS9CdHQwU04xZlROMGJHKy9uQmZKNGJJc2lQenJRUTI5L0lHb1dsa3VIUzY0b2FmS01MK3VmdFRGZ0hGbVUvOHI5ZXB3RHlHZEsyV0t4d0lOMlNQVFZtektqRDBkRzRLdW9ERHk3bVJSTnRGbWptRm1iaVR4TU4xdThFWjVhN2VWYkVBdURmY1RodmZGdFpjaWlGdFZYbGc2b3VUa1N3cTlKTVVYSmN2eE1rMkVRcFRoR2N0SUU3NldpeUxwUzFFQXd6dlpyTGN1Qk5aZTBFeGhLcnRlMGoxREkxMlAiLCJtYWMiOiJmNjNmNThiYmE2ZTAwZGFjZDYyYTgxNjk3YTdlM2FlMjhjNWU2ODYyZjQwNzlkZjYwYTI2ZDE1Zjk5NGNjMzJjIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:14:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImU0MHErdlozcEkybmJyNEJYcmh1a1E9PSIsInZhbHVlIjoiSGtUUXlxMExRcENPOFYrdnRIaURlK1JBU0FEbWo4VVpOQ3ZZaklCeldWc1c0OTFjRnV4RWd4bnRoMjZyTTdqMVZXdDJFVjVsRk9IdlR0VHdkbjZFWTBzNWZaWURTTFN2UVlIMkY2Z3N1enE3K2lzS2lVWHk4czhhUXNmeXFZSGdkbGFKZDdNUlYwY1FRanJSTmNrdWhJWlBZM2tNdEEzVkFrR1JLUDlrbHBaYUFNODZhTGxpa0pidU1ZRkdkQ0JmNXRNeXVNTHdDZXY1dW9CYnVPbldMdnBnUXpiRk54OG9rTE9aNzliNitZKzRVZTJzWkl1NlJMUFozTEJRKzFPcHhSL2E2T3VMaXk2Z1hwQ0tZZ0ZnYS9xZWVyYXQvK0dtQmoyS213Uk5jM0w2RkJ5TXA2UXRTTGlhNlZYSTI1dS9Za1Z0YmNLNFdNZjZucDBsRG5SWENhamVkZnR0SDF0N2pwb2FPZlVsZngyUS9xQ0VZRXh3N0tnbXExa09MTHFDRHJDbWltZGJIVFhiNzhXRThjdmFOUXBhMGYzWFA2UFZrQUpFT3FMbUJBRVQ3RS9XakxKUTMrcHZOcVhBMEw1U1JCNHNDNitRTll2a3RTdXMvQlB6OG9SU3dzMmpsMEtjUjhVZ2xOQU1RV1dZelJnSm1ITHJsb3VqeU5tVTBmNlMiLCJtYWMiOiIzYzY2ZTc5YjZjMjc2YjFkN2Y2MWRmNzdhYzRjOGRiYjUyYTBhMjYyYTg5ZTNiN2JhMmQwN2Y4NDNmZjZjN2NiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:14:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVTeHpMTmc4NmpLWkFUcW5oWUxiNGc9PSIsInZhbHVlIjoib2dVQXc2bG1ReTkreVlLd1BmSDUrRnpacS9hazZnaVc0VHNld3BEZlJGTnhTQ00yV0VzMzcrRGpnVW9pMUViU0ZDYlJQNmE0Tjdwd1ZzdDRRNi9SWVhLUHlTN1RReFU0NXk4QXN3RFRoRG95R0JmdDB4ZFNBTUpDb3JNYmVRVFpKN1pFUUVNMVk5MmZLTWd2cFlWamRzVHBNdndtTktDZFN6WVVEYVJYM0Z4WFgreTZORlg3SVdsSzJNQm1FRkQ5SWZhbFdBQ0h3THczdjQ4d1BXdnZVbmdwNE9oRzkyZ210NEMrOUlWUXdwdU5PQm44QzVUYjgrNXQ3NnRTTytmYWZxaElLdGxDWkU5QktyeXIxckE2U2RsM2ZDSmlmWjh6OGVvcS9CdHQwU04xZlROMGJHKy9uQmZKNGJJc2lQenJRUTI5L0lHb1dsa3VIUzY0b2FmS01MK3VmdFRGZ0hGbVUvOHI5ZXB3RHlHZEsyV0t4d0lOMlNQVFZtektqRDBkRzRLdW9ERHk3bVJSTnRGbWptRm1iaVR4TU4xdThFWjVhN2VWYkVBdURmY1RodmZGdFpjaWlGdFZYbGc2b3VUa1N3cTlKTVVYSmN2eE1rMkVRcFRoR2N0SUU3NldpeUxwUzFFQXd6dlpyTGN1Qk5aZTBFeGhLcnRlMGoxREkxMlAiLCJtYWMiOiJmNjNmNThiYmE2ZTAwZGFjZDYyYTgxNjk3YTdlM2FlMjhjNWU2ODYyZjQwNzlkZjYwYTI2ZDE1Zjk5NGNjMzJjIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:14:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728100645\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1540893835 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540893835\", {\"maxDepth\":0})</script>\n"}}