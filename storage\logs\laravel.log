[2025-08-02 08:16:28] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 08:16:28] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 08:16:36] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:16:39] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:18:54] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 08:18:54] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 08:19:53] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"+918987895025","subject":null,"user_id":"81","date_of_birth":"2025-07-30","next_follow_up_date":"2025-08-17","type":"lead","pipeline_id":"23","stage_id":"86","status":"warm","opportunity_info":null,"opportunity_source":"referral","lead_value":null,"opportunity_description":"ok","labels":["89","90","new_pa","new_VPN"]}} 
[2025-08-02 08:19:53] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"81"} 
[2025-08-02 08:19:53] local.INFO: Processing tags {"tagsInput":["89","90","new_pa","new_VPN"],"has_labels":true,"has_tags":false,"labels_value":["89","90","new_pa","new_VPN"],"tags_value":null} 
[2025-08-02 08:19:53] local.INFO: Using existing tag ID {"tag_id":"89"} 
[2025-08-02 08:19:53] local.INFO: Using existing tag ID {"tag_id":"90"} 
[2025-08-02 08:19:53] local.ERROR: Error processing tag {"tag":"new_pa","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (pa, primary, 79, 1, 2025-08-02 08:19:53, 2025-08-02 08:19:53))"} 
[2025-08-02 08:19:53] local.ERROR: Error processing tag {"tag":"new_VPN","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (VPN, primary, 79, 1, 2025-08-02 08:19:53, 2025-08-02 08:19:53))"} 
[2025-08-02 08:19:53] local.INFO: Set lead tags {"tag_ids":["89","90"],"tags_string":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}]}} 
[2025-08-02 08:19:53] local.INFO: Lead saved successfully {"lead_id":15} 
[2025-08-02 08:19:53] local.INFO: UserLead relationships created successfully  
[2025-08-02 08:19:54] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 08:19:54] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 08:19:54] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 08:19:56] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 08:19:56] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:20:23] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 08:20:24] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 08:20:28] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:20:29] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:20:41] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_stage_changed  
[2025-08-02 08:20:41] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_stage_changed  
[2025-08-02 08:20:41] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 08:20:43] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_stage_changed  
[2025-08-02 08:20:43] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/lead_stages/move-lead","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:20:57] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads/json","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:21:30] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:21:31] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:21:50] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 08:21:51] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 08:21:55] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:21:56] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:23:16] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 08:23:16] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 08:24:11] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Raja Kumar Singh","email":"<EMAIL>","phone":"+916532658905","subject":null,"user_id":"81","date_of_birth":"2025-07-30","next_follow_up_date":"2025-08-15","type":"lead","pipeline_id":"23","stage_id":"87","status":"hot","opportunity_info":null,"opportunity_source":"website","lead_value":"8900","opportunity_description":null,"labels":["89","91","new_Parichay ok"]}} 
[2025-08-02 08:24:12] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":87,"user_id":"81"} 
[2025-08-02 08:24:12] local.INFO: Processing tags {"tagsInput":["89","91","new_Parichay ok"],"has_labels":true,"has_tags":false,"labels_value":["89","91","new_Parichay ok"],"tags_value":null} 
[2025-08-02 08:24:12] local.INFO: Using existing tag ID {"tag_id":"89"} 
[2025-08-02 08:24:12] local.INFO: Using existing tag ID {"tag_id":"91"} 
[2025-08-02 08:24:12] local.ERROR: Error processing tag {"tag":"new_Parichay ok","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Parichay ok, primary, 79, 1, 2025-08-02 08:24:12, 2025-08-02 08:24:12))"} 
[2025-08-02 08:24:12] local.INFO: Set lead tags {"tag_ids":["89","91"],"tags_string":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}]}} 
[2025-08-02 08:24:12] local.INFO: Lead saved successfully {"lead_id":16} 
[2025-08-02 08:24:12] local.INFO: UserLead relationships created successfully  
[2025-08-02 08:24:12] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 08:24:12] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 08:24:12] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 08:24:14] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 08:24:14] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:24:31] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 08:24:31] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 08:24:35] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:24:36] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:26:48] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads/json","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:27:31] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:27:34] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:28:42] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads/16/labels","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:28:57] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:29:00] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:29:14] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 08:29:15] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 08:29:24] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:29:26] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:37:22] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 08:37:23] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 08:37:31] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:37:34] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:38:42] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 08:38:42] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 08:39:43] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Mr Mukharji","email":"<EMAIL>","phone":"+918975620126","subject":null,"user_id":"81","date_of_birth":"2025-07-30","next_follow_up_date":"2025-08-23","type":"lead","pipeline_id":"23","stage_id":"87","status":"hot","opportunity_info":null,"opportunity_source":"phone","lead_value":"8900","opportunity_description":"ok","labels":["89","90","91","92","93","new_Parichay AI","new_ROCK"]}} 
[2025-08-02 08:39:44] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":87,"user_id":"81"} 
[2025-08-02 08:39:44] local.INFO: Processing tags {"tagsInput":["89","90","91","92","93","new_Parichay AI","new_ROCK"],"has_labels":true,"has_tags":false,"labels_value":["89","90","91","92","93","new_Parichay AI","new_ROCK"],"tags_value":null} 
[2025-08-02 08:39:44] local.INFO: Using existing tag ID {"tag_id":"89"} 
[2025-08-02 08:39:44] local.INFO: Using existing tag ID {"tag_id":"90"} 
[2025-08-02 08:39:44] local.INFO: Using existing tag ID {"tag_id":"91"} 
[2025-08-02 08:39:44] local.INFO: Using existing tag ID {"tag_id":"92"} 
[2025-08-02 08:39:44] local.INFO: Using existing tag ID {"tag_id":"93"} 
[2025-08-02 08:39:44] local.ERROR: Error processing tag {"tag":"new_Parichay AI","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Parichay AI, primary, 79, 1, 2025-08-02 08:39:44, 2025-08-02 08:39:44))"} 
[2025-08-02 08:39:44] local.ERROR: Error processing tag {"tag":"new_ROCK","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (ROCK, primary, 79, 1, 2025-08-02 08:39:44, 2025-08-02 08:39:44))"} 
[2025-08-02 08:39:44] local.INFO: Set lead tags {"tag_ids":["89","90","91","92","93"],"tags_string":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}]}} 
[2025-08-02 08:39:44] local.INFO: Lead saved successfully {"lead_id":17,"tags_field":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":89,"name":"Cold Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}]},"tags_method_count":5,"tags_method_data":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 08:39:44] local.INFO: UserLead relationships created successfully  
[2025-08-02 08:39:44] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 08:39:44] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 08:39:44] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 08:39:46] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 08:39:46] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:40:22] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 08:40:23] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 08:40:35] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:40:38] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 08:40:42] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_stage_changed  
[2025-08-02 08:40:42] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_stage_changed  
[2025-08-02 08:40:42] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 08:40:44] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_stage_changed  
[2025-08-02 08:40:44] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/lead_stages/move-lead","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:09:16] local.DEBUG: Lead tags retrieved {"lead_id":12,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],89,92,88,93,91,90","labels_field":null,"tag_ids":{"6":"89","7":"92","8":"88","9":"93","10":"91","11":"90"},"tags_found":6,"tag_names":["Cold Lead","Follow Up","Hot Lead","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:09:16] local.DEBUG: Lead tags retrieved {"lead_id":17,"tags_field":"89,90,91,92,93","labels_field":null,"tag_ids":["89","90","91","92","93"],"tags_found":5,"tag_names":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:09:16] local.DEBUG: Lead tags retrieved {"lead_id":15,"tags_field":"89,90","labels_field":null,"tag_ids":["89","90"],"tags_found":2,"tag_names":["Cold Lead","Warm Lead"]} 
[2025-08-02 09:09:16] local.DEBUG: Lead tags retrieved {"lead_id":16,"tags_field":"89,90,91,95","labels_field":null,"tag_ids":["89","90","91","95"],"tags_found":4,"tag_names":["Cold Lead","ok bot","VIP","Warm Lead"]} 
[2025-08-02 09:09:16] local.DEBUG: Lead tags retrieved {"lead_id":11,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],88,91,92,93","labels_field":null,"tag_ids":{"6":"88","7":"91","8":"92","9":"93"},"tags_found":4,"tag_names":["Follow Up","Hot Lead","New Customer","VIP"]} 
[2025-08-02 09:09:16] local.DEBUG: Lead tags retrieved {"lead_id":13,"tags_field":"[],88","labels_field":null,"tag_ids":{"1":"88"},"tags_found":1,"tag_names":["Hot Lead"]} 
[2025-08-02 09:09:16] local.DEBUG: Lead tags retrieved {"lead_id":14,"tags_field":"90,92","labels_field":null,"tag_ids":["90","92"],"tags_found":2,"tag_names":["Follow Up","Warm Lead"]} 
[2025-08-02 09:09:30] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:09:31] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:09:38] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:09:40] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:10:56] local.DEBUG: Lead tags retrieved {"lead_id":12,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],89,92,88,93,91,90","labels_field":null,"tag_ids":{"6":"89","7":"92","8":"88","9":"93","10":"91","11":"90"},"tags_found":6,"tag_names":["Cold Lead","Follow Up","Hot Lead","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:10:56] local.DEBUG: Lead tags retrieved {"lead_id":17,"tags_field":"89,90,91,92,93","labels_field":null,"tag_ids":["89","90","91","92","93"],"tags_found":5,"tag_names":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:10:56] local.DEBUG: Lead tags retrieved {"lead_id":15,"tags_field":"89,90","labels_field":null,"tag_ids":["89","90"],"tags_found":2,"tag_names":["Cold Lead","Warm Lead"]} 
[2025-08-02 09:10:56] local.DEBUG: Lead tags retrieved {"lead_id":16,"tags_field":"89,90,91,95","labels_field":null,"tag_ids":["89","90","91","95"],"tags_found":4,"tag_names":["Cold Lead","ok bot","VIP","Warm Lead"]} 
[2025-08-02 09:10:56] local.DEBUG: Lead tags retrieved {"lead_id":11,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],88,91,92,93","labels_field":null,"tag_ids":{"6":"88","7":"91","8":"92","9":"93"},"tags_found":4,"tag_names":["Follow Up","Hot Lead","New Customer","VIP"]} 
[2025-08-02 09:10:56] local.DEBUG: Lead tags retrieved {"lead_id":13,"tags_field":"[],88","labels_field":null,"tag_ids":{"1":"88"},"tags_found":1,"tag_names":["Hot Lead"]} 
[2025-08-02 09:10:56] local.DEBUG: Lead tags retrieved {"lead_id":14,"tags_field":"90,92","labels_field":null,"tag_ids":["90","92"],"tags_found":2,"tag_names":["Follow Up","Warm Lead"]} 
[2025-08-02 09:11:10] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:11:11] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:11:19] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:11:21] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:12:12] local.DEBUG: Lead tags retrieved {"lead_id":12,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],89,92,88,93,91,90","labels_field":null,"tag_ids":{"6":"89","7":"92","8":"88","9":"93","10":"91","11":"90"},"tags_found":6,"tag_names":["Cold Lead","Follow Up","Hot Lead","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:12:12] local.DEBUG: Lead tags retrieved {"lead_id":17,"tags_field":"89,90,91,92,93","labels_field":null,"tag_ids":["89","90","91","92","93"],"tags_found":5,"tag_names":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:12:12] local.DEBUG: Lead tags retrieved {"lead_id":15,"tags_field":"89,90","labels_field":null,"tag_ids":["89","90"],"tags_found":2,"tag_names":["Cold Lead","Warm Lead"]} 
[2025-08-02 09:12:12] local.DEBUG: Lead tags retrieved {"lead_id":16,"tags_field":"89,90,91,95","labels_field":null,"tag_ids":["89","90","91","95"],"tags_found":4,"tag_names":["Cold Lead","ok bot","VIP","Warm Lead"]} 
[2025-08-02 09:12:12] local.DEBUG: Lead tags retrieved {"lead_id":11,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],88,91,92,93","labels_field":null,"tag_ids":{"6":"88","7":"91","8":"92","9":"93"},"tags_found":4,"tag_names":["Follow Up","Hot Lead","New Customer","VIP"]} 
[2025-08-02 09:12:12] local.DEBUG: Lead tags retrieved {"lead_id":13,"tags_field":"[],88","labels_field":null,"tag_ids":{"1":"88"},"tags_found":1,"tag_names":["Hot Lead"]} 
[2025-08-02 09:12:12] local.DEBUG: Lead tags retrieved {"lead_id":14,"tags_field":"90,92","labels_field":null,"tag_ids":["90","92"],"tags_found":2,"tag_names":["Follow Up","Warm Lead"]} 
[2025-08-02 09:12:24] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:12:25] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:12:31] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:12:32] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:13:51] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 09:13:51] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 09:15:05] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Anurak Mukharji","email":"<EMAIL>","phone":"+918965201235","subject":null,"user_id":"81","date_of_birth":"2025-07-30","next_follow_up_date":"2025-08-16","type":"lead","pipeline_id":"23","stage_id":"90","status":"hot","opportunity_info":null,"opportunity_source":"social_media","lead_value":null,"opportunity_description":"ok","labels":["88","90","91","92","93","94","new_Ok Paruchay","new_Mr DEMON"]}} 
[2025-08-02 09:15:05] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":90,"user_id":"81"} 
[2025-08-02 09:15:05] local.INFO: Processing tags {"tagsInput":["88","90","91","92","93","94","new_Ok Paruchay","new_Mr DEMON"],"has_labels":true,"has_tags":false,"labels_value":["88","90","91","92","93","94","new_Ok Paruchay","new_Mr DEMON"],"tags_value":null} 
[2025-08-02 09:15:05] local.INFO: Using existing tag ID {"tag_id":"88"} 
[2025-08-02 09:15:05] local.INFO: Using existing tag ID {"tag_id":"90"} 
[2025-08-02 09:15:05] local.INFO: Using existing tag ID {"tag_id":"91"} 
[2025-08-02 09:15:05] local.INFO: Using existing tag ID {"tag_id":"92"} 
[2025-08-02 09:15:05] local.INFO: Using existing tag ID {"tag_id":"93"} 
[2025-08-02 09:15:05] local.INFO: Using existing tag ID {"tag_id":"94"} 
[2025-08-02 09:15:05] local.ERROR: Error processing tag {"tag":"new_Ok Paruchay","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Ok Paruchay, primary, 79, 1, 2025-08-02 09:15:05, 2025-08-02 09:15:05))"} 
[2025-08-02 09:15:05] local.ERROR: Error processing tag {"tag":"new_Mr DEMON","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Mr DEMON, primary, 79, 1, 2025-08-02 09:15:05, 2025-08-02 09:15:05))"} 
[2025-08-02 09:15:05] local.INFO: Set lead tags {"tag_ids":["88","90","91","92","93","94"],"tags_string":"88,90,91,92,93,94"} 
[2025-08-02 09:15:05] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:15:05] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:15:05] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:15:05] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:15:05] local.INFO: Lead saved successfully {"lead_id":18,"tags_field":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":88,"name":"Hot Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":94,"name":"Ok AUI","created_by":79,"is_active":1,"created_at":"2025-08-02T07:52:49.000000Z","updated_at":"2025-08-02T07:52:49.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"}]},"labels_field":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":88,"name":"Hot Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":90,"name":"Warm Lead","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":91,"name":"VIP","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":92,"name":"Follow Up","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":93,"name":"New Customer","created_by":79,"is_active":1,"created_at":"2025-07-30T17:17:23.000000Z","updated_at":"2025-07-30T17:17:23.000000Z"},{"id":94,"name":"Ok AUI","created_by":79,"is_active":1,"created_at":"2025-08-02T07:52:49.000000Z","updated_at":"2025-08-02T07:52:49.000000Z"}]},"tags_method_count":6,"tags_method_data":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"],"raw_attributes":{"tags":"88,90,91,92,93,94","labels":"88,90,91,92,93,94"}} 
[2025-08-02 09:15:05] local.INFO: UserLead relationships created successfully  
[2025-08-02 09:15:05] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 09:15:05] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 09:15:05] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 09:15:07] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 09:15:07] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:15:07] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:15:07] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":12,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],89,92,88,93,91,90","labels_field":null,"tag_ids":{"6":"89","7":"92","8":"88","9":"93","10":"91","11":"90"},"tags_found":6,"tag_names":["Cold Lead","Follow Up","Hot Lead","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":17,"tags_field":"89,90,91,92,93","labels_field":null,"tag_ids":["89","90","91","92","93"],"tags_found":5,"tag_names":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":15,"tags_field":"89,90","labels_field":null,"tag_ids":["89","90"],"tags_found":2,"tag_names":["Cold Lead","Warm Lead"]} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":16,"tags_field":"89,90,91,95","labels_field":null,"tag_ids":["89","90","91","95"],"tags_found":4,"tag_names":["Cold Lead","ok bot","VIP","Warm Lead"]} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":11,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],88,91,92,93","labels_field":null,"tag_ids":{"6":"88","7":"91","8":"92","9":"93"},"tags_found":4,"tag_names":["Follow Up","Hot Lead","New Customer","VIP"]} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":13,"tags_field":"[],88","labels_field":null,"tag_ids":{"1":"88"},"tags_found":1,"tag_names":["Hot Lead"]} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":14,"tags_field":"90,92","labels_field":null,"tag_ids":["90","92"],"tags_found":2,"tag_names":["Follow Up","Warm Lead"]} 
[2025-08-02 09:15:10] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:15:29] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:15:29] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:15:33] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:15:33] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:02] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:03] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:09] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads/json","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:20] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94"],"tags_found":6,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","VIP","Warm Lead"]} 
[2025-08-02 09:16:35] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads/18/labels","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:43] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:44] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":12,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],89,92,88,93,91,90","labels_field":null,"tag_ids":{"6":"89","7":"92","8":"88","9":"93","10":"91","11":"90"},"tags_found":6,"tag_names":["Cold Lead","Follow Up","Hot Lead","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":17,"tags_field":"89,90,91,92,93","labels_field":null,"tag_ids":["89","90","91","92","93"],"tags_found":5,"tag_names":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":15,"tags_field":"89,90","labels_field":null,"tag_ids":["89","90"],"tags_found":2,"tag_names":["Cold Lead","Warm Lead"]} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":16,"tags_field":"89,90,91,95","labels_field":null,"tag_ids":["89","90","91","95"],"tags_found":4,"tag_names":["Cold Lead","ok bot","VIP","Warm Lead"]} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":11,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],88,91,92,93","labels_field":null,"tag_ids":{"6":"88","7":"91","8":"92","9":"93"},"tags_found":4,"tag_names":["Follow Up","Hot Lead","New Customer","VIP"]} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":13,"tags_field":"[],88","labels_field":null,"tag_ids":{"1":"88"},"tags_found":1,"tag_names":["Hot Lead"]} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":14,"tags_field":"90,92","labels_field":null,"tag_ids":["90","92"],"tags_found":2,"tag_names":["Follow Up","Warm Lead"]} 
[2025-08-02 09:16:47] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94,96","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94","96"],"tags_found":7,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","ok bot ai","VIP","Warm Lead"]} 
[2025-08-02 09:16:52] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:16:53] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:16:58] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:16:59] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:27:30] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:27:31] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:27:35] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94,96","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94","96"],"tags_found":7,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","ok bot ai","VIP","Warm Lead"]} 
[2025-08-02 09:27:58] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads/18/labels","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:28:07] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:28:09] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:28:10] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94,96,97","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94","96","97"],"tags_found":8,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","ok bot ai","Paaa","VIP","Warm Lead"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":12,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],89,92,88,93,91,90","labels_field":null,"tag_ids":{"6":"89","7":"92","8":"88","9":"93","10":"91","11":"90"},"tags_found":6,"tag_names":["Cold Lead","Follow Up","Hot Lead","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":17,"tags_field":"89,90,91,92,93","labels_field":null,"tag_ids":["89","90","91","92","93"],"tags_found":5,"tag_names":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":15,"tags_field":"89,90","labels_field":null,"tag_ids":["89","90"],"tags_found":2,"tag_names":["Cold Lead","Warm Lead"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":16,"tags_field":"89,90,91,95","labels_field":null,"tag_ids":["89","90","91","95"],"tags_found":4,"tag_names":["Cold Lead","ok bot","VIP","Warm Lead"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":11,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],88,91,92,93","labels_field":null,"tag_ids":{"6":"88","7":"91","8":"92","9":"93"},"tags_found":4,"tag_names":["Follow Up","Hot Lead","New Customer","VIP"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":13,"tags_field":"[],88","labels_field":null,"tag_ids":{"1":"88"},"tags_found":1,"tag_names":["Hot Lead"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":14,"tags_field":"90,92","labels_field":null,"tag_ids":["90","92"],"tags_found":2,"tag_names":["Follow Up","Warm Lead"]} 
[2025-08-02 09:30:31] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94,96,97","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94","96","97"],"tags_found":8,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","ok bot ai","Paaa","VIP","Warm Lead"]} 
[2025-08-02 09:30:35] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:30:35] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:30:39] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:30:40] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:31:36] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":null,"email":null,"phone":null,"subject":null,"user_id":null,"date_of_birth":null,"next_follow_up_date":null,"type":null,"pipeline_id":null,"stage_id":null,"status":null,"opportunity_info":null,"opportunity_source":null,"lead_value":null,"opportunity_description":null,"labels":["new_bls"]}} 
[2025-08-02 09:31:37] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:31:52] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"+918617555736","subject":null,"user_id":"81","date_of_birth":null,"next_follow_up_date":null,"type":null,"pipeline_id":null,"stage_id":null,"status":null,"opportunity_info":null,"opportunity_source":null,"lead_value":null,"opportunity_description":null,"labels":["new_bls"]}} 
[2025-08-02 09:31:52] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"81"} 
[2025-08-02 09:31:52] local.INFO: Processing tags {"tagsInput":["new_bls"],"has_labels":true,"has_tags":false,"labels_value":["new_bls"],"tags_value":null} 
[2025-08-02 09:31:52] local.ERROR: Error processing tag {"tag":"new_bls","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (bls, primary, 79, 1, 2025-08-02 09:31:52, 2025-08-02 09:31:52))"} 
[2025-08-02 09:31:52] local.INFO: No valid tags found, setting to null  
[2025-08-02 09:31:52] local.INFO: Lead saved successfully {"lead_id":19,"tags_field":{"Illuminate\\Support\\Collection":[]},"labels_field":{"Illuminate\\Support\\Collection":[]},"tags_method_count":0,"tags_method_data":[],"raw_attributes":{"tags":null,"labels":null}} 
[2025-08-02 09:31:52] local.INFO: UserLead relationships created successfully  
[2025-08-02 09:31:52] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 09:31:52] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 09:31:52] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 09:31:54] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 09:31:54] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":12,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],89,92,88,93,91,90","labels_field":null,"tag_ids":{"6":"89","7":"92","8":"88","9":"93","10":"91","11":"90"},"tags_found":6,"tag_names":["Cold Lead","Follow Up","Hot Lead","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":17,"tags_field":"89,90,91,92,93","labels_field":null,"tag_ids":["89","90","91","92","93"],"tags_found":5,"tag_names":["Cold Lead","Follow Up","New Customer","VIP","Warm Lead"]} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":15,"tags_field":"89,90","labels_field":null,"tag_ids":["89","90"],"tags_found":2,"tag_names":["Cold Lead","Warm Lead"]} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":16,"tags_field":"89,90,91,95","labels_field":null,"tag_ids":["89","90","91","95"],"tags_found":4,"tag_names":["Cold Lead","ok bot","VIP","Warm Lead"]} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":11,"tags_field":"[{\"id\":88,\"name\":\"Hot Lead\",\"created_by\":79,\"is_active\":1,\"created_at\":\"2025-07-30T17:17:23.000000Z\",\"updated_at\":\"2025-07-30T17:17:23.000000Z\"}],88,91,92,93","labels_field":null,"tag_ids":{"6":"88","7":"91","8":"92","9":"93"},"tags_found":4,"tag_names":["Follow Up","Hot Lead","New Customer","VIP"]} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":13,"tags_field":"[],88","labels_field":null,"tag_ids":{"1":"88"},"tags_found":1,"tag_names":["Hot Lead"]} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":14,"tags_field":"90,92","labels_field":null,"tag_ids":["90","92"],"tags_found":2,"tag_names":["Follow Up","Warm Lead"]} 
[2025-08-02 09:31:57] local.DEBUG: Lead tags retrieved {"lead_id":18,"tags_field":"88,90,91,92,93,94,96,97","labels_field":"88,90,91,92,93,94","tag_ids":["88","90","91","92","93","94","96","97"],"tags_found":8,"tag_names":["Follow Up","Hot Lead","New Customer","Ok AUI","ok bot ai","Paaa","VIP","Warm Lead"]} 
[2025-08-02 09:32:11] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:32:11] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:32:15] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:32:16] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:34:40] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:34:41] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:34:48] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:34:49] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:35:59] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 09:35:59] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 09:36:19] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Rani Kumar","email":"<EMAIL>","phone":"+9168984585980","subject":null,"user_id":"81","date_of_birth":null,"next_follow_up_date":null,"type":null,"pipeline_id":"23","stage_id":"86","status":"won","opportunity_info":null,"opportunity_source":null,"lead_value":null,"opportunity_description":null,"labels":["90"]}} 
[2025-08-02 09:36:19] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"81"} 
[2025-08-02 09:36:19] local.INFO: Lead saved successfully {"lead_id":20} 
[2025-08-02 09:36:19] local.INFO: UserLead relationships created successfully  
[2025-08-02 09:36:19] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 09:36:19] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 09:36:19] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 09:36:21] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 09:36:21] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:36:38] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:36:38] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:36:42] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:36:43] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:38:41] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:38:43] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:41:04] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:41:04] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:41:08] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:41:09] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:41:35] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":null,"email":null,"phone":null,"subject":null,"user_id":null,"date_of_birth":null,"next_follow_up_date":null,"type":null,"pipeline_id":null,"stage_id":null,"status":null,"opportunity_info":null,"opportunity_source":null,"lead_value":null,"opportunity_description":null}} 
[2025-08-02 09:41:36] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:42:03] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Ranu","email":"<EMAIL>","phone":"+915896584789","subject":null,"user_id":"81","date_of_birth":null,"next_follow_up_date":null,"type":null,"pipeline_id":null,"stage_id":null,"status":null,"opportunity_info":null,"opportunity_source":null,"lead_value":null,"opportunity_description":null}} 
[2025-08-02 09:42:03] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"81"} 
[2025-08-02 09:42:03] local.INFO: Lead saved successfully {"lead_id":21} 
[2025-08-02 09:42:03] local.INFO: UserLead relationships created successfully  
[2025-08-02 09:42:03] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 09:42:03] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 09:42:03] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 09:42:05] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 09:42:05] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:42:25] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:42:25] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:42:30] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:42:32] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:44:00] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 09:44:00] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 09:44:28] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Mintu Roy Barman","email":"<EMAIL>","phone":"+916895452012","subject":null,"user_id":"81","date_of_birth":null,"next_follow_up_date":null,"type":null,"pipeline_id":"23","stage_id":"89","status":null,"opportunity_info":null,"opportunity_source":null,"lead_value":null,"opportunity_description":null,"labels":["new_pa"]}} 
[2025-08-02 09:44:29] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":89,"user_id":"81"} 
[2025-08-02 09:44:29] local.ERROR: Error in lead creation {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (pa, primary, 79, 1, 2025-08-02 09:44:29, 2025-08-02 09:44:29))","trace":"#0 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into `ta...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `ta...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `ta...', Array, 'id')
#3 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3754): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `ta...', Array, 'id')
#4 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2038): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1358): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1323): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1162): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1079): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Tag))
#10 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1078): tap(Object(App\\Models\\Tag), Object(Closure))
#11 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2367): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2379): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php(436): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\LeadController->store(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#17 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LeadController), 'store')
#18 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runController()
#19 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php(65): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\XSS->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnsurePermissionsAfterPost->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\FilterRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#78 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#79 C:\\xampp\\htdocs\\omx-new-saas\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#80 C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#81 {main}"} 
[2025-08-02 09:44:29] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:54:11] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:54:12] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:54:17] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:54:18] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:55:35] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 09:55:35] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 09:56:11] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":89,"user_id":"81"} 
[2025-08-02 09:56:11] local.ERROR: Error processing tags {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Parichay AI, primary, 79, 1, 2025-08-02 09:56:11, 2025-08-02 09:56:11))","tagsInput":["new_Parichay AI"]} 
[2025-08-02 09:56:11] local.INFO: Lead saved successfully {"lead_id":22} 
[2025-08-02 09:56:11] local.INFO: UserLead relationships created successfully  
[2025-08-02 09:56:11] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 09:56:11] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 09:56:11] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 09:56:13] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 09:56:13] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:56:28] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 09:56:29] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 09:56:34] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 09:56:34] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:04:01] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:04:02] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:04:07] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:04:08] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:04:52] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 10:04:52] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 10:05:14] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":89,"user_id":"81"} 
[2025-08-02 10:05:14] local.ERROR: Error processing tags {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Parichay AI, primary, 79, 1, 2025-08-02 10:05:14, 2025-08-02 10:05:14))","tagsInput":["new_Parichay AI"]} 
[2025-08-02 10:05:14] local.INFO: Lead saved successfully {"lead_id":23,"tags":null} 
[2025-08-02 10:05:14] local.INFO: UserLead relationships created successfully  
[2025-08-02 10:05:14] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 10:05:14] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 10:05:14] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 10:05:16] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 10:05:16] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:05:33] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:05:33] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:05:37] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:05:38] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:06:37] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads/23/labels","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:06:44] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:06:45] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:06:49] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:06:50] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:11:39] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:11:39] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:11:44] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:11:45] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:13:05] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:13:05] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:13:09] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:13:10] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:14:41] local.INFO: getPipelineStages called {"pipeline_id":"23","user_id":79,"creator_id":79,"request_data":{"pipeline_id":"23"}} 
[2025-08-02 10:14:41] local.INFO: Stages query result {"pipeline_id":"23","stages_count":5,"stages":[{"stdClass":{"id":86,"name":"New","order":0}},{"stdClass":{"id":87,"name":"Qualified","order":1}},{"stdClass":{"id":88,"name":"Discussion","order":2}},{"stdClass":{"id":89,"name":"Negotiation","order":3}},{"stdClass":{"id":90,"name":"Won/Lost","order":4}}]} 
[2025-08-02 10:14:58] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Anudua Sen","email":"<EMAIL>","phone":"+918654523569","subject":null,"user_id":"81","date_of_birth":null,"next_follow_up_date":null,"type":null,"pipeline_id":"23","stage_id":"90","status":null,"opportunity_info":null,"opportunity_source":null,"lead_value":null,"opportunity_description":null,"labels":["new_Parichay da"]}} 
[2025-08-02 10:14:58] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":90,"user_id":"81"} 
[2025-08-02 10:14:58] local.ERROR: Error processing tag {"tag":"new_Parichay da","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Parichay da, primary, 79, 1, 2025-08-02 10:14:58, 2025-08-02 10:14:58))"} 
[2025-08-02 10:14:58] local.INFO: Lead saved successfully {"lead_id":24} 
[2025-08-02 10:14:58] local.INFO: UserLead relationships created successfully  
[2025-08-02 10:14:58] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 10:14:58] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 10:14:58] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 10:15:01] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 10:15:01] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:15:18] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:15:18] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:15:23] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:15:24] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:16:28] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:16:29] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:17:27] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"Maha Bir Bhadra","email":"<EMAIL>","phone":"8654985625","date_of_birth":"2025-08-01","contact_type":"Lead","tags":["95","96","98","97","93"],"postal_code":"734429","city":"DARJILING","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:17:27] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:22:02] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:22:03] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:22:52] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"mistry sen","email":"<EMAIL>","phone":"8956425359","date_of_birth":null,"contact_type":"Lead","tags":["95","93","97","91","94"],"postal_code":"734429","city":"DARJILING","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJ

naxalbari","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:22:52] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"79"} 
[2025-08-02 10:22:52] local.INFO: Assigned tags to lead {"tag_ids":["95","93","97","91","94"]} 
[2025-08-02 10:22:52] local.INFO: Lead saved successfully {"lead_id":25} 
[2025-08-02 10:22:52] local.INFO: UserLead relationships created successfully  
[2025-08-02 10:22:52] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 10:22:52] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 10:22:52] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 10:22:54] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 10:22:55] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:25:02] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/api/contacts/add-tags","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:25:15] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:25:16] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:26:27] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"mama bar","email":"mamabari","phone":"************","date_of_birth":null,"contact_type":"Lead","tags":["95","96","98","97","94","93","88","92","89"],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:26:28] local.ERROR: Lead creation validation failed {"errors":{"email":["The email must be a valid email address."]},"request_data":{"name":"mama bar","email":"mamabari","phone":"************","date_of_birth":null,"contact_type":"Lead","tags":["95","96","98","97","94","93","88","92","89"],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:26:28] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:26:45] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"mama bar","email":"<EMAIL>","phone":"************","date_of_birth":null,"contact_type":"Lead","tags":["95","96","98","97","94","93","88","92","89"],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:26:45] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"79"} 
[2025-08-02 10:26:45] local.INFO: Assigned tags to lead {"tag_ids":["95","96","98","97","94","93","88","92","89"]} 
[2025-08-02 10:26:45] local.INFO: Lead saved successfully {"lead_id":26} 
[2025-08-02 10:26:45] local.INFO: UserLead relationships created successfully  
[2025-08-02 10:26:45] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 10:26:45] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 10:26:45] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 10:26:47] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 10:26:47] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:27:24] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:27:25] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:28:37] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:28:38] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:29:23] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"My AI BOT","email":"<EMAIL>","phone":"8965231205","date_of_birth":"2025-08-01","contact_type":"Lead","tags":["95","96","98","97","94","93","89"],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734001","business_address":"Siliguri","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:29:23] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"79"} 
[2025-08-02 10:29:23] local.INFO: Assigned tags to lead {"tag_ids":["95","96","98","97","94","93","89"]} 
[2025-08-02 10:29:23] local.INFO: Lead saved successfully {"lead_id":27} 
[2025-08-02 10:29:23] local.INFO: UserLead relationships created successfully  
[2025-08-02 10:29:23] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 10:29:23] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 10:29:23] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 10:29:25] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 10:29:25] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:29:59] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/api/contacts/add-tags","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:30:17] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:30:18] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:30:36] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:30:37] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:31:27] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"miona dlj","email":"<EMAIL>","phone":"8965421025","date_of_birth":"2025-07-30","contact_type":"Lead","tags":["92","88","89","93","94"],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJ

naxalbari","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:31:27] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"79"} 
[2025-08-02 10:31:27] local.INFO: Assigned tags to lead {"tag_ids":["92","88","89","93","94"]} 
[2025-08-02 10:31:27] local.INFO: Lead saved successfully {"lead_id":28} 
[2025-08-02 10:31:27] local.INFO: UserLead relationships created successfully  
[2025-08-02 10:31:27] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 10:31:27] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 10:31:27] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 10:31:29] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 10:31:29] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:31:46] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:31:47] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:32:05] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:32:05] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:32:09] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:32:10] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:32:48] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:32:49] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:34:18] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:34:20] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:38:29] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:38:30] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:38:47] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:38:48] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:39:55] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:39:56] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:40:42] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"demo one","email":"<EMAIL>","phone":"8956420125","date_of_birth":"2025-08-01","contact_type":"Lead","tags":["93"],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:40:42] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:45:20] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:45:22] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:46:27] local.INFO: Lead creation started {"user_id":79,"request_data":{"_token":"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL","name":"demo my ai","email":"<EMAIL>","phone":"8654215890","date_of_birth":"2025-07-30","contact_type":"Lead","tags":["93"],"postal_code":"734429","city":"Siliguri","state":"West Bengal","country":"India","business_name":"Smart Internz","business_gst":"Smart Internz","business_state":"West Bengal","business_postal_code":"734429","business_address":"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJ

naxalbari","user_id":"79","subject":"New Contact","pipeline_id":null,"stage_id":null,"dnd_settings":"{\"all\":false,\"emails\":false,\"whatsapp\":false,\"sms\":false,\"calls\":false}"}} 
[2025-08-02 10:46:27] local.INFO: Creating lead with data {"pipeline_id":23,"stage_id":86,"user_id":"79"} 
[2025-08-02 10:46:27] local.INFO: Assigned tags to lead {"tag_ids":["93"]} 
[2025-08-02 10:46:27] local.INFO: Lead saved successfully {"lead_id":29} 
[2025-08-02 10:46:27] local.INFO: UserLead relationships created successfully  
[2025-08-02 10:46:27] local.INFO: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created  
[2025-08-02 10:46:27] local.INFO: Found 1 enabled integrations for webhook action: crm.lead_created  
[2025-08-02 10:46:27] local.INFO: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/  
[2025-08-02 10:46:29] local.INFO: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created  
[2025-08-02 10:46:29] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/leads","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:46:44] local.INFO: Filter users request {"user_id":79,"user_type":"company","user_created_by":7} 
[2025-08-02 10:46:44] local.INFO: Fetching users for filter {"current_user_id":79,"current_user_type":"company","creator_id":79} 
[2025-08-02 10:46:50] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:46:51] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:47:05] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-08-02 10:47:08] local.INFO: Permissions refreshed after POST request {"user_id":79,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
