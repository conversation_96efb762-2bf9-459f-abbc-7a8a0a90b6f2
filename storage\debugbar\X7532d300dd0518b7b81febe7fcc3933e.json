{"__meta": {"id": "X7532d300dd0518b7b81febe7fcc3933e", "datetime": "2025-08-02 10:25:12", "utime": **********.727241, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130311.373126, "end": **********.727293, "duration": 1.3541669845581055, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1754130311.373126, "relative_start": 0, "end": **********.626852, "relative_end": **********.626852, "duration": 1.2537260055541992, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.62687, "relative_start": 1.****************, "end": **********.727296, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "AYXfLP5Xdz2CcR379tR8kxZTs2FAO5lG1RBgVQnK", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-982655033 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-982655033\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-308877446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-308877446\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1729435844 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1729435844\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-755209081 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755209081\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1152211198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1152211198\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-518489135 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:25:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InB2bW1ZbTFGZ0RMOXpwdDlSRVJqUUE9PSIsInZhbHVlIjoicWwzTzJncU5HbXptdzUxaDgvV1UwUzZPUUVBYldJV1l5MUJjbkJrVkw2eHBZdEdMWVNuQ2lSWU1teE93VWY3VVZzSndQWFFoWkR3WXZGV0RsRXFzODJEVERaQmZYZUR1TDBkYjBMSTZGQVRMOVNOL05Kei9IWmpPWE02cEJZUUwzK25FMW44SWlBMmlrSlAzazBrc2VkcDVZL084RTU4NnJZWVBLZnNuWHJEeVRHOUMrSU1KSjdEMEJXOGMyTnBjTW9sOVVtOEorTTJVZnlCUmZmUjZYaEFnRFBnZzdQT1VHZC9hZUpxR1RUKzE5cTcwVkpVdUNGZllJV3BIck1PK1R1TTZqMUdTaDlqZjdocXpKMFp5ZWkrSkJ3VWFNWktNejZSdGcvaWY2bHg5Rk9CenQxclZUcGlLdTFma2NRUThZcHRhbCtxNzJJZC9yVjNwMTZsOU11d0lnaTNlREttbDl1ZnBLbWxiSktqdHg4WFlRdjJ3cjRMUjJCQ2Z6NUEwMWY5Z0dGRDhqb2czQjdhUk9tNENOejZPMVNqZDJUYkl2M2xobG44K3FHaWF6M3JOVWhNdDZWcmRtcW1TZCtQU28xYVVXK1I5blhmeVJBY2hYM29hYlVud3BiN3QvdWEyL3NPQnJIREdLZktudHhUUEhIalJ3TkxYTG1KdTVsNEUiLCJtYWMiOiJhNzI0MmMwNDczMDYyMmUyMDNmZDJiOGY4NDcyYjc5MDczMGM0YjE2ZGUzMTI4YzA3NWFjNTMyMGZjYTY4ZGM0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:25:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Img0M0QwV0dWZ0syOGR5b0J1NDNPMkE9PSIsInZhbHVlIjoiVUtoaDZpeVRDekVZZVVXOVl6SWV6Zjd1dHVmZzhhbnZQNWMvQWRIbHA3TmhROHJORS9qanFyei9WUktpZ3N4NjMvMUw5d01pT3pEUFhOYVkvelM2aXRPcDJlTmYwTlR1U3ZNVU9WM25HM1BHNml4UU9UWEg5UDU5SkRxckdpWTJkMVgzS2U3bkl2bWp0bXJrWDZEVCtrWFd3RGZYc3JiMlgxQTdXTEFZY3RIUjhZc1JaMUdlWHI3SE0veVowajhaYWpsbklKTHhtOXRnNHU5em5RZTc1b3diVVF0ckIwamN3Q1RjSjFpazBwOEN2RFJuRC90UHpSVEZDTXlvMnhxY0tlRzdMcDdaNXdVWkJPYmMvcnJTZ1hwTEhhNE16cys2S2FPeWdxODRWWmw5K1grRi9Kc2xRSDZmZnBOb3FNU2JIVFN2b0V3Mkt3cG0yeVJubllEeDYwa2plZk8rL1BjYnkxYm5JN1ZFZitYQ21DK05kVmUySnN4UGQ0WjNzM1ExYWV4WlRxYW8wcXo5MFhLUjlVVTFOOUpTYjJKR2RQdS9XcDJ6ei9ycytmMTNtV3FFZ1kyMDhsc1k1R2FmY0pRdzFUQWZLY05EU01jbjhZTUpaZ0YyajNTMmdqcnpxTXhjTjNRWDAvdFE1aWpTQVlXKzljdUYzZWZQT3dyLzRmN2MiLCJtYWMiOiI3OGIxNWFmOTI1ZjRjOTY3ZmRiMDdiOWU4MjA0MGQ5YzMyNjg5NGJkZDJiMzU5OWY4OWRkMjI2YTM3ZmU3ZThmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:25:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InB2bW1ZbTFGZ0RMOXpwdDlSRVJqUUE9PSIsInZhbHVlIjoicWwzTzJncU5HbXptdzUxaDgvV1UwUzZPUUVBYldJV1l5MUJjbkJrVkw2eHBZdEdMWVNuQ2lSWU1teE93VWY3VVZzSndQWFFoWkR3WXZGV0RsRXFzODJEVERaQmZYZUR1TDBkYjBMSTZGQVRMOVNOL05Kei9IWmpPWE02cEJZUUwzK25FMW44SWlBMmlrSlAzazBrc2VkcDVZL084RTU4NnJZWVBLZnNuWHJEeVRHOUMrSU1KSjdEMEJXOGMyTnBjTW9sOVVtOEorTTJVZnlCUmZmUjZYaEFnRFBnZzdQT1VHZC9hZUpxR1RUKzE5cTcwVkpVdUNGZllJV3BIck1PK1R1TTZqMUdTaDlqZjdocXpKMFp5ZWkrSkJ3VWFNWktNejZSdGcvaWY2bHg5Rk9CenQxclZUcGlLdTFma2NRUThZcHRhbCtxNzJJZC9yVjNwMTZsOU11d0lnaTNlREttbDl1ZnBLbWxiSktqdHg4WFlRdjJ3cjRMUjJCQ2Z6NUEwMWY5Z0dGRDhqb2czQjdhUk9tNENOejZPMVNqZDJUYkl2M2xobG44K3FHaWF6M3JOVWhNdDZWcmRtcW1TZCtQU28xYVVXK1I5blhmeVJBY2hYM29hYlVud3BiN3QvdWEyL3NPQnJIREdLZktudHhUUEhIalJ3TkxYTG1KdTVsNEUiLCJtYWMiOiJhNzI0MmMwNDczMDYyMmUyMDNmZDJiOGY4NDcyYjc5MDczMGM0YjE2ZGUzMTI4YzA3NWFjNTMyMGZjYTY4ZGM0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:25:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Img0M0QwV0dWZ0syOGR5b0J1NDNPMkE9PSIsInZhbHVlIjoiVUtoaDZpeVRDekVZZVVXOVl6SWV6Zjd1dHVmZzhhbnZQNWMvQWRIbHA3TmhROHJORS9qanFyei9WUktpZ3N4NjMvMUw5d01pT3pEUFhOYVkvelM2aXRPcDJlTmYwTlR1U3ZNVU9WM25HM1BHNml4UU9UWEg5UDU5SkRxckdpWTJkMVgzS2U3bkl2bWp0bXJrWDZEVCtrWFd3RGZYc3JiMlgxQTdXTEFZY3RIUjhZc1JaMUdlWHI3SE0veVowajhaYWpsbklKTHhtOXRnNHU5em5RZTc1b3diVVF0ckIwamN3Q1RjSjFpazBwOEN2RFJuRC90UHpSVEZDTXlvMnhxY0tlRzdMcDdaNXdVWkJPYmMvcnJTZ1hwTEhhNE16cys2S2FPeWdxODRWWmw5K1grRi9Kc2xRSDZmZnBOb3FNU2JIVFN2b0V3Mkt3cG0yeVJubllEeDYwa2plZk8rL1BjYnkxYm5JN1ZFZitYQ21DK05kVmUySnN4UGQ0WjNzM1ExYWV4WlRxYW8wcXo5MFhLUjlVVTFOOUpTYjJKR2RQdS9XcDJ6ei9ycytmMTNtV3FFZ1kyMDhsc1k1R2FmY0pRdzFUQWZLY05EU01jbjhZTUpaZ0YyajNTMmdqcnpxTXhjTjNRWDAvdFE1aWpTQVlXKzljdUYzZWZQT3dyLzRmN2MiLCJtYWMiOiI3OGIxNWFmOTI1ZjRjOTY3ZmRiMDdiOWU4MjA0MGQ5YzMyNjg5NGJkZDJiMzU5OWY4OWRkMjI2YTM3ZmU3ZThmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:25:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518489135\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1849230611 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AYXfLP5Xdz2CcR379tR8kxZTs2FAO5lG1RBgVQnK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849230611\", {\"maxDepth\":0})</script>\n"}}