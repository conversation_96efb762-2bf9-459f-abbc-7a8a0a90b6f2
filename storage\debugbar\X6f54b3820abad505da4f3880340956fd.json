{"__meta": {"id": "X6f54b3820abad505da4f3880340956fd", "datetime": "2025-08-02 10:21:58", "utime": **********.691316, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130117.569209, "end": **********.691353, "duration": 1.1221439838409424, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1754130117.569209, "relative_start": 0, "end": **********.562661, "relative_end": **********.562661, "duration": 0.9934518337249756, "duration_str": "993ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.562691, "relative_start": 0.****************, "end": **********.691356, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7khavvui3rPbIRJnaqchGWp93Ehv16RWnHAIg0Jr", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-136339598 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-136339598\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-251031076 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-251031076\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-528406122 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-528406122\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-505139284 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505139284\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1232695907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1232695907\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-954525462 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:21:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhEb1VnT3l2NWZ0SDU3Zllyc0k1TGc9PSIsInZhbHVlIjoiS0lrMjdWM0hvWG1Rc1hpR2ZWOU5jQzc3UWpENXl0T1d0VWQ1VlZYcTg1TWFGVTh5VllzdW81ZCtrNWJ3RjhCZGgwWENObDR5SXcwZHZIVk5IQmZ1VFdhYkVXUlNzY3NReG1xU20wbEFlZHFyeVFPTUQ0RVZpUFBBTDVWcXJMTWpKcGdML3Ztc25RNG5XQ1dlanB3QThEcHJ4YVhGN3JxbUI5b2w0eHRYOWhCc015RmlMdVpHT0ZMNDg3b3VCang0OGVqTHluV0JpUEZrN3FPd0lHb1kwZjFJZStUQ0dkWVFJaktuN0IzZVpFWTh0RWpVRWxTc0dYUWo0SlBmelp5U09MRDE5VDdTN0hoTTZUc0tRWVk3Mms5WkpONDR4dDNVdVgxOC9tQTBsTEhsRjB0UXdVN2lUSmpmd1VQdXFvMXBaMHNaTVhEU1ZERkN3ZXkxQ1RaeFdaYWtYOXlaeFlZR0RkY0h5RGE0Q29GMEdDY09hOGtKbGhzTElYYjAxV0xVYURNWE1LRGtqakZuTU1lWVR3TmdCQ2cyaWNhMjh3R2RjQndsanFXWHYzaXNIeWZIUDFWTERVNXh0QmNEL2RGZ0lBZUZqbllaMkhKaWp3bmlIeWVuTllTWFpTWmpmNy9ORHNhWG56eGNWSFVpVkFCR1hlOCtUSlpXSWN0L29CRlciLCJtYWMiOiJjYmRlM2ViMWVkNTkyM2EzYmE5ZWU4ZGZlOTYzNmMyOGY1ZTdmM2IzYjJlMDM3ZjljNzI1OGVkMTM1MGNhMzQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:21:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjMva2lIa0Z1c2RwTm1CeGdrN25SNVE9PSIsInZhbHVlIjoiTEFlTy95U1ltaHNhWTVVNVFBYk9vOVFTVTZTbjNXZTdmSXREVlZkN3NPTUJLTzFVdDJlTjJNb0UvN2p6WHVCNXRGYkRNM0MrT2xXSUQ0MThUNCtPYjNhU09EeVFSc1FRU0lveW5HaEh2YjY2Tjk0VEFpSU5BSFRHemtsR3Z0TDNmMDh2WWJ2WU82dmpNa2ZubjI5RXZZbEErck56NXFtckVMbDdLdkhSM2tIMExyTjA4N0N5dlkvRXRKb2pPR3RwcER1TW5wcWVuSUFiOUo4ZDhYdkl2NThpQzVQZlExd1hBajg0YkVoQWpsYlp6VGlpRW5JZ2hqYk1LTzk2bmtqM2tnSnNja2hWKzEyNmhacFRsb1l0MlJJRUlCeVhSMUg2dk1pbksxMDJRclBZUkNGZU9pQU9uc2EzQ1RqL1l2QVN1aUlaS0J0TEIxQ09XNzkzTG9LN1pYRlBzemh6UTBDTUlXL2JRb3lHMGRiamR2RHA1bWN5VkJ3ZE5ZalBMcllqc3BMZVlaUTkrbENxTjB4cGNBZFhQdldob2I2Mkt3c0hKazJwUnhGVVpaemtsVXlBeEFMdzNISnhuVmRRZlE5TXJjTHZxdjdZMUFOYkIwMkd4TWdXKy95cUN0SGsvUk9VTHdCMDM5LzFxTGZRRzh4VEZvOFZETkMvMUZoMDVRN2YiLCJtYWMiOiIxOTE5OWFlMDM5ZmE0ODU4MzQ3M2I2NjlhMDI3NzI0MTM5M2EwOGNhODBkZjljZTIxNWUyZjViZGQxNjAzODkyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:21:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhEb1VnT3l2NWZ0SDU3Zllyc0k1TGc9PSIsInZhbHVlIjoiS0lrMjdWM0hvWG1Rc1hpR2ZWOU5jQzc3UWpENXl0T1d0VWQ1VlZYcTg1TWFGVTh5VllzdW81ZCtrNWJ3RjhCZGgwWENObDR5SXcwZHZIVk5IQmZ1VFdhYkVXUlNzY3NReG1xU20wbEFlZHFyeVFPTUQ0RVZpUFBBTDVWcXJMTWpKcGdML3Ztc25RNG5XQ1dlanB3QThEcHJ4YVhGN3JxbUI5b2w0eHRYOWhCc015RmlMdVpHT0ZMNDg3b3VCang0OGVqTHluV0JpUEZrN3FPd0lHb1kwZjFJZStUQ0dkWVFJaktuN0IzZVpFWTh0RWpVRWxTc0dYUWo0SlBmelp5U09MRDE5VDdTN0hoTTZUc0tRWVk3Mms5WkpONDR4dDNVdVgxOC9tQTBsTEhsRjB0UXdVN2lUSmpmd1VQdXFvMXBaMHNaTVhEU1ZERkN3ZXkxQ1RaeFdaYWtYOXlaeFlZR0RkY0h5RGE0Q29GMEdDY09hOGtKbGhzTElYYjAxV0xVYURNWE1LRGtqakZuTU1lWVR3TmdCQ2cyaWNhMjh3R2RjQndsanFXWHYzaXNIeWZIUDFWTERVNXh0QmNEL2RGZ0lBZUZqbllaMkhKaWp3bmlIeWVuTllTWFpTWmpmNy9ORHNhWG56eGNWSFVpVkFCR1hlOCtUSlpXSWN0L29CRlciLCJtYWMiOiJjYmRlM2ViMWVkNTkyM2EzYmE5ZWU4ZGZlOTYzNmMyOGY1ZTdmM2IzYjJlMDM3ZjljNzI1OGVkMTM1MGNhMzQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:21:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjMva2lIa0Z1c2RwTm1CeGdrN25SNVE9PSIsInZhbHVlIjoiTEFlTy95U1ltaHNhWTVVNVFBYk9vOVFTVTZTbjNXZTdmSXREVlZkN3NPTUJLTzFVdDJlTjJNb0UvN2p6WHVCNXRGYkRNM0MrT2xXSUQ0MThUNCtPYjNhU09EeVFSc1FRU0lveW5HaEh2YjY2Tjk0VEFpSU5BSFRHemtsR3Z0TDNmMDh2WWJ2WU82dmpNa2ZubjI5RXZZbEErck56NXFtckVMbDdLdkhSM2tIMExyTjA4N0N5dlkvRXRKb2pPR3RwcER1TW5wcWVuSUFiOUo4ZDhYdkl2NThpQzVQZlExd1hBajg0YkVoQWpsYlp6VGlpRW5JZ2hqYk1LTzk2bmtqM2tnSnNja2hWKzEyNmhacFRsb1l0MlJJRUlCeVhSMUg2dk1pbksxMDJRclBZUkNGZU9pQU9uc2EzQ1RqL1l2QVN1aUlaS0J0TEIxQ09XNzkzTG9LN1pYRlBzemh6UTBDTUlXL2JRb3lHMGRiamR2RHA1bWN5VkJ3ZE5ZalBMcllqc3BMZVlaUTkrbENxTjB4cGNBZFhQdldob2I2Mkt3c0hKazJwUnhGVVpaemtsVXlBeEFMdzNISnhuVmRRZlE5TXJjTHZxdjdZMUFOYkIwMkd4TWdXKy95cUN0SGsvUk9VTHdCMDM5LzFxTGZRRzh4VEZvOFZETkMvMUZoMDVRN2YiLCJtYWMiOiIxOTE5OWFlMDM5ZmE0ODU4MzQ3M2I2NjlhMDI3NzI0MTM5M2EwOGNhODBkZjljZTIxNWUyZjViZGQxNjAzODkyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:21:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954525462\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1101007009 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7khavvui3rPbIRJnaqchGWp93Ehv16RWnHAIg0Jr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101007009\", {\"maxDepth\":0})</script>\n"}}