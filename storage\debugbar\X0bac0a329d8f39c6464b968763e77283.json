{"__meta": {"id": "X0bac0a329d8f39c6464b968763e77283", "datetime": "2025-08-02 08:23:16", "utime": **********.139925, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[08:23:16] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.112364, "xdebug_link": null, "collector": "log"}, {"message": "[08:23:16] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.120283, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754122993.658155, "end": **********.139979, "duration": 2.4818239212036133, "duration_str": "2.48s", "measures": [{"label": "Booting", "start": 1754122993.658155, "relative_start": 0, "end": 1754122995.909986, "relative_end": 1754122995.909986, "duration": 2.2518310546875, "duration_str": "2.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754122995.910086, "relative_start": 2.2519309520721436, "end": **********.139984, "relative_end": 5.0067901611328125e-06, "duration": 0.22989797592163086, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46530544, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3021\" onclick=\"\">app/Http/Controllers/LeadController.php:3021-3077</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00682, "accumulated_duration_str": "6.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.068663, "duration": 0.00511, "duration_str": "5.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 74.927}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.102069, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 74.927, "width_percent": 13.05}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3048}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.112914, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:3048", "source": "app/Http/Controllers/LeadController.php:3048", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3048", "ajax": false, "filename": "LeadController.php", "line": "3048"}, "connection": "radhe_same", "start_percent": 87.977, "width_percent": 12.023}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-2009167843 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2009167843\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1198394647 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198394647\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-921933467 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-921933467\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1542565690 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9vRjhYN2pSYWVXTTlML1BWd1NzMmc9PSIsInZhbHVlIjoickU0bHRYMzVuc1JEUDBDWkpwR0UzQ2NreGNaelBFRGp3SStUZU1HdGpIL3VwTVVmNXJ2Wk5vRHB6Qmx4NmhVdDlINkk2K1crV1FpOCtSS3U3QzR6K3dMV0RWS3dxTEZZemZnaEJUWGxLWVI5VitDN1lkVGhsWjZBVkxGMFJPNDdQaHRnenUzV092T05HYzRjWUduTmVUbTRrL2Q4Z3p1V0hnck9JQlUxdnpEb0p6L25CeFQ3eURLRml4Sm9tUTg2M1JFN0lLTHFqSFJXbW1rY3V0OFBRQTNRR2lIdWZpbGdmQzR6aVg2UmduaXZrbHVkVVlNcUpEMm5LY1VuTnpLTWtuMkM2RDlaTUlJOHV2Q2k3b1JBbkVLSExoYmFCRElaNGc1YmFOakp5aG0rUE1xbXR1bjkxMHZRYnBhMGlLclNGeklNU0Y3eUNtQXhvYkNObHRPbnRrdnlnb3NmdFNnVnZNOXhyUVNUaUE4U3kwaWU2SmtBNWxGYUhsd2pjTEJoYnBLNnlPK1F0cXFqQ1YyQnBscTJmUDgwZDdPbHdCazd5YVRNZHJoV0FZMUU1VEpXOGY5TytxU2pQa3NHRkl6VXpsdVFFVlZ1TTZzTFVFUTZUdldFQ2pXTjU1Z013byt3dkQ4OVZjdEVjUWhKb3l0aTQwSzBzVEtLWUdDYXErdEMiLCJtYWMiOiIzN2M3ZmE5NmUzMjBiZTdjODA1MzgxYTMxZWJjNDg3MWExODRmYTE3NzUzNzc1MTRjMWUwNGUwNjljOTVhMTRkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ilh6VlhvcXB3c2NCdlluWGhmQUEyUFE9PSIsInZhbHVlIjoiU0R6Y1U3R3VadTlnVnd5TFhoS29YUFdVZ2lwQmdadytBLzJxNy90THdEckoxT2phNS9XRTFzU0dsWXJKcUZZQXRaS3V0NXZFWVV0YlFOQ05ydHlSQ3pIbEQ1MVZETlJWOGoyL051eUp1V1MvQ3IvMk1GQ2xxallVd0czNjlWZzJYYmN0VThLZUg0cVVaSnJUMExILytyUERBbUE4Y2JQNmFDeVh0SVdkTitQSTBQR09Dd1Y1bGE4WlJPQk93dWZlL1ZqVDJSUmsxVkdrT0N3clpqbDcxN3JRdEpRS1hVaVB0cStMV1ZzY1hXUmQzUWFneEpTSG5QSm5CWkJuYWdXWUhBMmxLcDI3QmlLRyswK1lOOEtSOG5mS21HNzMxL2NabnYvYWlpVHVkZXBGY1pNdzRrczA5QmVDRmhaRnlGaklTSGYxWHJTOG5sbVFjbU05eEFFRXNwTU1pU2U2aE5DcWwzclZCT0tIZE1haklZWFl5S1NnVDNIcVk1YmJtd0E5THlFZXM1dm9oTVc4WW9lSXNmb3RTcU1xT3FnVmRuTjJhMkFWbkQwREFua0kxeFoxMWFGb0hwS3ExdXJFTVl1bkhHU1JzZkQ1ZFdZUnVncW9HdUNlQ0JWS3FmUEZmV0UvTFdLc1Z0WnRKSDZqemJ6TGpRZ0svaDRHMVZVVHNJNngiLCJtYWMiOiJiMzQwMGMxNGY3YzQxNWU0YzQxY2U4OWIwOTk5MzFkMjY2NzcwZTVmMDQxZGE5ZDM1MzIyNWJhY2U4NmU0ZWVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542565690\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-287236568 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287236568\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-780795118 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:23:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVrd2treXQxU0RYRU81VmJoSm4rb3c9PSIsInZhbHVlIjoiS3pyMWhXbUtIVVBEbzlMNG9aMFNUSTc4UUR1YXRpZ1YzVXFSbEtzYU0xa216ZXVicW9vWnBZaHBBV1VKbkJsTHJWUS9ETFlselArdnJNcjhOZjBWZ2RkUThCSDc1MG0xUHdxRSs1SEFhRGloNUgvaU8rN25rcmdHSm5Da01yMGw5b1BLSnlkWmw2K3BEOU9XZ2VoVFNONmJDanFyWi9EeVFmMmpWd1V5QXRLK0NuM1ZNVCtOK3lmVUhhMVMwbmlEZWwzVXJuVU9kWUJzbWRiYmt5UEczMkZLL04vREtubVZUdERXaTNaUWV6T1Z6eGJJU0JkV0MvenZNNXMrMUpTcmV0VDg1c2NmRmRvRGpnZlZ5RlBPZDByN3d5dzFtN2prSkdKY0E1dTU3dHpabTdiZWlEM3czR1pubXdvNlRWajZQK01rbEtJN3ArQlJjUTZLamc3WVVmUTJSdmNFQnZqSUdyMWVYaWlOb0dwbjBJOUt1aDV1eU9veWlaQ3JaYkFtdzhHQ29SUVhUNDBlMzFVV3crcUhqd2RNYTROVHBSVWdkQ3l0UGZwRE5NSTVNblVNYmpraHJWVTA4a2tmMFBVWW4vMEV0MkpnMGxTelRuZUU5d2RrMmJOTEc2WjFOcDVKQ3hBemUvRkpPcUxIU3FrRSt6Z1NyYmlyZ1NqS2Y3cWsiLCJtYWMiOiJiMTI3MTZhNWY3ZDE0NzU3NGVhY2E5ZTc4OWE2MGUzZDgzMzU2NjlkNTQwYThkZTNmYmNkNzVlNzk2ZWE0MzY4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:23:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ing0bGU3Nmp0emh4dERsTDF5RXFUNlE9PSIsInZhbHVlIjoiWTkrM3ZMREMyNHloT0hCZjZPNm8yNTRUZ2FtM3lFeG5SelM2cWF2SGJ2d0Y4Y2g5N2dTMitqRFZPM2NSenhON1poaFM5SWE2YW9wZjFhN3dIWUVRQU9FTGEvU3FVS3BaN1dNU0JSNnJmYlFIbEw5UjBKT1c3VktUeGVRWCtGVDdWazhKTDVVdWx6bFEvWmpEd3B0eWhMRVQ1S3VNTmZ3NmRrMEFWQVRjRHdNR2tocFJKYUQxV0hFVWp6T2ZmWk8yNlE3NXZKRG92WTBwOTdVZSsvV1lEbll0WjlIZWRWU2RGSmtDK1N3YldKTVJ5cThaNDR3bzRCVU5icEcxSUtBenZXbmRVS0paQkdSd3liQWF3bFQxd1IrbUsyeUliWkgyT3NrVEhiZ256MlN2K2Z5TmRjM0N5V1BDN0ZKUGczQWpqMzR2b2tEd2c0dGdwdiswS1RjbzZHWWpscnJaQ28yajZaZUhDVUhtN293cE5zWk93SjF1c3FQQnErc2tjc3hTd2prZlhaTGpYbXJYMGFra1VHVlkyNlc4NXNlQjZuVFBnSTBKZ3U4dXM4TUxTTTllNVpsWkE0RUtQS1pabDB6ZGkxWXJlQlBPWVBmcUdjWEVzQmxhV0JZbUhnTldjL3NRc0M5NlV6a3pFOFVuUlppU1hGWlkvS0FETUx6bk9qTC8iLCJtYWMiOiIzZjg2ZmJhNjBhYTFmMmE0ZWEyYjQwYjEyZTlmNWYzNTEzNzU5NzExZjA3Y2MyYzVjZjY4MWY1YmQ0NDRjNjNlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:23:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVrd2treXQxU0RYRU81VmJoSm4rb3c9PSIsInZhbHVlIjoiS3pyMWhXbUtIVVBEbzlMNG9aMFNUSTc4UUR1YXRpZ1YzVXFSbEtzYU0xa216ZXVicW9vWnBZaHBBV1VKbkJsTHJWUS9ETFlselArdnJNcjhOZjBWZ2RkUThCSDc1MG0xUHdxRSs1SEFhRGloNUgvaU8rN25rcmdHSm5Da01yMGw5b1BLSnlkWmw2K3BEOU9XZ2VoVFNONmJDanFyWi9EeVFmMmpWd1V5QXRLK0NuM1ZNVCtOK3lmVUhhMVMwbmlEZWwzVXJuVU9kWUJzbWRiYmt5UEczMkZLL04vREtubVZUdERXaTNaUWV6T1Z6eGJJU0JkV0MvenZNNXMrMUpTcmV0VDg1c2NmRmRvRGpnZlZ5RlBPZDByN3d5dzFtN2prSkdKY0E1dTU3dHpabTdiZWlEM3czR1pubXdvNlRWajZQK01rbEtJN3ArQlJjUTZLamc3WVVmUTJSdmNFQnZqSUdyMWVYaWlOb0dwbjBJOUt1aDV1eU9veWlaQ3JaYkFtdzhHQ29SUVhUNDBlMzFVV3crcUhqd2RNYTROVHBSVWdkQ3l0UGZwRE5NSTVNblVNYmpraHJWVTA4a2tmMFBVWW4vMEV0MkpnMGxTelRuZUU5d2RrMmJOTEc2WjFOcDVKQ3hBemUvRkpPcUxIU3FrRSt6Z1NyYmlyZ1NqS2Y3cWsiLCJtYWMiOiJiMTI3MTZhNWY3ZDE0NzU3NGVhY2E5ZTc4OWE2MGUzZDgzMzU2NjlkNTQwYThkZTNmYmNkNzVlNzk2ZWE0MzY4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:23:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ing0bGU3Nmp0emh4dERsTDF5RXFUNlE9PSIsInZhbHVlIjoiWTkrM3ZMREMyNHloT0hCZjZPNm8yNTRUZ2FtM3lFeG5SelM2cWF2SGJ2d0Y4Y2g5N2dTMitqRFZPM2NSenhON1poaFM5SWE2YW9wZjFhN3dIWUVRQU9FTGEvU3FVS3BaN1dNU0JSNnJmYlFIbEw5UjBKT1c3VktUeGVRWCtGVDdWazhKTDVVdWx6bFEvWmpEd3B0eWhMRVQ1S3VNTmZ3NmRrMEFWQVRjRHdNR2tocFJKYUQxV0hFVWp6T2ZmWk8yNlE3NXZKRG92WTBwOTdVZSsvV1lEbll0WjlIZWRWU2RGSmtDK1N3YldKTVJ5cThaNDR3bzRCVU5icEcxSUtBenZXbmRVS0paQkdSd3liQWF3bFQxd1IrbUsyeUliWkgyT3NrVEhiZ256MlN2K2Z5TmRjM0N5V1BDN0ZKUGczQWpqMzR2b2tEd2c0dGdwdiswS1RjbzZHWWpscnJaQ28yajZaZUhDVUhtN293cE5zWk93SjF1c3FQQnErc2tjc3hTd2prZlhaTGpYbXJYMGFra1VHVlkyNlc4NXNlQjZuVFBnSTBKZ3U4dXM4TUxTTTllNVpsWkE0RUtQS1pabDB6ZGkxWXJlQlBPWVBmcUdjWEVzQmxhV0JZbUhnTldjL3NRc0M5NlV6a3pFOFVuUlppU1hGWlkvS0FETUx6bk9qTC8iLCJtYWMiOiIzZjg2ZmJhNjBhYTFmMmE0ZWEyYjQwYjEyZTlmNWYzNTEzNzU5NzExZjA3Y2MyYzVjZjY4MWY1YmQ0NDRjNjNlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:23:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780795118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-844945003 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844945003\", {\"maxDepth\":0})</script>\n"}}