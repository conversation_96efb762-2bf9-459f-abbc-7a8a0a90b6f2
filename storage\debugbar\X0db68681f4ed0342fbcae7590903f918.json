{"__meta": {"id": "X0db68681f4ed0342fbcae7590903f918", "datetime": "2025-08-02 08:20:57", "utime": 1754122857.068399, "method": "POST", "uri": "/leads/json", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[08:20:57] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\\/json\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1754122857.05457, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754122855.644324, "end": 1754122857.06845, "duration": 1.4241259098052979, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1754122855.644324, "relative_start": 0, "end": **********.677649, "relative_end": **********.677649, "duration": 1.033324956893921, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.677666, "relative_start": 1.033341884613037, "end": 1754122857.068454, "relative_end": 4.0531158447265625e-06, "duration": 0.39078807830810547, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47727768, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads/json", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\LeadController@json", "namespace": null, "prefix": "", "where": [], "as": "leads.json", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=969\" onclick=\"\">app/Http/Controllers/LeadController.php:969-985</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02611, "accumulated_duration_str": "26.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8095832, "duration": 0.01047, "duration_str": "10.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 40.1}, {"sql": "select * from `lead_stages` where `pipeline_id` = '23'", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 977}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.838356, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:977", "source": "app/Http/Controllers/LeadController.php:977", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=977", "ajax": false, "filename": "LeadController.php", "line": "977"}, "connection": "radhe_same", "start_percent": 40.1, "width_percent": 3.524}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.8767421, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 43.623, "width_percent": 10.034}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9135718, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 53.658, "width_percent": 5.63}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.931237, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 59.288, "width_percent": 15.32}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.948364, "duration": 0.00663, "duration_str": "6.63ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 74.607, "width_percent": 25.393}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 555, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/json", "status_code": "<pre class=sf-dump id=sf-dump-1019569839 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1019569839\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1242746541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1242746541\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1198343571 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198343571\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-996983659 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhlYjV3dXo2Q0VLcXhsRU9MZ3FVYlE9PSIsInZhbHVlIjoibTdwNWdycjIwY1J0eTlyZTdkZmVPdXNQaG4zRkRyOGNJM0pqUXpvcXE1WlE5UkR1QU5BU0pTZkNUQlRIazNQL1dFK1dRR2FNYTd4NFhsQzFkTG1uMVpmR1lYY3U5Nkc5eDZLcUlrWTRvNGZxamFacW9vYkR6eGJxVlNMTWx0OWxRdGpJVmQwcTFFam9tY09NUmhkVS9GWjZPU2JaVU94Q3hJUWxPNFJwS0dkSm9VbjJrbnRYbjkvcERtN1JKTXBxYXkzVkVueWpPTHNudldzY2xZbjlTUEV3VzJ0K3NCL3RDMmNHUC9VZEFjNTdPeTl0dEJMTFpVRGpmRUJ6SkNVdzVRV3IwSU1LV2MxYlVpN3o0cm1WQytNdTZpUnd0dHIzTCtjYXUyWlc2TGVNeUNHbFBPaUVvSDloY01uek15bzRhako4UVJiWWZuNngvU1JObk5kcDcydWhlOS9WOTdtVWxSeDZLSUc0N2R3Rkg0cU0zSXd6TUc0OFdvK2VGTUh5OTU5L2N5eEVRQllYU1U0UW5JSGQ3ZXEreU56eXpzMjNnU1d2OW8zVGZoYWEvZGVEK3BmVlI5RmN2MjZuUmJHVGxPWmZVcFNDT0J0cGdaQkxHVjVpclpJdUNtVDloOGdjRGJtd0lHNHRLYWlEUGc5YmsyWUJLVmhvc0ZoZ1BYMjkiLCJtYWMiOiJkMmZlYTRlMTY4OGRlZmZlMWJmYTdhYTUxOWVkOGQyZTgwZmRkNmYxNjRlMDQ4MmZmZTIyMzMxZWMwMThjMzRmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlUyR01FV25GUkF0Y3crTWdIY3NweGc9PSIsInZhbHVlIjoiMFFxVU5xRWxFSFBuK3N6dm5zTXAvTUZJS2NsMHFXbVlyRTJWQ0g3a3BwTnlsdWNiZ0ROb2dBRlNubmNXaFEwUWRra3Z5eGVadnpvQ0RyVUQydXgwRnZiTFBvOCtSWTM5ZmtjdEI3U1lMNGJEMVJTK1BNc244R3N0bEErSUxINkNPazNNdTJ5NmVWVmVlTFJIalJkWURKTXdiTytkbDRTZmhqczJONUJwUklkbUdtSGxML0VSMi9uNUhmeG1Nck1YdjR5UmpyVnZWZjN5VWdaYnFCY281RmN1cXl4VjJjUklaOE1maExvWEU1bHFiazZnblRTOVpROWREVlNlZUxIb3pQd3ZVZVRNQUJjdXplUkkvUXVsNDFPT0lDa3dzUnd4dDFKSEY4MmtqMWdnYTY3dEpndEpXZFVaVzlweWFnbGFHZTFaZ0ZnRGFGZFpVZmpKNHRuSG1iWGZUZ1RGS3FvRkcxMGZ0WXorL0NBS1FPKytyU1lrVFpzSXh0YjJEWGZUNjdZTmtVeWhHcUVZWlJRaERabHJGWVFXUGtEeWNYY1B5S09pMXNQVDlVUDZkQmd6ZndQc1gxNEpyd3FFbEdqUEtncFhGcUhpM1R0NHozdTUyNDlLWHpUZ2MyZndleEZkazdrdFJyTEtQakEyMnRGek5Wam0vbnJrb1hDemc4dWciLCJtYWMiOiJlZGYyMjUyYzQzNmNhMzE5ZTM4YjdkNGU1NmU0YWI3YmEwYTkyYTRiZmYzZTJiZThkMTA2YzljNWNmZTRhYzk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-996983659\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1254336118 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254336118\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-93714447 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:20:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJWOEJ3ZGFrMnVjS1NDd3dpUkU5WUE9PSIsInZhbHVlIjoiNkduMElNSTF3UFY5cXp5dkdiMFFLWTlTd1F0K0grM0RxRkxoL2xTTm9yWkFDL2t3QUltSkluTzBPaDliTXFYVWFjaDBMVHpNL0g2UHdBVmlHNWYxdXRJZk5PUURxZTdoSHN0amhweWUvbkI0aFhYNU5XMWp6a1pWK3VNUTFSNkVxOGRlbkhoYkc0RGx2Q2R2aW0vRlA3RytsRWVhY1pyUmJycTg1TUdnTUtsbUQ1bGFMRDZBSzAxNFE0UUNvQWs2UTlwRjlKd2pKNmJmZkZ2WUpmOHBFSnZLQ1RCbE9waTFla2dodUpmNis0V3p5WTVuQ0k3WGZ2RHFpMW9UbU5OcXNkaEdTY0J0eURHLzg5enpaKzBSVWg4Zkp4ZWRjQWZ2TmVSYnVRajBiNGlDN1VnSWZTRVphQ1hDMjNKaFo5anpuY0ZGMEFLMjVVd2xodys2bW9NNFpnUUQwd25yMW16T2h2MDN4TmFLVVVVcnVZS05pYXRHbzFXbTZvVThVamF0N0F3aDhtNitBQnF0V3Z2OHRuenZvZnJhTTMreHk2NXpBQ0hYbytsM3R4bitNaVBWRkxOVWxZMFBZWFNyS2UvNnpKMW15WXdObm9ubGUvQ21NRkxuOXVYeFZ0ZUJwNUltdm1oNHdDZTFybHp6TDJBbVlVVWVIS2t4dlk5WHVLYTUiLCJtYWMiOiJkYmE3MWNlNTE0OTMyMDE0ZWZkY2Q3NjE0ZTk0OTdjYTg0M2JmODFjNTc5ZjA0NzhjYWM0N2ZhYzFkYzczMWY4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:20:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjhqVW9BeGVwZ2hDNGEvQVRlWjVuVHc9PSIsInZhbHVlIjoidzVzbitUelMxcWpKVFdhR2NHdjJRZldjSkFYQlpnazBFUVlDQ2pxYWd2c0hzQU9sZVJqcWtjeThNQUJibWxMaE5NMHVTU053K1RnMEtvMmZaMFFpUTNOaURBMlUrMTVDb2FWQklRTnZGeDZMZWFTZjBFVUo5eDFYb0dQZnRucUtzSFVLOUFISVA1QXowbzdESHluR1RQOHM0ellvZzBuMUo2NDVHallKMUxmMU1hZCtCQnZFZjc3NmRKS04vWE5QL0dpc3B3TkIvMm1RMzVTRS9ueEF2MUMvM29sTGJQY1ZKRlJKa3ZKNkNISWN0bmR4WkpVVEMxeDZCcTcxN3lHZGlqd0RVdmlXUU9qSnVNK1cya09aSDZKT2lSNTlMU3F3YmZvUWk1cTBoVTNKMDdLelpLdzhLVStLUEk5V1ludnRacmhzS0RxclNLdWxJcnp3SHZnMzAzcnBtQmNqdnRlamtETlJrbnVxTTA0bnVzd0xCWjhLTEN5Zzkva1JXUm1RUytWSTdjbGFOR3FnbUNNYjdqWGlCMzhzeWltTzMvNHc2VXlEWjJ1UFV5RHBBdU9tVEliL1E0ZUpMUVJMTGxtUC9GSlpxMzJyL1N6NDlYTnQ4RFU3cTl2Tk1iR1BNcWRGV0JKdlVSMW04U0lhR3E1dFFXZG9KRmZLSWJ3elJUb2siLCJtYWMiOiIzYmRiNDZhYWMxZGY4MDE0MDIwMDllOTFkNTk4MjMzZmZiOWZkMjMzYTkwNGVjYjlmZmRhMjBkZTY0ZDIxMmJlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:20:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJWOEJ3ZGFrMnVjS1NDd3dpUkU5WUE9PSIsInZhbHVlIjoiNkduMElNSTF3UFY5cXp5dkdiMFFLWTlTd1F0K0grM0RxRkxoL2xTTm9yWkFDL2t3QUltSkluTzBPaDliTXFYVWFjaDBMVHpNL0g2UHdBVmlHNWYxdXRJZk5PUURxZTdoSHN0amhweWUvbkI0aFhYNU5XMWp6a1pWK3VNUTFSNkVxOGRlbkhoYkc0RGx2Q2R2aW0vRlA3RytsRWVhY1pyUmJycTg1TUdnTUtsbUQ1bGFMRDZBSzAxNFE0UUNvQWs2UTlwRjlKd2pKNmJmZkZ2WUpmOHBFSnZLQ1RCbE9waTFla2dodUpmNis0V3p5WTVuQ0k3WGZ2RHFpMW9UbU5OcXNkaEdTY0J0eURHLzg5enpaKzBSVWg4Zkp4ZWRjQWZ2TmVSYnVRajBiNGlDN1VnSWZTRVphQ1hDMjNKaFo5anpuY0ZGMEFLMjVVd2xodys2bW9NNFpnUUQwd25yMW16T2h2MDN4TmFLVVVVcnVZS05pYXRHbzFXbTZvVThVamF0N0F3aDhtNitBQnF0V3Z2OHRuenZvZnJhTTMreHk2NXpBQ0hYbytsM3R4bitNaVBWRkxOVWxZMFBZWFNyS2UvNnpKMW15WXdObm9ubGUvQ21NRkxuOXVYeFZ0ZUJwNUltdm1oNHdDZTFybHp6TDJBbVlVVWVIS2t4dlk5WHVLYTUiLCJtYWMiOiJkYmE3MWNlNTE0OTMyMDE0ZWZkY2Q3NjE0ZTk0OTdjYTg0M2JmODFjNTc5ZjA0NzhjYWM0N2ZhYzFkYzczMWY4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:20:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjhqVW9BeGVwZ2hDNGEvQVRlWjVuVHc9PSIsInZhbHVlIjoidzVzbitUelMxcWpKVFdhR2NHdjJRZldjSkFYQlpnazBFUVlDQ2pxYWd2c0hzQU9sZVJqcWtjeThNQUJibWxMaE5NMHVTU053K1RnMEtvMmZaMFFpUTNOaURBMlUrMTVDb2FWQklRTnZGeDZMZWFTZjBFVUo5eDFYb0dQZnRucUtzSFVLOUFISVA1QXowbzdESHluR1RQOHM0ellvZzBuMUo2NDVHallKMUxmMU1hZCtCQnZFZjc3NmRKS04vWE5QL0dpc3B3TkIvMm1RMzVTRS9ueEF2MUMvM29sTGJQY1ZKRlJKa3ZKNkNISWN0bmR4WkpVVEMxeDZCcTcxN3lHZGlqd0RVdmlXUU9qSnVNK1cya09aSDZKT2lSNTlMU3F3YmZvUWk1cTBoVTNKMDdLelpLdzhLVStLUEk5V1ludnRacmhzS0RxclNLdWxJcnp3SHZnMzAzcnBtQmNqdnRlamtETlJrbnVxTTA0bnVzd0xCWjhLTEN5Zzkva1JXUm1RUytWSTdjbGFOR3FnbUNNYjdqWGlCMzhzeWltTzMvNHc2VXlEWjJ1UFV5RHBBdU9tVEliL1E0ZUpMUVJMTGxtUC9GSlpxMzJyL1N6NDlYTnQ4RFU3cTl2Tk1iR1BNcWRGV0JKdlVSMW04U0lhR3E1dFFXZG9KRmZLSWJ3elJUb2siLCJtYWMiOiIzYmRiNDZhYWMxZGY4MDE0MDIwMDllOTFkNTk4MjMzZmZiOWZkMjMzYTkwNGVjYjlmZmRhMjBkZTY0ZDIxMmJlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:20:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93714447\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1760043527 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760043527\", {\"maxDepth\":0})</script>\n"}}