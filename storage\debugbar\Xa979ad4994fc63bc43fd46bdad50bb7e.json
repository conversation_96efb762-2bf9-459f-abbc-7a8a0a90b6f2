{"__meta": {"id": "Xa979ad4994fc63bc43fd46bdad50bb7e", "datetime": "2025-08-02 10:03:58", "utime": **********.913698, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754129037.807943, "end": **********.913724, "duration": 1.105780839920044, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1754129037.807943, "relative_start": 0, "end": **********.83582, "relative_end": **********.83582, "duration": 1.027876853942871, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.835848, "relative_start": 1.***************, "end": **********.913727, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "77.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3036\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1883 to 1889\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1883\" onclick=\"\">routes/web.php:1883-1889</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "96cPiQJQyUB7A8shzXU5nRehndClJfoWfeXFilCq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-997480829 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-997480829\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-912195621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-912195621\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-942899684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-942899684\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-933009461 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933009461\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-989224885 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-989224885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-276737055 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:03:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii93azMvK1R5QVRrYUpmSFdEc2UxZGc9PSIsInZhbHVlIjoiQ25pbTQ5NUtZWjZWNVd5RGFKTk84dWszSHZRV0FNMVc3NGVYd1lPWUhSWXBQTXNxWUtrU0FzMGE1b0pLc0VIWldlOUFxbVgxeS9wNmsvRk9CcTVDaDk0U2h3UUFFckx5YUVrRTBBeFc1WjJ2aTlVY3RjMkVtNUdmdjVzTkZZdzRFUUd0L1pISWdDSmdhNmZGNUtLTkhwb0hoVkxtNHo5ZXl1Wm85Ui9KSjF6NHRQT1dzWVhKcnhtS1IrTCs1a3hHZys2a0E1bGU4cUlRdTF1cnBTa1NZUW8yekVIUnBBMjRYaFFPb0Z2RHhXek53WWkrOFFBNFE0Yk9lNUUxZU42ZVZTQ0V0TEtLOTIrRnZGN1RNalliM2MyQkVzNS9HV1IraG90dmlVaEJHVWVRTlppOTBNcFZWT2NzeSt6dDl0V1ZQVEVNVG5OdVF6M0U2bmJWU1ppWDEzS2FoUk9JYXdySU5uWXIxblR1NVp3dnhBbHBySkx4OVJNZlAyWTVXYXFoMjZRV3hwVlY4d0wwbE5ZUEFoY0xlckh1czdlSjFFWGJFa3pNQy9uVjlMOTE1N3h2MG1jQVl6ai9SdlU5QlpVK0VGSzRlUnhoczNNRjRReHNVOHJsZHk1YlJMbUQ2UVVzY2szNW5QaW1QbURQQkRrTWxZSEFaL3lEMHczOCtSdnIiLCJtYWMiOiI3YWQwNDc2ZGJkOGU1MTA4Y2VlNzBiOWJmYjFiNmI3MzMxMjRiMGM4YjE4Njg4NThmNDIyY2UyZjE1NjY4YTRlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:03:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9nbjI4SkJKTEdEdllsKzBWLytaY0E9PSIsInZhbHVlIjoiU1ozaGFzM3JnNFBHc3Z3a2RNcEVNcTRkR0JwOWJjMVZGTWZuUGgzd1RQS1ByWmQvYXpKZEE1NVJhUDIwK0pOSVd2cXZabnhOTFdQMG1mVVBzWThTTHBneTF2MHdiSlpLUFdjZm1WdllBZVgrZzBGR0M2dm9ENlo4YXZUMFdFMjBtN09zdUdOUHpjWldHb2c2cU9pdGxRSmROcHJMMUFUWHc4UzRLYnNNZ1l3RFZCV25aeGp5cW9IWUJOVFZRVXZXcEZWdEt2OWg4RE1kWGhKdklLd3cydnNuWjRWQzJydUlNWHVlbnZTYmlQNlJobzhZcVNnU096THhIUTdhcHh1SWZWd2xiRE9rcldXT0Vrcm9MM0x5NVE5bGhuVitjK3NzY2xTQmVTRWRVUGdoeDRZTVltQjJXUEIwVU9paDdDbU4vQWpsRHNTMWJISjF6UmR6dUpzME1nTG5sbnVXT0lOdE9hc2Q2byt1WHM0RDhOeFZKRUtqNkxyem9OMmZ0U1R0aHJ1L2wrSHdnRnpLTWpBYWJ2ZmdsR0dlaTJtU2ZaRGJxTGxITHluMmltalAyc0lrc0s3Z2ZFVDlLQldqN21qT1haVXlJdXoxSVkrUTJ1bEhnOGlsRVZLb0lIUzZtMWNWWGJlVll0d0k1M0tXTzFpdFovNUNpa3FsK0wxTC9xSUoiLCJtYWMiOiJhYzc2MmIyYTZiYTQwYTFlY2UwNDQyOTdhMTk1MTJjZTYzYTA5YjUwOWYyYTBlOThlNjRmZDlhYTAwYTJkMzNmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:03:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii93azMvK1R5QVRrYUpmSFdEc2UxZGc9PSIsInZhbHVlIjoiQ25pbTQ5NUtZWjZWNVd5RGFKTk84dWszSHZRV0FNMVc3NGVYd1lPWUhSWXBQTXNxWUtrU0FzMGE1b0pLc0VIWldlOUFxbVgxeS9wNmsvRk9CcTVDaDk0U2h3UUFFckx5YUVrRTBBeFc1WjJ2aTlVY3RjMkVtNUdmdjVzTkZZdzRFUUd0L1pISWdDSmdhNmZGNUtLTkhwb0hoVkxtNHo5ZXl1Wm85Ui9KSjF6NHRQT1dzWVhKcnhtS1IrTCs1a3hHZys2a0E1bGU4cUlRdTF1cnBTa1NZUW8yekVIUnBBMjRYaFFPb0Z2RHhXek53WWkrOFFBNFE0Yk9lNUUxZU42ZVZTQ0V0TEtLOTIrRnZGN1RNalliM2MyQkVzNS9HV1IraG90dmlVaEJHVWVRTlppOTBNcFZWT2NzeSt6dDl0V1ZQVEVNVG5OdVF6M0U2bmJWU1ppWDEzS2FoUk9JYXdySU5uWXIxblR1NVp3dnhBbHBySkx4OVJNZlAyWTVXYXFoMjZRV3hwVlY4d0wwbE5ZUEFoY0xlckh1czdlSjFFWGJFa3pNQy9uVjlMOTE1N3h2MG1jQVl6ai9SdlU5QlpVK0VGSzRlUnhoczNNRjRReHNVOHJsZHk1YlJMbUQ2UVVzY2szNW5QaW1QbURQQkRrTWxZSEFaL3lEMHczOCtSdnIiLCJtYWMiOiI3YWQwNDc2ZGJkOGU1MTA4Y2VlNzBiOWJmYjFiNmI3MzMxMjRiMGM4YjE4Njg4NThmNDIyY2UyZjE1NjY4YTRlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:03:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9nbjI4SkJKTEdEdllsKzBWLytaY0E9PSIsInZhbHVlIjoiU1ozaGFzM3JnNFBHc3Z3a2RNcEVNcTRkR0JwOWJjMVZGTWZuUGgzd1RQS1ByWmQvYXpKZEE1NVJhUDIwK0pOSVd2cXZabnhOTFdQMG1mVVBzWThTTHBneTF2MHdiSlpLUFdjZm1WdllBZVgrZzBGR0M2dm9ENlo4YXZUMFdFMjBtN09zdUdOUHpjWldHb2c2cU9pdGxRSmROcHJMMUFUWHc4UzRLYnNNZ1l3RFZCV25aeGp5cW9IWUJOVFZRVXZXcEZWdEt2OWg4RE1kWGhKdklLd3cydnNuWjRWQzJydUlNWHVlbnZTYmlQNlJobzhZcVNnU096THhIUTdhcHh1SWZWd2xiRE9rcldXT0Vrcm9MM0x5NVE5bGhuVitjK3NzY2xTQmVTRWRVUGdoeDRZTVltQjJXUEIwVU9paDdDbU4vQWpsRHNTMWJISjF6UmR6dUpzME1nTG5sbnVXT0lOdE9hc2Q2byt1WHM0RDhOeFZKRUtqNkxyem9OMmZ0U1R0aHJ1L2wrSHdnRnpLTWpBYWJ2ZmdsR0dlaTJtU2ZaRGJxTGxITHluMmltalAyc0lrc0s3Z2ZFVDlLQldqN21qT1haVXlJdXoxSVkrUTJ1bEhnOGlsRVZLb0lIUzZtMWNWWGJlVll0d0k1M0tXTzFpdFovNUNpa3FsK0wxTC9xSUoiLCJtYWMiOiJhYzc2MmIyYTZiYTQwYTFlY2UwNDQyOTdhMTk1MTJjZTYzYTA5YjUwOWYyYTBlOThlNjRmZDlhYTAwYTJkMzNmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:03:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276737055\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1520966676 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">96cPiQJQyUB7A8shzXU5nRehndClJfoWfeXFilCq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520966676\", {\"maxDepth\":0})</script>\n"}}