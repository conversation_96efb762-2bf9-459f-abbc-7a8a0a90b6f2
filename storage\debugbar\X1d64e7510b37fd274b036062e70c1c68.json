{"__meta": {"id": "X1d64e7510b37fd274b036062e70c1c68", "datetime": "2025-08-02 10:38:25", "utime": **********.787952, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754131104.680824, "end": **********.787978, "duration": 1.1071538925170898, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1754131104.680824, "relative_start": 0, "end": **********.684893, "relative_end": **********.684893, "duration": 1.0040688514709473, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.684916, "relative_start": 1.****************, "end": **********.787981, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zfigRWVBxLmsNh3wZD7NCFK2TxtpOX6yuiugup95", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2055582525 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2055582525\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1486349157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1486349157\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-450609242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-450609242\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-195250965 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195250965\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-320514633 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-320514633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-925504121 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:38:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZzVFYxZXlSeDM5Y3ZpWjdBMnJiT2c9PSIsInZhbHVlIjoidHJtdWFKaTg5TzVJVkpud1NYMWNpbEZVWjczWFJwOER1eEVaTkhBdDRONjlwU0RYZGxlbnplVUtFSzFxa3hhRUlWaVJDNHM4ZVlCL1FEVHpxL2ZkaExESXFqbFBHWDFWWW5HcExyUGlCZWtob3JLNU44aDhpRnFPTnovSjZwaGxEQml6cktSWG5DRnduZUtQdmMxdWYvd0U1dlM4eGd5a2lMSFMxQzJ3MVBKellXMnczWS81S2F2Yi95SmhVSlZIN0cyRkREUldDbEN6elFFdnRWSUp6Skd1MXpxMjFhL3F6MFhqeVQrUmh1T2o4T0tjUlMzaUNuVnd6UXJvWUQwSlB6R2w0V2VIRjRzOHhkNnpNS2k3RmpCTFRQSEs4anQ3TlpjWXplUHlDL0c1WUlUSjVaSFFxZkg3TmZSTHNHZnNwQmsvVVJJWWJWbnF6c1FwT29kQXNmUlg4RElSRmV2WDljUWNsYXZPeVNqOFVhekt4UFBncVhWOXF4VVNPWVZGUmhHMkFDRTZCRnVLM2VWZ1VEcGsybGdIUEhJSzh2dVZLNmRDamJhVDkrYTArYktHVHphMHRiaElJS09PMFFhYXBveTJFV1NRZVRacUFFV1NXUW51ZHNOc0tqSElaNURZMmw1OU05R2IvRHdCb3VMaWhndmRlYnlGS0xZeE5WMXciLCJtYWMiOiJhZjI0NTc1MzRmZGZiMTVkMjc2ZjhiYThiNDZhNjhlYzJjYjEzODRlZmVlOTE0OThhNTE0NTkwOGU2Njc2ZWUwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:38:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFPZHU2NnBZTjZrS2JKS0tDS1BOcHc9PSIsInZhbHVlIjoiSVdkY2ZaSnBnaU5EZk5ubVZCcG4xeHU4aU43UllpL1ZPREJGcG9xV2hDTTEvdms3ZFFJd3BwTXpuZFhwVGE1KzIxbEEwMG8veVJFM0dDZndTOGZTV3FMUWpuTGlleVpZMVNleFRHcUxidTMycm9tZE41a2pLdS9EeTVwcWNOSEJmWi9jcFN2YVpENDh4ejdDQ1YxdzU0NTlsTzNJbzFieENSNWtrNnQvd0JaSUdpU1drS05zZCsvV3MyUG9pRXJMNW8wL1E0eERqQXFNbFdrbXV0TU1HVUZybytwOEU3c2FyeGprbnFCSGtyWmxOQnJta2hOMzVrK1ZiYXo0SllhVnBSSFlqczdzYWFWNUp2YWs2YnkwbGpiSjBWMS9DZUJEWHdxSUJWaDZLYzBCaTJMV3Q2eFNReEEvT0F1SUUvbWtzMkR4dmRTc1JkanlTa0FzOC9hZ3FCbHVOc2IrRGV2MlhDeHJrMXExZGRLUDM0SDR1TVpxWUY0SEFGK0RXRXZyL3J5ZHFBYzAzaWNBblJJcmdzRWQxUWVYUWhLMUR0M2ZjaWdZdkJuY1k5MFJyZWMxTitELzFYMVVhUlhMY3g4ZDgrOHpNclVCci80d3NhV0xsSTM3RmdTQUNnN0NXdld0dUxiQ1cwQWFKbGZWSTc3ZS9HNFNMTjVpVFdMMEhzVHIiLCJtYWMiOiJhMTljOWQ5NzM0MDA5NWIyZWQ1NWQ0NDhlNDZlOTEyZWFjMmJmMzA2YjU0YjJiMDAxYTk0ZjA2YmVhYTQ3NDhkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:38:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZzVFYxZXlSeDM5Y3ZpWjdBMnJiT2c9PSIsInZhbHVlIjoidHJtdWFKaTg5TzVJVkpud1NYMWNpbEZVWjczWFJwOER1eEVaTkhBdDRONjlwU0RYZGxlbnplVUtFSzFxa3hhRUlWaVJDNHM4ZVlCL1FEVHpxL2ZkaExESXFqbFBHWDFWWW5HcExyUGlCZWtob3JLNU44aDhpRnFPTnovSjZwaGxEQml6cktSWG5DRnduZUtQdmMxdWYvd0U1dlM4eGd5a2lMSFMxQzJ3MVBKellXMnczWS81S2F2Yi95SmhVSlZIN0cyRkREUldDbEN6elFFdnRWSUp6Skd1MXpxMjFhL3F6MFhqeVQrUmh1T2o4T0tjUlMzaUNuVnd6UXJvWUQwSlB6R2w0V2VIRjRzOHhkNnpNS2k3RmpCTFRQSEs4anQ3TlpjWXplUHlDL0c1WUlUSjVaSFFxZkg3TmZSTHNHZnNwQmsvVVJJWWJWbnF6c1FwT29kQXNmUlg4RElSRmV2WDljUWNsYXZPeVNqOFVhekt4UFBncVhWOXF4VVNPWVZGUmhHMkFDRTZCRnVLM2VWZ1VEcGsybGdIUEhJSzh2dVZLNmRDamJhVDkrYTArYktHVHphMHRiaElJS09PMFFhYXBveTJFV1NRZVRacUFFV1NXUW51ZHNOc0tqSElaNURZMmw1OU05R2IvRHdCb3VMaWhndmRlYnlGS0xZeE5WMXciLCJtYWMiOiJhZjI0NTc1MzRmZGZiMTVkMjc2ZjhiYThiNDZhNjhlYzJjYjEzODRlZmVlOTE0OThhNTE0NTkwOGU2Njc2ZWUwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:38:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFPZHU2NnBZTjZrS2JKS0tDS1BOcHc9PSIsInZhbHVlIjoiSVdkY2ZaSnBnaU5EZk5ubVZCcG4xeHU4aU43UllpL1ZPREJGcG9xV2hDTTEvdms3ZFFJd3BwTXpuZFhwVGE1KzIxbEEwMG8veVJFM0dDZndTOGZTV3FMUWpuTGlleVpZMVNleFRHcUxidTMycm9tZE41a2pLdS9EeTVwcWNOSEJmWi9jcFN2YVpENDh4ejdDQ1YxdzU0NTlsTzNJbzFieENSNWtrNnQvd0JaSUdpU1drS05zZCsvV3MyUG9pRXJMNW8wL1E0eERqQXFNbFdrbXV0TU1HVUZybytwOEU3c2FyeGprbnFCSGtyWmxOQnJta2hOMzVrK1ZiYXo0SllhVnBSSFlqczdzYWFWNUp2YWs2YnkwbGpiSjBWMS9DZUJEWHdxSUJWaDZLYzBCaTJMV3Q2eFNReEEvT0F1SUUvbWtzMkR4dmRTc1JkanlTa0FzOC9hZ3FCbHVOc2IrRGV2MlhDeHJrMXExZGRLUDM0SDR1TVpxWUY0SEFGK0RXRXZyL3J5ZHFBYzAzaWNBblJJcmdzRWQxUWVYUWhLMUR0M2ZjaWdZdkJuY1k5MFJyZWMxTitELzFYMVVhUlhMY3g4ZDgrOHpNclVCci80d3NhV0xsSTM3RmdTQUNnN0NXdld0dUxiQ1cwQWFKbGZWSTc3ZS9HNFNMTjVpVFdMMEhzVHIiLCJtYWMiOiJhMTljOWQ5NzM0MDA5NWIyZWQ1NWQ0NDhlNDZlOTEyZWFjMmJmMzA2YjU0YjJiMDAxYTk0ZjA2YmVhYTQ3NDhkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:38:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925504121\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1806279240 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zfigRWVBxLmsNh3wZD7NCFK2TxtpOX6yuiugup95</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806279240\", {\"maxDepth\":0})</script>\n"}}