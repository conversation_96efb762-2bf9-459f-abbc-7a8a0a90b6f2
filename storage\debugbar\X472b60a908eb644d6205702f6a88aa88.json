{"__meta": {"id": "X472b60a908eb644d6205702f6a88aa88", "datetime": "2025-08-02 10:47:03", "utime": **********.415292, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754131622.252655, "end": **********.415319, "duration": 1.1626639366149902, "duration_str": "1.16s", "measures": [{"label": "Booting", "start": 1754131622.252655, "relative_start": 0, "end": **********.315596, "relative_end": **********.315596, "duration": 1.062941074371338, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.315615, "relative_start": 1.***************, "end": **********.415322, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "99.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A1KDHhNkhYQM9DBRW3bXFtZbCSpe78j7DXKZYJ8t", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-277470393 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-277470393\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1465957487 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1465957487\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-722119218 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-722119218\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-466943898 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466943898\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-400216126 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-400216126\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2057774682 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:47:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJIT1lkb2xOSy9UUzNLQVF3RXltaVE9PSIsInZhbHVlIjoiU3V4clRWTmlQSUZUSjh5NTJONlpoTnBBcW9FRllkSHg2TGI5dFkyZElpUklCclU0emFETVZDTkRMdndrVVFZUTM0aDY2U3drOVBPVjE5aE1TTGFtWnk0RjV6NlRHUlRTbXE3NkdZY2xLcGNaaGRrN3U3YnRxZEtoQzg4S2Z2Y2ZqUjlDRVNGdGl0UklGdnU3b2g4MkpzKzVBdzIwV2xoY1BBZWFmUUx5Z0JnVnVEa3RGRHJyYisvdStOT2NjRG5QMXNFeXljeDhBdStPZnBuQk1WV25rZ3h4Znd6UlRCZW5xOW5nT0NDem9TSFZLa3c5QmhXQmRqVnUzYmI5YXUxamJIZ0JQK2dhTmJOU280TFkwQklyMHBPcmtXUGg0aDFLUFM3T2tlbFRjQlBKdWlVc3I2OElmSnFJNkFrQVl5VDFKZGFycGJyYk9BQ0RvNXg1RlArdS9ab2pnOERLWm1iNjBhZlJ2ZDVjMENHNmcvQ25Ec3VNUkM5bGY4ckpDa1BZZnB0ZUFkK3l5UElkU0kvYXA0USsyK2gzMEo0NGZNV3A5eC9WTjhSREpyalhYdVN2TEFFV3NoL1c1ZnlLeW5tQ3lRcnJuaERkQno4RVo5ZWUxakxNeVNoODluMUtPQy8wZEdiaG03STRTNXhLQ2cxQTRGVHFwZ1YybjE1ODl0ankiLCJtYWMiOiJhMDk4MWNhOTg4ZWMzNTliNmZhZGVjOTJjYTAxYWI1ZmYyOTczMDZhZGYzMjU0NGMxNTFiOGZhNGJhZWZhMThmIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:47:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVwZ04xZ3JiNEJ6WXd3NUVaQXZXSlE9PSIsInZhbHVlIjoiMDlnRDRYS1N4NXZyR1BxSGU5bDh5b1BWZ1cyNEpnL3hLQWhrdldUT202VXR4dGtTQWNRMWNLUWlzdHFRVEtGYXlQOGVOSWpPZjVnQnF0Uk1JakdINU9Db2NlMks3MmVmd0I0dkQyQlYwT0JhNUpSTitleEdzeW1yMkIxSnIzUTVnUllIZmE4K0Y1WWI2S0RCWjBrQm1IY2daVEZTNlViT0R1MmdCYTZGcW9rMWQ1WWZ0aGE2U0srcEoyd050UzY3OW93VkYyQjRhV2JJTnZUYTRMbmgranBFRzgzaVpIKzhaMFhCWEJNa3JFYm1EWDF5VnFFK2tTSWpNK0RmU0NpTDRRTEpJeThLbHc1cmlIMEhtcThsL3p1MjFldTRwTEFkTElWYWRjVFBoa084MERwSkpic0huQjBJVnlKVTJiMlo3MURCQmJOL3VxcXhubC84NWFwRjlsdjJuNXVlUHdUT3pxOW1tVUpKaFM3cGlVWFEyemlSRFJJY0JlS1JseDBQRzNiN0dzUFdCUmp1VXJaN3cyMHV1WWtRTFZKV2VoelVLSHFwOFVVRnlEb1ZQa1hMVU43aHJSKzdjc2Z2Y2R5SjRGUDdLQ2JpT2o1RHR4SnZsSU9rYmpiQUg0K1NWa0lFMzF2Wm1RK1JqUG9LTTFOeXZQd1FDZFcvN1ZrZmlFKzEiLCJtYWMiOiIxZTMxYTQ2MmJlODBjMjM5MDNhZTMwZDIwOWQ1ODY3MzMxYzY3M2FhN2M0YzM2ZjRjZjI0OTEzNjQ4MDE1ZWM2IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:47:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJIT1lkb2xOSy9UUzNLQVF3RXltaVE9PSIsInZhbHVlIjoiU3V4clRWTmlQSUZUSjh5NTJONlpoTnBBcW9FRllkSHg2TGI5dFkyZElpUklCclU0emFETVZDTkRMdndrVVFZUTM0aDY2U3drOVBPVjE5aE1TTGFtWnk0RjV6NlRHUlRTbXE3NkdZY2xLcGNaaGRrN3U3YnRxZEtoQzg4S2Z2Y2ZqUjlDRVNGdGl0UklGdnU3b2g4MkpzKzVBdzIwV2xoY1BBZWFmUUx5Z0JnVnVEa3RGRHJyYisvdStOT2NjRG5QMXNFeXljeDhBdStPZnBuQk1WV25rZ3h4Znd6UlRCZW5xOW5nT0NDem9TSFZLa3c5QmhXQmRqVnUzYmI5YXUxamJIZ0JQK2dhTmJOU280TFkwQklyMHBPcmtXUGg0aDFLUFM3T2tlbFRjQlBKdWlVc3I2OElmSnFJNkFrQVl5VDFKZGFycGJyYk9BQ0RvNXg1RlArdS9ab2pnOERLWm1iNjBhZlJ2ZDVjMENHNmcvQ25Ec3VNUkM5bGY4ckpDa1BZZnB0ZUFkK3l5UElkU0kvYXA0USsyK2gzMEo0NGZNV3A5eC9WTjhSREpyalhYdVN2TEFFV3NoL1c1ZnlLeW5tQ3lRcnJuaERkQno4RVo5ZWUxakxNeVNoODluMUtPQy8wZEdiaG03STRTNXhLQ2cxQTRGVHFwZ1YybjE1ODl0ankiLCJtYWMiOiJhMDk4MWNhOTg4ZWMzNTliNmZhZGVjOTJjYTAxYWI1ZmYyOTczMDZhZGYzMjU0NGMxNTFiOGZhNGJhZWZhMThmIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:47:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVwZ04xZ3JiNEJ6WXd3NUVaQXZXSlE9PSIsInZhbHVlIjoiMDlnRDRYS1N4NXZyR1BxSGU5bDh5b1BWZ1cyNEpnL3hLQWhrdldUT202VXR4dGtTQWNRMWNLUWlzdHFRVEtGYXlQOGVOSWpPZjVnQnF0Uk1JakdINU9Db2NlMks3MmVmd0I0dkQyQlYwT0JhNUpSTitleEdzeW1yMkIxSnIzUTVnUllIZmE4K0Y1WWI2S0RCWjBrQm1IY2daVEZTNlViT0R1MmdCYTZGcW9rMWQ1WWZ0aGE2U0srcEoyd050UzY3OW93VkYyQjRhV2JJTnZUYTRMbmgranBFRzgzaVpIKzhaMFhCWEJNa3JFYm1EWDF5VnFFK2tTSWpNK0RmU0NpTDRRTEpJeThLbHc1cmlIMEhtcThsL3p1MjFldTRwTEFkTElWYWRjVFBoa084MERwSkpic0huQjBJVnlKVTJiMlo3MURCQmJOL3VxcXhubC84NWFwRjlsdjJuNXVlUHdUT3pxOW1tVUpKaFM3cGlVWFEyemlSRFJJY0JlS1JseDBQRzNiN0dzUFdCUmp1VXJaN3cyMHV1WWtRTFZKV2VoelVLSHFwOFVVRnlEb1ZQa1hMVU43aHJSKzdjc2Z2Y2R5SjRGUDdLQ2JpT2o1RHR4SnZsSU9rYmpiQUg0K1NWa0lFMzF2Wm1RK1JqUG9LTTFOeXZQd1FDZFcvN1ZrZmlFKzEiLCJtYWMiOiIxZTMxYTQ2MmJlODBjMjM5MDNhZTMwZDIwOWQ1ODY3MzMxYzY3M2FhN2M0YzM2ZjRjZjI0OTEzNjQ4MDE1ZWM2IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:47:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057774682\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1513214209 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A1KDHhNkhYQM9DBRW3bXFtZbCSpe78j7DXKZYJ8t</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513214209\", {\"maxDepth\":0})</script>\n"}}