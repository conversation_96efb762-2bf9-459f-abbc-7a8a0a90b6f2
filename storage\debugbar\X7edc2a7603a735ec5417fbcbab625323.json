{"__meta": {"id": "X7edc2a7603a735ec5417fbcbab625323", "datetime": "2025-08-02 10:39:51", "utime": **********.949117, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.061983, "end": **********.949146, "duration": 0.8871629238128662, "duration_str": "887ms", "measures": [{"label": "Booting", "start": **********.061983, "relative_start": 0, "end": **********.864646, "relative_end": **********.864646, "duration": 0.8026628494262695, "duration_str": "803ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.864679, "relative_start": 0.****************, "end": **********.949149, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "84.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KoMiJ3uHX5C2thUiWH44M0Qltzg7qLaiio20oEuL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-654008586 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-654008586\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-564567598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-564567598\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1643184202 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1643184202\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-679917833 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679917833\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-566998919 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-566998919\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1426244680 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:39:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBOQVZzWTNtN0lCd1dlbmo1d3pHSmc9PSIsInZhbHVlIjoiamZFQnlNUG1aOURPT2EzNjBnR1poaW9HYVl6TGxySDRRUDd2VVA5QjYrMys0Q2d1QUV4ZWNXOEJOaDl0VXp3MW9YRFU2WEVsTDkyQm5ETWQxTnAvaFl5ekQ1dVBvWm91OTQzTkNMcVh0N2dDd0RVa2NVVFR3Q1h5d1lLSXplc3JPVmU5aDhmbm9ScEw0MlVMYTFJS05FWFFIdkNjTnQyNTRSTkhmSENZZlJFOWFLcnllUzlWdmw0cE9zcHhZQWZaTmFxR0R3aVRNaUZ3K3RUSFdPRnJnazA4S3hjMWMyU1NtS2ZKSXc4NGV1LzZTNVNTcE1HOGJkNTc5VU9hMHhVekQvSHV5SVlOMldaZFllYjBCKzI3QS96REpHYnVGWnpya3RnMUQ4MTRrVHI1NjFBb015bVBiTXFUeXhSRUJCZnVneDc2RzRua3dnVVhONWNOcEEzQk1taGpNQm9uQ0w2UHdCc1ZQT1hMUHJTaE9XYmFGcTNadUg3YUpqMEJJbEcyK3pPZmlLNXY5NDFnbTVXTWh1b1dzb0Foczg4NzRKNDdyMFBzS3BMMS90SENFRVU0aldsaEtNamEvVGhtTkJ4NmxDNGVkczUwR1oweVBMQXNoaTFZcC8yczF0eWQzWExpWFl0dU01MmUyL3ZiQW1tOXh6Y3pvN0E5NGhrZlNHbEEiLCJtYWMiOiJkZjA0YmY4NzQzODMzOWM5NDMxMTU5ODBkMTdkMGY1MmM4ZTBhZDMzYTg2Y2VhYjkwNzIzMTBhZmIwMzI5YmYyIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:39:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRHcDNRZ2VpbGxmQkxkVkVZdkNoT0E9PSIsInZhbHVlIjoiZW8weUtieEFyUWtPdk5QZ0ViVnBIU0hLcThNNDVqTC9mL0lkU2lZZmpMdWg1YUQ0QnExaElocUp4NTV3OG1GUy82MlRMeWNTYzBxUXRkNGI0KzZERk5RdGlIZGhEY2pWaWZ0aUg5d1VOWEd4Q3R5TXBadDVLUGZiUXJ3ZFB5VGlyaE1XS2o1QTdTTi84SjNkT3lFVjRFRTJWYTFFamJ0SlB3Rk5PUGx1eWhvckZlbWJndmFkSC82b29OVUtTY00va3I0Qi9iMzc5QzFIZ1FMend0V3lFaU42MjNnQnIxOEhXYVlQdko3VlZ6TDdDUG5KRWVaczVMcEJjbERtQ2lCbmVLQjE3WHUrQ2NuUUFKS0ZWalp6bVVCWUUvWDRPL3cvZ3p5MGIwcWZPM0EzbzQwdG9pZnUrZGRnOU9pTDVhb2J2NlZFRmNOYWlKTm9hZ202Q1NZV1Bwekc2TnBjV0xCcWU3MHNQOFBlNTgxczZMWGVCSCt4TXBWTUEvRlZldG9mM0VuMkMxZklUbFpHekxxZDd1VVBVeFdLNjBlRjd2aHlEVmJCQVBuT1g4b0dNOWl6YkFtSzh2bXc5QkdsNXNrRzlxUzdzTGFDWWVsZUtSdGU2L0Qxd3hUMU5hMSsyTTJDSU1YU2NNaXQ1dnk4bU1sQWJJanpybW9kNFBPc1dxTkIiLCJtYWMiOiI4NDAyZmU3MWEyNjdkNDBiOWUxZWRjMDYyYjY5NjQ2MTNlOTQ5NzQ5N2FlZmI5NzU2YTZkODE5NzVmY2M0ZmFlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:39:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBOQVZzWTNtN0lCd1dlbmo1d3pHSmc9PSIsInZhbHVlIjoiamZFQnlNUG1aOURPT2EzNjBnR1poaW9HYVl6TGxySDRRUDd2VVA5QjYrMys0Q2d1QUV4ZWNXOEJOaDl0VXp3MW9YRFU2WEVsTDkyQm5ETWQxTnAvaFl5ekQ1dVBvWm91OTQzTkNMcVh0N2dDd0RVa2NVVFR3Q1h5d1lLSXplc3JPVmU5aDhmbm9ScEw0MlVMYTFJS05FWFFIdkNjTnQyNTRSTkhmSENZZlJFOWFLcnllUzlWdmw0cE9zcHhZQWZaTmFxR0R3aVRNaUZ3K3RUSFdPRnJnazA4S3hjMWMyU1NtS2ZKSXc4NGV1LzZTNVNTcE1HOGJkNTc5VU9hMHhVekQvSHV5SVlOMldaZFllYjBCKzI3QS96REpHYnVGWnpya3RnMUQ4MTRrVHI1NjFBb015bVBiTXFUeXhSRUJCZnVneDc2RzRua3dnVVhONWNOcEEzQk1taGpNQm9uQ0w2UHdCc1ZQT1hMUHJTaE9XYmFGcTNadUg3YUpqMEJJbEcyK3pPZmlLNXY5NDFnbTVXTWh1b1dzb0Foczg4NzRKNDdyMFBzS3BMMS90SENFRVU0aldsaEtNamEvVGhtTkJ4NmxDNGVkczUwR1oweVBMQXNoaTFZcC8yczF0eWQzWExpWFl0dU01MmUyL3ZiQW1tOXh6Y3pvN0E5NGhrZlNHbEEiLCJtYWMiOiJkZjA0YmY4NzQzODMzOWM5NDMxMTU5ODBkMTdkMGY1MmM4ZTBhZDMzYTg2Y2VhYjkwNzIzMTBhZmIwMzI5YmYyIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:39:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRHcDNRZ2VpbGxmQkxkVkVZdkNoT0E9PSIsInZhbHVlIjoiZW8weUtieEFyUWtPdk5QZ0ViVnBIU0hLcThNNDVqTC9mL0lkU2lZZmpMdWg1YUQ0QnExaElocUp4NTV3OG1GUy82MlRMeWNTYzBxUXRkNGI0KzZERk5RdGlIZGhEY2pWaWZ0aUg5d1VOWEd4Q3R5TXBadDVLUGZiUXJ3ZFB5VGlyaE1XS2o1QTdTTi84SjNkT3lFVjRFRTJWYTFFamJ0SlB3Rk5PUGx1eWhvckZlbWJndmFkSC82b29OVUtTY00va3I0Qi9iMzc5QzFIZ1FMend0V3lFaU42MjNnQnIxOEhXYVlQdko3VlZ6TDdDUG5KRWVaczVMcEJjbERtQ2lCbmVLQjE3WHUrQ2NuUUFKS0ZWalp6bVVCWUUvWDRPL3cvZ3p5MGIwcWZPM0EzbzQwdG9pZnUrZGRnOU9pTDVhb2J2NlZFRmNOYWlKTm9hZ202Q1NZV1Bwekc2TnBjV0xCcWU3MHNQOFBlNTgxczZMWGVCSCt4TXBWTUEvRlZldG9mM0VuMkMxZklUbFpHekxxZDd1VVBVeFdLNjBlRjd2aHlEVmJCQVBuT1g4b0dNOWl6YkFtSzh2bXc5QkdsNXNrRzlxUzdzTGFDWWVsZUtSdGU2L0Qxd3hUMU5hMSsyTTJDSU1YU2NNaXQ1dnk4bU1sQWJJanpybW9kNFBPc1dxTkIiLCJtYWMiOiI4NDAyZmU3MWEyNjdkNDBiOWUxZWRjMDYyYjY5NjQ2MTNlOTQ5NzQ5N2FlZmI5NzU2YTZkODE5NzVmY2M0ZmFlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:39:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426244680\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1020712693 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KoMiJ3uHX5C2thUiWH44M0Qltzg7qLaiio20oEuL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020712693\", {\"maxDepth\":0})</script>\n"}}