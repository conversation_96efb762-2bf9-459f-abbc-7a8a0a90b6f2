{"__meta": {"id": "X810f549847d7d9be1c803c6143ff708a", "datetime": "2025-08-02 09:15:07", "utime": **********.680932, "method": "POST", "uri": "/leads", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 28, "messages": [{"message": "[09:15:05] LOG.info: Lead creation started {\n    \"user_id\": 79,\n    \"request_data\": {\n        \"_token\": \"qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL\",\n        \"name\": \"<PERSON><PERSON><PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"+918965201235\",\n        \"subject\": null,\n        \"user_id\": \"81\",\n        \"date_of_birth\": \"2025-07-30\",\n        \"next_follow_up_date\": \"2025-08-16\",\n        \"type\": \"lead\",\n        \"pipeline_id\": \"23\",\n        \"stage_id\": \"90\",\n        \"status\": \"hot\",\n        \"opportunity_info\": null,\n        \"opportunity_source\": \"social_media\",\n        \"lead_value\": null,\n        \"opportunity_description\": \"ok\",\n        \"labels\": [\n            \"88\",\n            \"90\",\n            \"91\",\n            \"92\",\n            \"93\",\n            \"94\",\n            \"new_<PERSON> Paruchay\",\n            \"new_Mr DEMON\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.062359, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Creating lead with data {\n    \"pipeline_id\": 23,\n    \"stage_id\": 90,\n    \"user_id\": \"81\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.361567, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Processing tags {\n    \"tagsInput\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\",\n        \"new_<PERSON> Paruchay\",\n        \"new_Mr DEMON\"\n    ],\n    \"has_labels\": true,\n    \"has_tags\": false,\n    \"labels_value\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\",\n        \"new_<PERSON> Paruchay\",\n        \"new_Mr DEMON\"\n    ],\n    \"tags_value\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.364095, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Using existing tag ID {\n    \"tag_id\": \"88\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.364286, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Using existing tag ID {\n    \"tag_id\": \"90\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.364449, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Using existing tag ID {\n    \"tag_id\": \"91\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.36462, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Using existing tag ID {\n    \"tag_id\": \"92\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.364775, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Using existing tag ID {\n    \"tag_id\": \"93\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.364926, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Using existing tag ID {\n    \"tag_id\": \"94\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.365074, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.error: Error processing tag {\n    \"tag\": \"new_<PERSON> Paruchay\",\n    \"error\": \"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Ok Paruchay, primary, 79, 1, 2025-08-02 09:15:05, 2025-08-02 09:15:05))\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.372676, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.error: Error processing tag {\n    \"tag\": \"new_Mr DEMON\",\n    \"error\": \"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'color' in 'field list' (Connection: mysql, SQL: insert into `tags` (`name`, `color`, `created_by`, `is_active`, `updated_at`, `created_at`) values (Mr <PERSON>, primary, 79, 1, 2025-08-02 09:15:05, 2025-08-02 09:15:05))\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.378369, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Set lead tags {\n    \"tag_ids\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\"\n    ],\n    \"tags_string\": \"88,90,91,92,93,94\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.378724, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.debug: Lead tags retrieved {\n    \"lead_id\": 18,\n    \"tags_field\": \"88,90,91,92,93,94\",\n    \"labels_field\": \"88,90,91,92,93,94\",\n    \"tag_ids\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\"\n    ],\n    \"tags_found\": 6,\n    \"tag_names\": [\n        \"Follow Up\",\n        \"Hot Lead\",\n        \"New Customer\",\n        \"Ok AUI\",\n        \"VIP\",\n        \"Warm Lead\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.406157, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.debug: Lead tags retrieved {\n    \"lead_id\": 18,\n    \"tags_field\": \"88,90,91,92,93,94\",\n    \"labels_field\": \"88,90,91,92,93,94\",\n    \"tag_ids\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\"\n    ],\n    \"tags_found\": 6,\n    \"tag_names\": [\n        \"Follow Up\",\n        \"Hot Lead\",\n        \"New Customer\",\n        \"Ok AUI\",\n        \"VIP\",\n        \"Warm Lead\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.410938, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.debug: Lead tags retrieved {\n    \"lead_id\": 18,\n    \"tags_field\": \"88,90,91,92,93,94\",\n    \"labels_field\": \"88,90,91,92,93,94\",\n    \"tag_ids\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\"\n    ],\n    \"tags_found\": 6,\n    \"tag_names\": [\n        \"Follow Up\",\n        \"Hot Lead\",\n        \"New Customer\",\n        \"Ok AUI\",\n        \"VIP\",\n        \"Warm Lead\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.415337, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.debug: Lead tags retrieved {\n    \"lead_id\": 18,\n    \"tags_field\": \"88,90,91,92,93,94\",\n    \"labels_field\": \"88,90,91,92,93,94\",\n    \"tag_ids\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\"\n    ],\n    \"tags_found\": 6,\n    \"tag_names\": [\n        \"Follow Up\",\n        \"Hot Lead\",\n        \"New Customer\",\n        \"Ok AUI\",\n        \"VIP\",\n        \"Warm Lead\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.419296, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Lead saved successfully {\n    \"lead_id\": 18,\n    \"tags_field\": [\n        {\n            \"id\": 92,\n            \"name\": \"Follow Up\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Hot Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 93,\n            \"name\": \"New Customer\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 94,\n            \"name\": \"Ok AUI\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-08-02T07:52:49.000000Z\",\n            \"updated_at\": \"2025-08-02T07:52:49.000000Z\"\n        },\n        {\n            \"id\": 91,\n            \"name\": \"VIP\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Warm Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        }\n    ],\n    \"labels_field\": [\n        {\n            \"id\": 88,\n            \"name\": \"Hot Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Warm Lead\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 91,\n            \"name\": \"VIP\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 92,\n            \"name\": \"Follow Up\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 93,\n            \"name\": \"New Customer\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n            \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n        },\n        {\n            \"id\": 94,\n            \"name\": \"Ok AUI\",\n            \"created_by\": 79,\n            \"is_active\": 1,\n            \"created_at\": \"2025-08-02T07:52:49.000000Z\",\n            \"updated_at\": \"2025-08-02T07:52:49.000000Z\"\n        }\n    ],\n    \"tags_method_count\": 6,\n    \"tags_method_data\": [\n        \"Follow Up\",\n        \"Hot Lead\",\n        \"New Customer\",\n        \"Ok AUI\",\n        \"VIP\",\n        \"Warm Lead\"\n    ],\n    \"raw_attributes\": {\n        \"tags\": \"88,90,91,92,93,94\",\n        \"labels\": \"88,90,91,92,93,94\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.427174, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: UserLead relationships created successfully", "message_html": null, "is_string": false, "label": "info", "time": **********.441217, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Starting webhook dispatch for action: crm.lead_created {\n    \"timestamp\": \"2025-08-02T09:15:05.504132Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 79,\n    \"entity_type\": \"Lead\",\n    \"entity_id\": 18,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.507335, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_created", "message_html": null, "is_string": false, "label": "info", "time": **********.507762, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Found 1 enabled integrations for webhook action: crm.lead_created", "message_html": null, "is_string": false, "label": "info", "time": **********.513442, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:05] LOG.info: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/", "message_html": null, "is_string": false, "label": "info", "time": **********.513671, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:07] LOG.error: <PERSON><PERSON><PERSON> failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-08-02T09:15:07.648640Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 2135,\n    \"user_id\": 79,\n    \"entity_id\": 18,\n    \"entity_type\": \"Lead\",\n    \"request_payload\": {\n        \"action\": \"crm.lead_created\",\n        \"timestamp\": \"2025-08-02T09:15:05.517015Z\",\n        \"data\": {\n            \"id\": 18,\n            \"name\": \"<PERSON><PERSON><PERSON>\",\n            \"contact_type\": \"Lead\",\n            \"tags\": [\n                {\n                    \"id\": 92,\n                    \"name\": \"Follow Up\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 88,\n                    \"name\": \"Hot Lead\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 93,\n                    \"name\": \"New Customer\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 94,\n                    \"name\": \"Ok AUI\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-08-02T07:52:49.000000Z\",\n                    \"updated_at\": \"2025-08-02T07:52:49.000000Z\"\n                },\n                {\n                    \"id\": 91,\n                    \"name\": \"VIP\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 90,\n                    \"name\": \"Warm Lead\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                }\n            ],\n            \"postal_code\": null,\n            \"city\": null,\n            \"state\": null,\n            \"country\": null,\n            \"business_name\": null,\n            \"business_gst\": null,\n            \"business_state\": null,\n            \"business_postal_code\": null,\n            \"business_address\": null,\n            \"dnd_settings\": null,\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"+918965201235\",\n            \"date_of_birth\": \"2025-07-30\",\n            \"type\": \"\",\n            \"status\": \"\",\n            \"opportunity_info\": null,\n            \"opportunity_description\": \"ok\",\n            \"opportunity_source\": \"social_media\",\n            \"lead_value\": null,\n            \"subject\": \"Lead from Anurak Mukharji\",\n            \"user_id\": 81,\n            \"pipeline_id\": 23,\n            \"stage_id\": 90,\n            \"contact_group_id\": null,\n            \"sources\": [],\n            \"products\": [],\n            \"notes\": null,\n            \"labels\": [],\n            \"custom_fields\": null,\n            \"order\": 0,\n            \"created_by\": 79,\n            \"is_deleted\": 0,\n            \"is_active\": 1,\n            \"is_converted\": 0,\n            \"date\": \"2025-08-02\",\n            \"next_follow_up_date\": \"2025-08-16\",\n            \"created_at\": \"2025-08-02T09:15:05.000000Z\",\n            \"updated_at\": \"2025-08-02T09:15:05.000000Z\",\n            \"stage\": {\n                \"id\": 90,\n                \"name\": \"Won\\/Lost\",\n                \"pipeline_id\": 23,\n                \"created_by\": 79,\n                \"order\": 4,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:03.000000Z\"\n            },\n            \"pipeline\": {\n                \"id\": 23,\n                \"name\": \"OMX Digital Bot\",\n                \"created_by\": 79,\n                \"is_deleted\": 0,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:42.000000Z\"\n            },\n            \"triggered_by\": {\n                \"user_id\": 79,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"Parichay Singha AI\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 79,\n        \"triggered_by\": {\n            \"user_id\": 79,\n            \"email\": \"<EMAIL>\",\n            \"name\": \"Parichay Singha AI\",\n            \"type\": \"company\"\n        },\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.649069, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:07] LOG.info: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_created", "message_html": null, "is_string": false, "label": "info", "time": **********.649479, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:07] LOG.warning: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-08-02T09:15:07.649571Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 79,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.650007, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:07] LOG.debug: Lead tags retrieved {\n    \"lead_id\": 18,\n    \"tags_field\": \"88,90,91,92,93,94\",\n    \"labels_field\": \"88,90,91,92,93,94\",\n    \"tag_ids\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\"\n    ],\n    \"tags_found\": 6,\n    \"tag_names\": [\n        \"Follow Up\",\n        \"Hot Lead\",\n        \"New Customer\",\n        \"Ok AUI\",\n        \"VIP\",\n        \"Warm Lead\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.658161, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:07] LOG.debug: Lead tags retrieved {\n    \"lead_id\": 18,\n    \"tags_field\": \"88,90,91,92,93,94\",\n    \"labels_field\": \"88,90,91,92,93,94\",\n    \"tag_ids\": [\n        \"88\",\n        \"90\",\n        \"91\",\n        \"92\",\n        \"93\",\n        \"94\"\n    ],\n    \"tags_found\": 6,\n    \"tag_names\": [\n        \"Follow Up\",\n        \"Hot Lead\",\n        \"New Customer\",\n        \"Ok AUI\",\n        \"VIP\",\n        \"Warm Lead\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.661976, "xdebug_link": null, "collector": "log"}, {"message": "[09:15:07] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.674785, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754126103.956412, "end": **********.680991, "duration": 3.724578857421875, "duration_str": "3.72s", "measures": [{"label": "Booting", "start": 1754126103.956412, "relative_start": 0, "end": 1754126104.9212, "relative_end": 1754126104.9212, "duration": 0.9647879600524902, "duration_str": "965ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754126104.921219, "relative_start": 0.9648070335388184, "end": **********.680994, "relative_end": 3.0994415283203125e-06, "duration": 2.759774923324585, "duration_str": "2.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 57507480, "peak_usage_str": "55MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads", "middleware": "web, verified, auth, XSS", "as": "leads.store", "controller": "App\\Http\\Controllers\\LeadController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=256\" onclick=\"\">app/Http/Controllers/LeadController.php:256-687</a>"}, "queries": {"nb_statements": 39, "nb_failed_statements": 0, "accumulated_duration": 0.08295999999999999, "accumulated_duration_str": "82.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.00027, "duration": 0.02885, "duration_str": "28.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 34.776}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0568569, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 34.776, "width_percent": 1.085}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.071745, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 35.861, "width_percent": 0.916}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0790908, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 36.777, "width_percent": 0.94}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 266}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.084765, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 37.717, "width_percent": 3.652}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.120246, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 41.369, "width_percent": 2.917}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.149548, "duration": 0.00828, "duration_str": "8.28ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 44.286, "width_percent": 9.981}, {"sql": "select * from `pipelines` where `created_by` = 79 and `id` = '23' limit 1", "type": "query", "params": [], "bindings": ["79", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3524399, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:324", "source": "app/Http/Controllers/LeadController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=324", "ajax": false, "filename": "LeadController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 54.267, "width_percent": 0.916}, {"sql": "select * from `lead_stages` where `id` = '90' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["90", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 325}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.357969, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:325", "source": "app/Http/Controllers/LeadController.php:325", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=325", "ajax": false, "filename": "LeadController.php", "line": "325"}, "connection": "radhe_same", "start_percent": 55.183, "width_percent": 0.808}, {"sql": "select * from `tags` where `name` = 'Ok Paruchay' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["<PERSON>", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 445}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.366175, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:445", "source": "app/Http/Controllers/LeadController.php:445", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=445", "ajax": false, "filename": "LeadController.php", "line": "445"}, "connection": "radhe_same", "start_percent": 55.991, "width_percent": 0.916}, {"sql": "select * from `tags` where `name` = 'Mr DEMON' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["Mr <PERSON>", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 445}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3731709, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:445", "source": "app/Http/Controllers/LeadController.php:445", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=445", "ajax": false, "filename": "LeadController.php", "line": "445"}, "connection": "radhe_same", "start_percent": 56.907, "width_percent": 1.362}, {"sql": "insert into `leads` (`name`, `email`, `phone`, `subject`, `user_id`, `pipeline_id`, `stage_id`, `created_by`, `date`, `date_of_birth`, `type`, `status`, `opportunity_info`, `opportunity_description`, `opportunity_source`, `lead_value`, `next_follow_up_date`, `contact_type`, `postal_code`, `city`, `state`, `country`, `business_name`, `business_gst`, `business_state`, `business_postal_code`, `business_address`, `dnd_settings`, `tags`, `labels`, `updated_at`, `created_at`) values ('<PERSON><PERSON><PERSON> Mukharji', '<EMAIL>', '+918965201235', 'Lead from Anurak Mukharji', '81', 23, 90, 79, '2025-08-02', '2025-07-30', 'lead', 'hot', '', 'ok', 'social_media', '', '2025-08-16', 'Lead', '', '', '', '', '', '', '', '', '', '', '88,90,91,92,93,94', '88,90,91,92,93,94', '2025-08-02 09:15:05', '2025-08-02 09:15:05')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "<EMAIL>", "+918965201235", "Lead from <PERSON><PERSON><PERSON>", "81", "23", "90", "79", "2025-08-02", "2025-07-30", "lead", "hot", "", "ok", "social_media", "", "2025-08-16", "Lead", "", "", "", "", "", "", "", "", "", "", "88,90,91,92,93,94", "88,90,91,92,93,94", "2025-08-02 09:15:05", "2025-08-02 09:15:05"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 494}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.379307, "duration": 0.00722, "duration_str": "7.22ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:494", "source": "app/Http/Controllers/LeadController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=494", "ajax": false, "filename": "LeadController.php", "line": "494"}, "connection": "radhe_same", "start_percent": 58.269, "width_percent": 8.703}, {"sql": "select * from `leads` where `id` = 18 limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 497}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.39029, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:497", "source": "app/Http/Controllers/LeadController.php:497", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=497", "ajax": false, "filename": "LeadController.php", "line": "497"}, "connection": "radhe_same", "start_percent": 66.972, "width_percent": 1.29}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 202}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 504}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.394326, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Lead.php:202", "source": "app/Models/Lead.php:202", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=202", "ajax": false, "filename": "Lead.php", "line": "202"}, "connection": "radhe_same", "start_percent": 68.262, "width_percent": 1.085}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94')", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 58}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 505}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.39899, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Lead.php:58", "source": "app/Models/Lead.php:58", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=58", "ajax": false, "filename": "Lead.php", "line": "58"}, "connection": "radhe_same", "start_percent": 69.347, "width_percent": 1.097}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 168}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 506}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.402611, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Lead.php:168", "source": "app/Models/Lead.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=168", "ajax": false, "filename": "Lead.php", "line": "168"}, "connection": "radhe_same", "start_percent": 70.444, "width_percent": 1.049}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 168}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 506}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4066231, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Lead.php:168", "source": "app/Models/Lead.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=168", "ajax": false, "filename": "Lead.php", "line": "168"}, "connection": "radhe_same", "start_percent": 71.492, "width_percent": 1.254}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 168}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 507}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.411296, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Lead.php:168", "source": "app/Models/Lead.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=168", "ajax": false, "filename": "Lead.php", "line": "168"}, "connection": "radhe_same", "start_percent": 72.746, "width_percent": 0.856}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 168}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 507}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.415694, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Lead.php:168", "source": "app/Models/Lead.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=168", "ajax": false, "filename": "Lead.php", "line": "168"}, "connection": "radhe_same", "start_percent": 73.602, "width_percent": 0.916}, {"sql": "insert into `user_leads` (`user_id`, `lead_id`, `updated_at`, `created_at`) values (79, 18, '2025-08-02 09:15:05', '2025-08-02 09:15:05')", "type": "query", "params": [], "bindings": ["79", "18", "2025-08-02 09:15:05", "2025-08-02 09:15:05"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 536}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.428627, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:536", "source": "app/Http/Controllers/LeadController.php:536", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=536", "ajax": false, "filename": "LeadController.php", "line": "536"}, "connection": "radhe_same", "start_percent": 74.518, "width_percent": 4.918}, {"sql": "insert into `user_leads` (`user_id`, `lead_id`, `updated_at`, `created_at`) values ('81', 18, '2025-08-02 09:15:05', '2025-08-02 09:15:05')", "type": "query", "params": [], "bindings": ["81", "18", "2025-08-02 09:15:05", "2025-08-02 09:15:05"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 536}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.43666, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:536", "source": "app/Http/Controllers/LeadController.php:536", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=536", "ajax": false, "filename": "LeadController.php", "line": "536"}, "connection": "radhe_same", "start_percent": 79.436, "width_percent": 2.23}, {"sql": "select * from `users` where `users`.`id` = '81' limit 1", "type": "query", "params": [], "bindings": ["81"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 566}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.441653, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:566", "source": "app/Http/Controllers/LeadController.php:566", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=566", "ajax": false, "filename": "LeadController.php", "line": "566"}, "connection": "radhe_same", "start_percent": 81.666, "width_percent": 1.085}, {"sql": "select * from `users` where `users`.`id` = '81' limit 1", "type": "query", "params": [], "bindings": ["81"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 572}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.445246, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:572", "source": "app/Http/Controllers/LeadController.php:572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=572", "ajax": false, "filename": "LeadController.php", "line": "572"}, "connection": "radhe_same", "start_percent": 82.751, "width_percent": 1.073}, {"sql": "select * from `email_templates` where `name` LIKE 'lead_assigned' limit 1", "type": "query", "params": [], "bindings": ["lead_assigned"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2418}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 581}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.449691, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:2418", "source": "app/Models/Utility.php:2418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2418", "ajax": false, "filename": "Utility.php", "line": "2418"}, "connection": "radhe_same", "start_percent": 83.824, "width_percent": 0.988}, {"sql": "select * from `user_email_templates` where `template_id` = 4 and `user_id` = 79 limit 1", "type": "query", "params": [], "bindings": ["4", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2422}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 581}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.453774, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2422", "source": "app/Models/Utility.php:2422", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2422", "ajax": false, "filename": "Utility.php", "line": "2422"}, "connection": "radhe_same", "start_percent": 84.812, "width_percent": 1.326}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94')", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 58}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 353}, {"index": 25, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 26, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}], "start": **********.463632, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Lead.php:58", "source": "app/Models/Lead.php:58", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=58", "ajax": false, "filename": "Lead.php", "line": "58"}, "connection": "radhe_same", "start_percent": 86.138, "width_percent": 1.037}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 202}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 353}, {"index": 25, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 26, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}], "start": **********.467665, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Lead.php:202", "source": "app/Models/Lead.php:202", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=202", "ajax": false, "filename": "Lead.php", "line": "202"}, "connection": "radhe_same", "start_percent": 87.175, "width_percent": 1.097}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = 90 and `lead_stages`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["90"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 358}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4766421, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:358", "source": "app/Services/CrmWebhookDispatcher.php:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=358", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "358"}, "connection": "radhe_same", "start_percent": 88.271, "width_percent": 0.796}, {"sql": "select * from `pipelines` where `pipelines`.`id` = 23 and `pipelines`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 365}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.480187, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:365", "source": "app/Services/CrmWebhookDispatcher.php:365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=365", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "365"}, "connection": "radhe_same", "start_percent": 89.067, "width_percent": 0.723}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94')", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 58}, {"index": 21, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 144}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 372}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}], "start": **********.483292, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Lead.php:58", "source": "app/Models/Lead.php:58", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=58", "ajax": false, "filename": "Lead.php", "line": "58"}, "connection": "radhe_same", "start_percent": 89.79, "width_percent": 1}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":94', '\\\"name\\\":\\\"Ok AUI\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"}]')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:94", "&quot;name&quot;:&quot;Ok AUI&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;}]"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 144}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 372}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}], "start": **********.487303, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "Lead.php:144", "source": "app/Models/Lead.php:144", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=144", "ajax": false, "filename": "Lead.php", "line": "144"}, "connection": "radhe_same", "start_percent": 90.791, "width_percent": 2.025}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 107}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}], "start": **********.4930649, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Lead.php:107", "source": "app/Models/Lead.php:107", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=107", "ajax": false, "filename": "Lead.php", "line": "107"}, "connection": "radhe_same", "start_percent": 92.816, "width_percent": 0.856}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 97}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 390}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}], "start": **********.497708, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Lead.php:97", "source": "app/Models/Lead.php:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=97", "ajax": false, "filename": "Lead.php", "line": "97"}, "connection": "radhe_same", "start_percent": 93.672, "width_percent": 0.808}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.500983, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": "app/Services/CrmWebhookDispatcher.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "radhe_same", "start_percent": 94.479, "width_percent": 1}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 63}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 607}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.509583, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:63", "source": "app/Services/ModuleWebhookService.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=63", "ajax": false, "filename": "ModuleWebhookService.php", "line": "63"}, "connection": "radhe_same", "start_percent": 95.48, "width_percent": 0.735}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 227}, {"index": 21, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 97}, {"index": 22, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 76}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}], "start": **********.513974, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:227", "source": "app/Services/ModuleWebhookService.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=227", "ajax": false, "filename": "ModuleWebhookService.php", "line": "227"}, "connection": "radhe_same", "start_percent": 96.215, "width_percent": 0.832}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 168}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 643}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.654131, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Lead.php:168", "source": "app/Models/Lead.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=168", "ajax": false, "filename": "Lead.php", "line": "168"}, "connection": "radhe_same", "start_percent": 97.047, "width_percent": 1.145}, {"sql": "select * from `tags` where `id` in ('88', '90', '91', '92', '93', '94') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["88", "90", "91", "92", "93", "94", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 168}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 643}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.658478, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Lead.php:168", "source": "app/Models/Lead.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=168", "ajax": false, "filename": "Lead.php", "line": "168"}, "connection": "radhe_same", "start_percent": 98.192, "width_percent": 0.892}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.66935, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 99.084, "width_percent": 0.916}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Tag": {"value": 66, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2857, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1088874081 data-indent-pad=\"  \"><span class=sf-dump-note>create lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088874081\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.299937, "xdebug_link": null}]}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads", "status_code": "<pre class=sf-dump id=sf-dump-946872243 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-946872243\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1486787164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1486787164\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-858988395 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><PERSON><PERSON><PERSON></span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+918965201235</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-30</span>\"\n  \"<span class=sf-dump-key>next_follow_up_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-16</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">lead</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>stage_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"3 characters\">hot</span>\"\n  \"<span class=sf-dump-key>opportunity_info</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>opportunity_source</span>\" => \"<span class=sf-dump-str title=\"12 characters\">social_media</span>\"\n  \"<span class=sf-dump-key>lead_value</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>opportunity_description</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ok</span>\"\n  \"<span class=sf-dump-key>labels</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">91</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">93</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"15 characters\">new_Ok Paruchay</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"12 characters\">new_Mr DEMON</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858988395\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2053468640 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2537</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryFMjlikcg7gkDPivg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1YbTdYOWxrWVV4VjNNc1B2cnR2b0E9PSIsInZhbHVlIjoicWZCcUhOSmgvblNMY2JXS0FmbVkvQXo1RUJKWUtONXJad1dDSjd3VFBSZyt6OVlUZ3J6OEx0bDBRZE5tK09jRUt6QWRvZTMrcXdWMEZXaHEwY3krMHJsOUpGSCtEOW9NN2NhcHhSenVmMEE1VXh1cEh6c0Fqc0l4N3RkSGJQZndtcjdWaTJadWFnK1J2ZUdFRFZFTUxRN1htSFRweDBKUDV4d1BaR1hCd2c4RTJIb3VkMmFtYWVGV1ZjUjdWNlEzelNtOWp1QjUrRlBwdzFsMWZMTzJ2bmFRS0s5c0FWampZaEFwbW0xZGx2OEJwb3U1M1hhYmJuSEEyZzgxc3RIQW5UUUJwT3lmQjZRaGdPMFdUcnFhMTduQ0NYQXlUUWQwaXNuTXBRSzRIN0J0bi9ReEV4WGh2bDlyWm1LcTV0a0VXaDY1REpxYVZoaFNVN2pkYTZEZzUwWU9lMGRHRm43bzdpUXpCcWptbC9EU21HT25NejRrVXlzSlJwNFNrcDlNRktkUStOaU9QbEFteFZTWFFqcTMrZm8zQ213RHZObW5CZi9ZQjV6eHZpYzZSZjhXSFdtL1dkZ1QzdE0rNWRodWpTM2ZzYTNJOUN3N3hpcnZyNEFIdTFjQVNOOFZUbTRBejgwWUo0aTRYSDM2NkhaZmZ2aktBUUdoUlhVOEYvV1IiLCJtYWMiOiIxYmIzYjM5NTViMjVmYjViYTlmODVhMGZjZDM4MTgyYWNkNmI2MDVkODkzY2IwYWIxZjFlZTViZTQ3MjFjOTA4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlNnU3B2Uk5GOUdLSUxLZkZCRXJKK0E9PSIsInZhbHVlIjoidlVYdmdHS3o4NE1sMHJad3VFM1VmNXVCbjRTNklsZU0yTlhJN0M1Yk1DSDZWaUUzWmJvdmJERitGUXkxT3VaSmluZnhaT3llRE9TTnAxck5XOXlBTCtIdlRDYzU0OGNNQ1BmVmYvTitickM5ZWdBeE1qWnFBTkh4N2hyUERwbnd0NGZjUStWWUUxVDdqUlV3WDZsNzIwNjZhRmhUQVdZZVBCTHQwdTVyWGNJMTk2UWhmNk81YUJFdVdyNDRrUWcxQnZvSE5IMHkveFlMend1T1lTbWZCS3dVSnhodjBlOEwwR1NIRW1DY0xYcEN2eDBHSnEyNFBLUis2Yy95M25WNHNMMDdaeEJ0OXhFbStCM2U1TVNyZTc5V0xVWGc0bWNKYzZGVk1FWGpJOVhlbmcvWEpURWVhUVd5YyszN0ZYMkdZam9BL09NQnNoNmJFTlRoaExkVXR6RXNJTG5sdzRPQ0MzQ0xFdUl2M0hhQTl4QzdhcHBHazlTcVlIaVo2dzRWZjV6NGlQSWhiSUl1ckE4UUZMUFBhdnZ3enN1TXVJcHJhakpESGhEemRBaUhzdmZZVVVJMGU5UWtaYkxUaHlrdW9SQVQzaktDK0tiRjBiaVg3MFBISG9nR2c4b1VWbmJRZmUyeWh3VGZ4RHFEMitydHNqSHpMd25uM3JWSGRhMlIiLCJtYWMiOiJiYzI3MDczYTNlZjhlYzIzMmI0ZjRlMTJjYzAwZGY3NmQ1MzNmYzI5OGMyMjkxNGI5MTgxY2UyNWFkYWZlZDA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053468640\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1595816412 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595816412\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2005552484 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:15:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5IaXRNRzVhWU8wU2lrZy92V3l5cXc9PSIsInZhbHVlIjoib2dKOHh4YWZJQnFuQklvYmJUVnBsTS9NaHEyTXVQUE5JZktaL1VNamU1cFRsL2FxSThIbFJMREwyQTc2a3pycExXalJIbGNXSmttdjJRSjIwdTF4TDdKU2ZCWjRWMU95bzhCRnlkWVArZWg1c2tITHpSM2gzQjR4a2N2Z0VlYjlwaGxrbkhOMHh5K0pKcFhOem0wK3d6cVNIb3plenF4Q2cxR1dyV0h6VUV0aWh5SFhNTHkzMDVBUkNWM2J6amZESVZRK05oNXlXenVHQnZ5cEcrRjZvcXBvdDJDdFo3NWtQaitpclhHcEJSTTYvZSsrWkEwRk4zb0RMSE9TUjBFd0tBSWdqQm9aVzBQSkIwWWo3eFk4ZXJFM1hIR2l2TU5CK29ITm1qNm1waFlJMFYzanl3aWkzWjFuZUNmM2JBSTlZWEJxaWlPWXVWNUNrakhoeHBrNHZsSTkrZHpoWEtrVVZtVG9qRjMyUEZ6c1JHYVdUVGIwL2QzbzhQTnE3THNSWXVMNkpLVFVIalJadHBXMjZlNkpJeHV3alBKOFJvei9pS2dHZnkrZGs1MnlUTnBYVE1KVkVBMDJhZWFHUHV0d3VlVGFkVWJyU3NiK1RoRkdkdlMxRkdyckhDQVZyNngwZkcxMXNtYUEwQXJLVFd2YkNadWtkWlo2eEVTL3BhK1kiLCJtYWMiOiIyNmE0NTEyNGZlNzYxZDU4MGJkNTU5YjA1NWQ5ZmEzMzU1YTY5ZmI3YTZkNjQ3MDYyMWFkYjljNGY2NmJmM2UxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:15:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkJsdDFNTUJHQk5uQ2xwaE5EMzdNMXc9PSIsInZhbHVlIjoiTUxIeXhwTVNYVWcwNDZDKzN5ZnE0RVQ3Y2ovbng3WEo0SU50YkorNXU5YmRvSHBiVVZHUkZzRW5CenNWdm1FRVk0UGdOa3o5Qy93UWhWZHFEZ1hlZ3lzL1NzRXo1a1ZwbnFKLzBpaGhRWkx3VDF0YWZZR1FjSm9FRzJxdUgwWG9JSlJCbC9EL3d5WEtOWUI4UDRGazdDR2FmNHU4WU51aVdscmw1M3hBcFNmVWV2UXVBWlJOMTE0bXdJWGdGMERTT3BBT3lvOVB0RU1ZY0hnY0lIcHgrallrQTJ6YlhCUmg4bURCa1pmRGxkOThEMmNQcjVYNjNMR2NpUkFrWEZMT1ptL0ZtS2NxOFVXQWlhUFMyeXFxT1pRLzBmbUcyT3ZtNE02RE4xWE04Qk5CQzA2QzdzbGxxbkpSakZLd09zdEMyYkRGbEw4d3paZUpXV0tySi9OY2FueTZzYW5STDBURm8xMTUwUy9pYlZIemRZYTJ3Y2xBZ3ZURk9BNG5SdkU4a3VnT3FNVjhZSmlKNjhXeUZnOTc0TC9Gem9xd1hKcnh2TFpHdlRiY1hDVDF1WkxkbHhudnRSdi82R1ZIbzFlZklSK2FKNGFQeCtTMmlVVkNtVWZYNEs2K2ZxTDdtUUlsa3hudmNqS0Irb2ZDVDZLT0RwVDRRZXhhZXFNUG9zZjYiLCJtYWMiOiJlN2YwNzZjYmYxYWI3MjM1YTBhZGY2ZmYxNWNkNjNjZDFhOTNkNzIwMGRlNDhiMjliOTQ1MDZjOTEzNmFjOTAzIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:15:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5IaXRNRzVhWU8wU2lrZy92V3l5cXc9PSIsInZhbHVlIjoib2dKOHh4YWZJQnFuQklvYmJUVnBsTS9NaHEyTXVQUE5JZktaL1VNamU1cFRsL2FxSThIbFJMREwyQTc2a3pycExXalJIbGNXSmttdjJRSjIwdTF4TDdKU2ZCWjRWMU95bzhCRnlkWVArZWg1c2tITHpSM2gzQjR4a2N2Z0VlYjlwaGxrbkhOMHh5K0pKcFhOem0wK3d6cVNIb3plenF4Q2cxR1dyV0h6VUV0aWh5SFhNTHkzMDVBUkNWM2J6amZESVZRK05oNXlXenVHQnZ5cEcrRjZvcXBvdDJDdFo3NWtQaitpclhHcEJSTTYvZSsrWkEwRk4zb0RMSE9TUjBFd0tBSWdqQm9aVzBQSkIwWWo3eFk4ZXJFM1hIR2l2TU5CK29ITm1qNm1waFlJMFYzanl3aWkzWjFuZUNmM2JBSTlZWEJxaWlPWXVWNUNrakhoeHBrNHZsSTkrZHpoWEtrVVZtVG9qRjMyUEZ6c1JHYVdUVGIwL2QzbzhQTnE3THNSWXVMNkpLVFVIalJadHBXMjZlNkpJeHV3alBKOFJvei9pS2dHZnkrZGs1MnlUTnBYVE1KVkVBMDJhZWFHUHV0d3VlVGFkVWJyU3NiK1RoRkdkdlMxRkdyckhDQVZyNngwZkcxMXNtYUEwQXJLVFd2YkNadWtkWlo2eEVTL3BhK1kiLCJtYWMiOiIyNmE0NTEyNGZlNzYxZDU4MGJkNTU5YjA1NWQ5ZmEzMzU1YTY5ZmI3YTZkNjQ3MDYyMWFkYjljNGY2NmJmM2UxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:15:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkJsdDFNTUJHQk5uQ2xwaE5EMzdNMXc9PSIsInZhbHVlIjoiTUxIeXhwTVNYVWcwNDZDKzN5ZnE0RVQ3Y2ovbng3WEo0SU50YkorNXU5YmRvSHBiVVZHUkZzRW5CenNWdm1FRVk0UGdOa3o5Qy93UWhWZHFEZ1hlZ3lzL1NzRXo1a1ZwbnFKLzBpaGhRWkx3VDF0YWZZR1FjSm9FRzJxdUgwWG9JSlJCbC9EL3d5WEtOWUI4UDRGazdDR2FmNHU4WU51aVdscmw1M3hBcFNmVWV2UXVBWlJOMTE0bXdJWGdGMERTT3BBT3lvOVB0RU1ZY0hnY0lIcHgrallrQTJ6YlhCUmg4bURCa1pmRGxkOThEMmNQcjVYNjNMR2NpUkFrWEZMT1ptL0ZtS2NxOFVXQWlhUFMyeXFxT1pRLzBmbUcyT3ZtNE02RE4xWE04Qk5CQzA2QzdzbGxxbkpSakZLd09zdEMyYkRGbEw4d3paZUpXV0tySi9OY2FueTZzYW5STDBURm8xMTUwUy9pYlZIemRZYTJ3Y2xBZ3ZURk9BNG5SdkU4a3VnT3FNVjhZSmlKNjhXeUZnOTc0TC9Gem9xd1hKcnh2TFpHdlRiY1hDVDF1WkxkbHhudnRSdi82R1ZIbzFlZklSK2FKNGFQeCtTMmlVVkNtVWZYNEs2K2ZxTDdtUUlsa3hudmNqS0Irb2ZDVDZLT0RwVDRRZXhhZXFNUG9zZjYiLCJtYWMiOiJlN2YwNzZjYmYxYWI3MjM1YTBhZGY2ZmYxNWNkNjNjZDFhOTNkNzIwMGRlNDhiMjliOTQ1MDZjOTEzNmFjOTAzIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:15:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005552484\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-6238651 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6238651\", {\"maxDepth\":0})</script>\n"}}