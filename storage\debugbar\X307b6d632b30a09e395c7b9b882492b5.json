{"__meta": {"id": "X307b6d632b30a09e395c7b9b882492b5", "datetime": "2025-08-02 08:18:54", "utime": **********.799892, "method": "GET", "uri": "/leads/pipeline-stages?pipeline_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[08:18:54] LOG.info: getPipelineStages called {\n    \"pipeline_id\": \"23\",\n    \"user_id\": 79,\n    \"creator_id\": 79,\n    \"request_data\": {\n        \"pipeline_id\": \"23\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.769304, "xdebug_link": null, "collector": "log"}, {"message": "[08:18:54] LOG.info: Stages query result {\n    \"pipeline_id\": \"23\",\n    \"stages_count\": 5,\n    \"stages\": [\n        {\n            \"id\": 86,\n            \"name\": \"New\",\n            \"order\": 0\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Qualified\",\n            \"order\": 1\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Discussion\",\n            \"order\": 2\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Negotiation\",\n            \"order\": 3\n        },\n        {\n            \"id\": 90,\n            \"name\": \"Won\\/Lost\",\n            \"order\": 4\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.780185, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754122733.342164, "end": **********.799938, "duration": 1.4577739238739014, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1754122733.342164, "relative_start": 0, "end": **********.598377, "relative_end": **********.598377, "duration": 1.2562129497528076, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.598405, "relative_start": 1.2562408447265625, "end": **********.799942, "relative_end": 4.0531158447265625e-06, "duration": 0.2015371322631836, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46530544, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET leads/pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@getPipelineStages", "namespace": null, "prefix": "", "where": [], "as": "leads.pipelineStages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3021\" onclick=\"\">app/Http/Controllers/LeadController.php:3021-3077</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021150000000000002, "accumulated_duration_str": "21.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.715466, "duration": 0.016390000000000002, "duration_str": "16.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 77.494}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7579608, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 77.494, "width_percent": 3.735}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '23' and `created_by` = 79 order by `order` asc", "type": "query", "params": [], "bindings": ["23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 3048}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.770368, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:3048", "source": "app/Http/Controllers/LeadController.php:3048", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=3048", "ajax": false, "filename": "LeadController.php", "line": "3048"}, "connection": "radhe_same", "start_percent": 81.229, "width_percent": 18.771}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-1208922601 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1208922601\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-472415093 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472415093\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-223629322 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-223629322\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-390176612 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ii9Cd1NmUjRzV3hsd1BEd3hmS0lra3c9PSIsInZhbHVlIjoidDhyRldZR2doOUttS1lMMm91UVhXbXBBTUhRTUpwSlpaeXVuWTg1SWZXTDVvbkljaWVscG1NNEkwR0tqYkp3YXEyMmZuaDh0dmVXdlJqaDFzcHdVMkZsZ1dpRVNaM3hEUUFUSk9Fdm5wZVU3RURZZktaR1pyeEo5SCtyMFh2cTZuY0tKUmI0RTF4Q0JEKzRTK2NBWVhHbDc5UzhITHgzM3UwSmQ1U1dyL1NiYUQ0MCtXazlwK3FXZlZacUdwS2d3Y0l0Y2kram5Ra2NQQmx3T0pzK0dXNHZFVkxvblhja0VhT2k3Wk5Bamh2c2JudzBlVkJEMkRBTG5uT0pPK3JzTzVuK05BWkgwZi9jWDR4aktlVXJKUWhzb0pkVHZOMVNsem5YR2tOVEYzY0dwQzhnTzg4Y2E4VzdzVmVnN01OcG12cjJRMS9IeG5Fb2p0SzhXcmJzMlVBL2J1MDVZT3lrSnQxdUtibFk4cEFUcDFhVlN6YzlJazlmNHlBWmRCNEZXQitNN3BVZUJKSnErM0tQc1dDd1ljWjFQNWtnR0ZEeGYzTC9qRzNLR3FNb3IzZzFtZTAzVFRneURubUdRY3YxTG4weTdhM2IvN3d0UUxaSzZnbnlTWjJzOTcyMU0wV3NvZGp2c0NuRU55NDRXcVdHck5Ud1ZuaHdmeGxxeE5GSWEiLCJtYWMiOiJhMTM0NTEyNDUzYWY1OTA2NmU2ZDEzMzY3YzE5N2NhMTI5MDhhMWYwYTM3NWIxOWQ1YWFkMTQ5NWQxNmFlMzk3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkhFLzJPUzdFTGVrUVBJa1pzYmhOSVE9PSIsInZhbHVlIjoidWtFOGFQWGY4QjdSUkFMMkdzbHV0TlczdDB3cEpDWEV2NVhmRHhacUNZaWtJQ1MzQmxMdFhHYWZWTDFlb0FuNmxOV3U2RVVGb0RNTlhDOTlYd2RqR2xDYkdPdlVkaXgyeFEwQm95dTBxbU9kUWFLOXJscXcwN0c3MHRZa1Y4dHN6aS9uL0ZLTWdJS01ORlJpcFZyelBnazRnM0k0WjlMUW0vL21hamZNRjVJSW5VY1BrZEFWelFoN0diZzJyRGZQdVdBTWt2bXB0UHlIaGlqdC9xNlR6bUFvSWlOc2RrZm8wa3lxMitnZnNvUmg0cnNMLzBmZXREci9mWGFOL3N6MnEzQ1d6bHA0MGcwSGg5TnovNVdQNEdCbU1oaG5TNU5MTzdsY0orMjI4bkJiNXdLcktBV2J1ZmJOdTZ3WDhJM0VuTXIrQlRDbmtOWHZJSTBydTJYRWFmV1Q1a0FIMkxVd1k4c0tRNnJnT1BDT0xscjduYnNXcktRY1lSdHAwUE5nYUFBNGkzc2dsTzdrMEplNUtQU281bkpFSE5mRW1hYlFZaytQR2UyK1k2Z2ZwZnNSZk5RRmo5QlpwcDU3Lzd3eU5TOVlVY1NIOTQ2cERmSUJOcEhibHlVQzBqT2oreVRPZ2dzQXNvOS9aTVFIb3FqVngrcjdFUERnSDZSMk1qZlEiLCJtYWMiOiJjODk0N2M3YmJhNDc0MTIyOGZlZjcyYzc3NTZjODJiYzcwZDBmOGI2NTk5M2QzNTZlNjAyZDAxMWU0Y2M5OGM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390176612\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-617653325 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617653325\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1188511141 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:18:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVtbktMa2t2a0NZVEJWNWJDWGR5QkE9PSIsInZhbHVlIjoiazdmR2kxbnJwWmNzOHdqa1MwQjVJMG1VMGUrRnJZOFhEZTRhbVppUkc1K0VBSzVJQlN6R3paSEViZFdRVGdORHdZTE5mcjRxamNkN0NYcVdrcnhNT1FZL1BmMmdEMWVuYXRhalJGZithREs1TW9qTy9GVmZsdEdlNnRJa29tZ2xBNUh0UTNBRnFPQjVSZUJtVFdmT2VPZWZibFdIUVU2Y2NjZjE5cGdHUEJZRWMvU1hhWVN1NjdLU2xVZGRUZDFXb3JaZlR4eTNjaXhXRHVBclVRSjdJM0VmU1NKY1o3ZllPVEY4TWY3aVhROVRPTkM1dnhUalozd3dtbUtVQWdNQWNhLytqdVpVWUMrMDJQbVRQZEFEUmdQN1BLc3ZyK2pHeURPaFRTaEp6VXAzY0VnT0dSbk1wSmszZUwycCt5dXhsOHdtV3k2QXk0UkVFQXNCV29hZjA5eWwxU3R1Y1I0cUlHa3owekxIK0N2T2NMRnFKYWxhWmZrNmgwYVcyZTdLaU5VMzQ4OVFIeVdkeFkyQjBCTlphY0J2M0JBTk9oeUxKSnp4RjQyTnY0K0JJZTZhOFRjT2lSbGw4UkRyQm42VzlONEo3NzRpWHQza1JNbElCMkRNOUFvdkttbktGTTMxdTdQTElOby9XK01LOHJ4T244aXQ5UzhCcHNrcWVuRkciLCJtYWMiOiI0NWMxNWE5YzliMjAxYjU2MzdjZjYzNjcwOTRiNzhlMjBiYmIxZTZkNDViYWMyOWUzM2NjMzk0NWRkNzk0NTQ4IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:18:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImtCU29rOU8vQkJlandEckVzOFBqQlE9PSIsInZhbHVlIjoielI3ZmFUK0U0R28yaWNQOHRWMkl5RlpnSno2RmVUSHM2SGljODA1dDBwQXlwc0JtNzJDZ2ZMd2YzTDh0djhMOUR1QWRFZjNrSGFmMmU0enF1T204Ri81bzZBaWcwc29qQ0hyRWRrbkt5MHlocGpVSnV1cFdsalRwZEtDL08rQW9yK0JLeHRMY01zSXdSMS9YcloxajdPTzc1Z0QvUkFFV1QyYWxmVFV3Q3RLMVFiUlVQdzJzMEo4b3IwSWtYeHVtdElrNnV2YWVNd05uQjg0TzZka1UzejN2SnFOMnhKOEdRTFNxa3RuS0pINm9NL0ZTTEE1MGVpcEIzOE9UUFRTTFM2dVFjVm9mbktCUTlFdFhOZ2VEb2xzYzJsWGpQL3Qrai9lMWRUK2dwSTNzQjRmMHd4aHJZblRYSFp1NStMV3ZZUmgzVGhWbGRKYitHK1lFOGUzcERXSTdOeWc2NXgwWDRXTFMzRzZEZ1ZybFp5dWRhVDlrYjBjQUxjeDNKc0xHUmhMWWZpS3M4UU1DTnBmNWZZUG51bS90dFUrVnR2VkU1V2NTdzI4UDYrQmdDdlNiMEhOU3V3Nm55VEpDaFhhWTdvT1didW83NUljOEo5Wk5KOHZpN2JxaGFIcTY3SnZEdllGa0lJRXlDSU9oWGYxMlBQYW5sVGN6K2FjOWlYL3ciLCJtYWMiOiIyZTNmMDk0YzliMGI3YjBhM2E0YjdkOWQ2ZDBlN2ZkYzFhZTZiNGE2YWI1NmM0ZDBmODZiYjMzZDVlNzhhNGRiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:18:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVtbktMa2t2a0NZVEJWNWJDWGR5QkE9PSIsInZhbHVlIjoiazdmR2kxbnJwWmNzOHdqa1MwQjVJMG1VMGUrRnJZOFhEZTRhbVppUkc1K0VBSzVJQlN6R3paSEViZFdRVGdORHdZTE5mcjRxamNkN0NYcVdrcnhNT1FZL1BmMmdEMWVuYXRhalJGZithREs1TW9qTy9GVmZsdEdlNnRJa29tZ2xBNUh0UTNBRnFPQjVSZUJtVFdmT2VPZWZibFdIUVU2Y2NjZjE5cGdHUEJZRWMvU1hhWVN1NjdLU2xVZGRUZDFXb3JaZlR4eTNjaXhXRHVBclVRSjdJM0VmU1NKY1o3ZllPVEY4TWY3aVhROVRPTkM1dnhUalozd3dtbUtVQWdNQWNhLytqdVpVWUMrMDJQbVRQZEFEUmdQN1BLc3ZyK2pHeURPaFRTaEp6VXAzY0VnT0dSbk1wSmszZUwycCt5dXhsOHdtV3k2QXk0UkVFQXNCV29hZjA5eWwxU3R1Y1I0cUlHa3owekxIK0N2T2NMRnFKYWxhWmZrNmgwYVcyZTdLaU5VMzQ4OVFIeVdkeFkyQjBCTlphY0J2M0JBTk9oeUxKSnp4RjQyTnY0K0JJZTZhOFRjT2lSbGw4UkRyQm42VzlONEo3NzRpWHQza1JNbElCMkRNOUFvdkttbktGTTMxdTdQTElOby9XK01LOHJ4T244aXQ5UzhCcHNrcWVuRkciLCJtYWMiOiI0NWMxNWE5YzliMjAxYjU2MzdjZjYzNjcwOTRiNzhlMjBiYmIxZTZkNDViYWMyOWUzM2NjMzk0NWRkNzk0NTQ4IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:18:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImtCU29rOU8vQkJlandEckVzOFBqQlE9PSIsInZhbHVlIjoielI3ZmFUK0U0R28yaWNQOHRWMkl5RlpnSno2RmVUSHM2SGljODA1dDBwQXlwc0JtNzJDZ2ZMd2YzTDh0djhMOUR1QWRFZjNrSGFmMmU0enF1T204Ri81bzZBaWcwc29qQ0hyRWRrbkt5MHlocGpVSnV1cFdsalRwZEtDL08rQW9yK0JLeHRMY01zSXdSMS9YcloxajdPTzc1Z0QvUkFFV1QyYWxmVFV3Q3RLMVFiUlVQdzJzMEo4b3IwSWtYeHVtdElrNnV2YWVNd05uQjg0TzZka1UzejN2SnFOMnhKOEdRTFNxa3RuS0pINm9NL0ZTTEE1MGVpcEIzOE9UUFRTTFM2dVFjVm9mbktCUTlFdFhOZ2VEb2xzYzJsWGpQL3Qrai9lMWRUK2dwSTNzQjRmMHd4aHJZblRYSFp1NStMV3ZZUmgzVGhWbGRKYitHK1lFOGUzcERXSTdOeWc2NXgwWDRXTFMzRzZEZ1ZybFp5dWRhVDlrYjBjQUxjeDNKc0xHUmhMWWZpS3M4UU1DTnBmNWZZUG51bS90dFUrVnR2VkU1V2NTdzI4UDYrQmdDdlNiMEhOU3V3Nm55VEpDaFhhWTdvT1didW83NUljOEo5Wk5KOHZpN2JxaGFIcTY3SnZEdllGa0lJRXlDSU9oWGYxMlBQYW5sVGN6K2FjOWlYL3ciLCJtYWMiOiIyZTNmMDk0YzliMGI3YjBhM2E0YjdkOWQ2ZDBlN2ZkYzFhZTZiNGE2YWI1NmM0ZDBmODZiYjMzZDVlNzhhNGRiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:18:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188511141\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1878260415 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878260415\", {\"maxDepth\":0})</script>\n"}}