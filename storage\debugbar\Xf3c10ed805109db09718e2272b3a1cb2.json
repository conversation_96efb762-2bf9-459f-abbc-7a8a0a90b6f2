{"__meta": {"id": "Xf3c10ed805109db09718e2272b3a1cb2", "datetime": "2025-08-02 08:28:52", "utime": **********.163185, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754123330.190376, "end": **********.163268, "duration": 1.9728920459747314, "duration_str": "1.97s", "measures": [{"label": "Booting", "start": 1754123330.190376, "relative_start": 0, "end": 1754123331.904992, "relative_end": 1754123331.904992, "duration": 1.714616060256958, "duration_str": "1.71s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754123331.905061, "relative_start": 1.****************, "end": **********.163273, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kPshlPRiYUMgkcoH1L3SgbcLiw6cLgOo0EGkT5LX", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-413776732 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-413776732\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-125306944 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-125306944\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-527445592 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-527445592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1022847342 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022847342\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1545409389 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1545409389\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-828738762 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:28:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZwMnJFK0phdDViRnFsT2VsOVo4TlE9PSIsInZhbHVlIjoiUXh3SGhzY3k1NERSd09IS1JQdXZhcFlpRFlCSE9XUXFOTDNuQlhWbVloNEIzVWZja05FYlNqaTZESGs2eittbGxZZC9kenc5V2ZWNzdkV1JuL3RFRHhvUDhFTXhSSnBCbmVYck1uQzBWcSs1eUczaHdoN3FIcHNTZmN3T0ozcHB0b1VQUkdMUDkwOGxjVDM4aUFtUnVrbktoK3FHWGo2Um11MCtVcHBWMFZDcXhHelBMVnppTmJJa05BSEwwWUU2ckUwK2JUOGVGNnhBc1k3NzhuMS94MzNUaVNIdEFHb3RWZC9YaFhvQU8vYVBRbWZwQmRHUGxMRGl2MUsxNnk3Q1JFNFAza21pSEw4VHpaSEZQTmJwYmE3cTR2UGdNTGt2YzNiSmlHcWlmV3BsWHlsaGVMT2kza1ZZUy9GTFRqeXI0bStVdFM0TjRkRU9KbE53ZXNYRGREZlpWY1FFbWp6MUYreGZTd1lBQjBHNytXU3dkckpGVXR4RDNaL242QlQrQ3RQbncwUEJwdDRKTlAxZnFqS01EMitMVnRzM3BSMFM0NCs5WEp1SHZ1WVpSMHRqcTRZUnYxUlZ1Y2lNS3lFeGVERUR6Q01lVy9ZZ05VWTVwQmo4K21ZU1k1R0JFRDFjRGNxM3NaYTdhUCt6WGdGRWZMWjY4UWhjVVpzV0Q2dEQiLCJtYWMiOiI3NzYyZWM0ZmJiZmU0MTZiOTNiZTA5MzBkNjJmZjllNzYyNmQ5ZjU2NjQ5Yjc4NDJkNGI0OTUwMGRjZjFiNmZhIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:28:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjJuOTBKT2ZQZ1hmWG4rNWVIYktMTlE9PSIsInZhbHVlIjoiU2lmRlhQcUE2ajROY29SUmxUOGxRR3RqbVpwNDBpVjhOLys1TDhCMTZXeG9EaWxRRE9lNVJJa2xDdmNJRFVuSUtUT1NPNVQ0R1hzcUNUYmJqZVdUSDFIbEEreEZTcEVpdTgrWEQzT29IWWVvQWkrN2dsWVJyZXNhRDFyNVovUmRiQ1lkZ2kzMVJBaURvUXZ1TXpHamtkNzBzTmJqRU9JT1NjWUdmYnZqK0pMUW9zN0M5UWdqQ0RJeUdtd2hTOVFLWU5kaWpmU3hEb0lJeVdIdGZlTG93OFdWR2FncjNxNmtkNGJVdzExYWMyektOOXMxSDN0cWdYYTNEK3MyOGlrZVhEK1RUNStnWS9Oa2tORVo4NklySWd1K0ZzUm1iUHV3VkFYYXFDZnhEa3JqamoxZnd0UktRT1R3NXYzU05GQ2MraHBQcEpSb0NBUndIdmNqVTFoaEpyNkdCUHhZcnBrTnN2UGpuaW52d0c4Zjc0V3Qzbmo4S2pFWnNReXkvdzFHYjk0VkVuTXNhMHJhZDNGZWZWOTJuLzhXeklucTcvR2VQSDhWcFhlRjhXc3ppS3hxZkdmNjZkS29sVjhpZDVCN0ZZdlAwOTB4QjF3aEdXeHp0b3hJSmUxaWpvUy9Ha2xIb3dRK0ZTdng5TzBQcjM0OThLRnhScHJ1c1lyRzhJeHUiLCJtYWMiOiIyNGNmOTI1MzJiNjBkZmI4ZWEyNTY5MTM2NTAyMThmZGY4NjZhZTBmMjVjNjliM2Y0MWNhYmNjY2MxNzRlZWQ5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:28:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZwMnJFK0phdDViRnFsT2VsOVo4TlE9PSIsInZhbHVlIjoiUXh3SGhzY3k1NERSd09IS1JQdXZhcFlpRFlCSE9XUXFOTDNuQlhWbVloNEIzVWZja05FYlNqaTZESGs2eittbGxZZC9kenc5V2ZWNzdkV1JuL3RFRHhvUDhFTXhSSnBCbmVYck1uQzBWcSs1eUczaHdoN3FIcHNTZmN3T0ozcHB0b1VQUkdMUDkwOGxjVDM4aUFtUnVrbktoK3FHWGo2Um11MCtVcHBWMFZDcXhHelBMVnppTmJJa05BSEwwWUU2ckUwK2JUOGVGNnhBc1k3NzhuMS94MzNUaVNIdEFHb3RWZC9YaFhvQU8vYVBRbWZwQmRHUGxMRGl2MUsxNnk3Q1JFNFAza21pSEw4VHpaSEZQTmJwYmE3cTR2UGdNTGt2YzNiSmlHcWlmV3BsWHlsaGVMT2kza1ZZUy9GTFRqeXI0bStVdFM0TjRkRU9KbE53ZXNYRGREZlpWY1FFbWp6MUYreGZTd1lBQjBHNytXU3dkckpGVXR4RDNaL242QlQrQ3RQbncwUEJwdDRKTlAxZnFqS01EMitMVnRzM3BSMFM0NCs5WEp1SHZ1WVpSMHRqcTRZUnYxUlZ1Y2lNS3lFeGVERUR6Q01lVy9ZZ05VWTVwQmo4K21ZU1k1R0JFRDFjRGNxM3NaYTdhUCt6WGdGRWZMWjY4UWhjVVpzV0Q2dEQiLCJtYWMiOiI3NzYyZWM0ZmJiZmU0MTZiOTNiZTA5MzBkNjJmZjllNzYyNmQ5ZjU2NjQ5Yjc4NDJkNGI0OTUwMGRjZjFiNmZhIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:28:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjJuOTBKT2ZQZ1hmWG4rNWVIYktMTlE9PSIsInZhbHVlIjoiU2lmRlhQcUE2ajROY29SUmxUOGxRR3RqbVpwNDBpVjhOLys1TDhCMTZXeG9EaWxRRE9lNVJJa2xDdmNJRFVuSUtUT1NPNVQ0R1hzcUNUYmJqZVdUSDFIbEEreEZTcEVpdTgrWEQzT29IWWVvQWkrN2dsWVJyZXNhRDFyNVovUmRiQ1lkZ2kzMVJBaURvUXZ1TXpHamtkNzBzTmJqRU9JT1NjWUdmYnZqK0pMUW9zN0M5UWdqQ0RJeUdtd2hTOVFLWU5kaWpmU3hEb0lJeVdIdGZlTG93OFdWR2FncjNxNmtkNGJVdzExYWMyektOOXMxSDN0cWdYYTNEK3MyOGlrZVhEK1RUNStnWS9Oa2tORVo4NklySWd1K0ZzUm1iUHV3VkFYYXFDZnhEa3JqamoxZnd0UktRT1R3NXYzU05GQ2MraHBQcEpSb0NBUndIdmNqVTFoaEpyNkdCUHhZcnBrTnN2UGpuaW52d0c4Zjc0V3Qzbmo4S2pFWnNReXkvdzFHYjk0VkVuTXNhMHJhZDNGZWZWOTJuLzhXeklucTcvR2VQSDhWcFhlRjhXc3ppS3hxZkdmNjZkS29sVjhpZDVCN0ZZdlAwOTB4QjF3aEdXeHp0b3hJSmUxaWpvUy9Ha2xIb3dRK0ZTdng5TzBQcjM0OThLRnhScHJ1c1lyRzhJeHUiLCJtYWMiOiIyNGNmOTI1MzJiNjBkZmI4ZWEyNTY5MTM2NTAyMThmZGY4NjZhZTBmMjVjNjliM2Y0MWNhYmNjY2MxNzRlZWQ5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:28:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828738762\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-808404991 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kPshlPRiYUMgkcoH1L3SgbcLiw6cLgOo0EGkT5LX</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808404991\", {\"maxDepth\":0})</script>\n"}}