{"__meta": {"id": "Xfab2d8f4ffbfba40bf117d0203cffb9d", "datetime": "2025-08-02 10:45:17", "utime": **********.484146, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754131516.490071, "end": **********.484214, "duration": 0.994143009185791, "duration_str": "994ms", "measures": [{"label": "Booting", "start": 1754131516.490071, "relative_start": 0, "end": **********.40036, "relative_end": **********.40036, "duration": 0.9102890491485596, "duration_str": "910ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.400379, "relative_start": 0.****************, "end": **********.484219, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "83.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tAowyxt1IHSANG7BB7KW8yzEGUwRziEYkcThyAs5", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-649942090 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-649942090\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2049335676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2049335676\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1102271311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1102271311\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1858760331 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858760331\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1228657616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1228657616\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-400385899 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:45:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNIS0lGWFovQnB0cVhCTG40T2NiS1E9PSIsInZhbHVlIjoienpaLzlsRnhjbWRMM1pob2NvUndDdjhaZ2pRZk84NFJVMVlkYndySlNLd3JtODEzVWlZc0FMNWdranFDTFFQUTE3ZzVJU1lUY1hWb3Exa2RqaXFoaHZNQVBtRGkvRlhVdzVtNXVucUVtTE5BMmFKT25ldWZoZzRPNlVBNzgzYURiWk5mWjFEeDBKcGwxcEtXb2FHME5MdTgvOHpUTjFxTnBib2QwYnNQTlZNQVRmQTAyS1JGQVVoZEhYcHZIeXZ3eXFWYkp3NWFHQXRjSFZkckFHSUp3S1loSElhN3dhUTQ1MVhUQllYS0RIZ1lnam9XcHJ5SVZsVkpMaG42RFFxZ2xpQ1c5K1Z6aGRWU0dWcjNKeE03V042Q29yVmZnc3FqWStmVVNNb055UE5XYWNPaEpQbWVyZkNnMmY3ZTdiZjFEd085Z3dzeUFDaVlGSHIyTmZWeVhNanhEMVp2NDhNRWYrZC9JM1VUUklTL0dxZkNDSllGM0U5Z1BZZVowZWljaUMrcEhqVkM0VUVBNDBoTWZIRGYrUDNObFUzTGh5UTlBMng5YUU5dzBXZUxXdElVMHE5UWszU2dYY2FKdlMzdkRlOWdvaXV1NytTOHJXd252Sk1CNlpzQ2VVVDFjNUl6ZHllQStVLzFnekVPMkRHTkErSnVIRlYvbnYxVmJ4UTMiLCJtYWMiOiI5MDI5OTM3YjEyOTQwMDdhZDZiMmQ3YjQ1MzZmYzE0YjcyYTk3NzQ2YTIyZWFmNzU4MGVhNTU5NjhiZTc4YjgxIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:45:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlNrSVFleWp2RDZXVm0wUWxaM3FDVVE9PSIsInZhbHVlIjoiVmxzQ2w5MWt3OHIzU0JFaVg1TndndzZtYVV0NXdnUmJiMnc2NTJHb09kRTFCYWtoUVRRbTcxOTlKNkNDczB1VHNMc3lHL2dNTmNHenhaZzJkYlh5ZTJ4OEFBME1xTURHOE9mOXFKVmdSWi9NclZCOW00NlBJdy9PQnc5Mm5vRDlUVnZ5MWF2SW5wREtzQjNXUDZCT1hDYk1VOHhUcTE5UWVCRHU3SWVIaVFNbDdHV1FQUnBNbkN0REpSbW51SGk3dVI3N3g0b25NeXdzTHFqaXYzK2ZwSXVtenFZRkVnczYwcERqVHlpM1hzb2laYWhUNFdSbFRFbU1Mc3U2aGVmaGZiVXRlb3lZZCtQMXB0aWt3LzNQY1cwRGpRRmZ2YkZWUm0rQ0t3ODNnYjF2ZFZpM09pcS9aSFUxQ2tXRFp3V3l5WHFvU2c5K2o0V1c2aWFSejdldnhaaS9KYW9tK1J1UHp3TkhQTUQ1Rm04M20rVXlPRHdNM1d3RVFIdUNJdGYrUFVBR2R3cjl0SXpDamZ4c1BqS1U1dWJSbTQ1dERVcHc1bzdxRENQb3VTdjh0MHoyZ1FrSlFGcE8vdDBkVkp4NFRYK3V2TFY5dXZQbGt1T2dJU0d6UUtPWEp0NU1ML3JkSUNtRVpsZE1hODI1T3lLdG9tcHZPSTVJclNJcU5za2kiLCJtYWMiOiJlZmU4M2Q1NWI0NTFhOWU1NDc4Y2E0NDU4N2VjYzA4ZTQ0ZjA3MzhjN2U0Mzk2MGUyNjgxY2RiZTEwOGI0MzI0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:45:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNIS0lGWFovQnB0cVhCTG40T2NiS1E9PSIsInZhbHVlIjoienpaLzlsRnhjbWRMM1pob2NvUndDdjhaZ2pRZk84NFJVMVlkYndySlNLd3JtODEzVWlZc0FMNWdranFDTFFQUTE3ZzVJU1lUY1hWb3Exa2RqaXFoaHZNQVBtRGkvRlhVdzVtNXVucUVtTE5BMmFKT25ldWZoZzRPNlVBNzgzYURiWk5mWjFEeDBKcGwxcEtXb2FHME5MdTgvOHpUTjFxTnBib2QwYnNQTlZNQVRmQTAyS1JGQVVoZEhYcHZIeXZ3eXFWYkp3NWFHQXRjSFZkckFHSUp3S1loSElhN3dhUTQ1MVhUQllYS0RIZ1lnam9XcHJ5SVZsVkpMaG42RFFxZ2xpQ1c5K1Z6aGRWU0dWcjNKeE03V042Q29yVmZnc3FqWStmVVNNb055UE5XYWNPaEpQbWVyZkNnMmY3ZTdiZjFEd085Z3dzeUFDaVlGSHIyTmZWeVhNanhEMVp2NDhNRWYrZC9JM1VUUklTL0dxZkNDSllGM0U5Z1BZZVowZWljaUMrcEhqVkM0VUVBNDBoTWZIRGYrUDNObFUzTGh5UTlBMng5YUU5dzBXZUxXdElVMHE5UWszU2dYY2FKdlMzdkRlOWdvaXV1NytTOHJXd252Sk1CNlpzQ2VVVDFjNUl6ZHllQStVLzFnekVPMkRHTkErSnVIRlYvbnYxVmJ4UTMiLCJtYWMiOiI5MDI5OTM3YjEyOTQwMDdhZDZiMmQ3YjQ1MzZmYzE0YjcyYTk3NzQ2YTIyZWFmNzU4MGVhNTU5NjhiZTc4YjgxIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:45:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlNrSVFleWp2RDZXVm0wUWxaM3FDVVE9PSIsInZhbHVlIjoiVmxzQ2w5MWt3OHIzU0JFaVg1TndndzZtYVV0NXdnUmJiMnc2NTJHb09kRTFCYWtoUVRRbTcxOTlKNkNDczB1VHNMc3lHL2dNTmNHenhaZzJkYlh5ZTJ4OEFBME1xTURHOE9mOXFKVmdSWi9NclZCOW00NlBJdy9PQnc5Mm5vRDlUVnZ5MWF2SW5wREtzQjNXUDZCT1hDYk1VOHhUcTE5UWVCRHU3SWVIaVFNbDdHV1FQUnBNbkN0REpSbW51SGk3dVI3N3g0b25NeXdzTHFqaXYzK2ZwSXVtenFZRkVnczYwcERqVHlpM1hzb2laYWhUNFdSbFRFbU1Mc3U2aGVmaGZiVXRlb3lZZCtQMXB0aWt3LzNQY1cwRGpRRmZ2YkZWUm0rQ0t3ODNnYjF2ZFZpM09pcS9aSFUxQ2tXRFp3V3l5WHFvU2c5K2o0V1c2aWFSejdldnhaaS9KYW9tK1J1UHp3TkhQTUQ1Rm04M20rVXlPRHdNM1d3RVFIdUNJdGYrUFVBR2R3cjl0SXpDamZ4c1BqS1U1dWJSbTQ1dERVcHc1bzdxRENQb3VTdjh0MHoyZ1FrSlFGcE8vdDBkVkp4NFRYK3V2TFY5dXZQbGt1T2dJU0d6UUtPWEp0NU1ML3JkSUNtRVpsZE1hODI1T3lLdG9tcHZPSTVJclNJcU5za2kiLCJtYWMiOiJlZmU4M2Q1NWI0NTFhOWU1NDc4Y2E0NDU4N2VjYzA4ZTQ0ZjA3MzhjN2U0Mzk2MGUyNjgxY2RiZTEwOGI0MzI0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:45:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400385899\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-303907158 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tAowyxt1IHSANG7BB7KW8yzEGUwRziEYkcThyAs5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303907158\", {\"maxDepth\":0})</script>\n"}}