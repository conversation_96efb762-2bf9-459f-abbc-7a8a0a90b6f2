{"__meta": {"id": "X7bdbf9bef6d26b277711e381a4695b52", "datetime": "2025-08-02 09:38:39", "utime": **********.252137, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754127518.317403, "end": **********.252165, "duration": 0.9347620010375977, "duration_str": "935ms", "measures": [{"label": "Booting", "start": 1754127518.317403, "relative_start": 0, "end": **********.165522, "relative_end": **********.165522, "duration": 0.8481190204620361, "duration_str": "848ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.165558, "relative_start": 0.****************, "end": **********.252167, "relative_end": 1.9073486328125e-06, "duration": 0.**************, "duration_str": "86.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "vUsiAx5ojM5LADxkzU6QZBulCHm2HL80mig6iju4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2063070151 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2063070151\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1254383040 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1254383040\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1641674900 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1641674900\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-161579562 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161579562\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1938091270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1938091270\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-577394924 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:38:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjkreWhET0NQQm15Si9STTVCaDZlSmc9PSIsInZhbHVlIjoiNjhDYjEyd08rUlRreTJrVitCUFRxQThMQjRGMG9IMThKN2pBeXpUMENFYUd1NDMzWWlsM2prSnBValZOVEVDTFpRWHQvRWdoMDQrY2F5VzJScXJSM0JvRVFMSEpxaHFJUjg2V0paeDAxTVloMjFJRlB6c0lyOHRnTmlQNWNRbUNPTktUTDFaampRQnQxb0lXeWVRV0F5N1dkSVhCWEpZZUg4bm5xT0xwVmhvS2trbHpXenNMeDFRODNLTEdJTUFZU2RpTVFWQlVsbVhraSswK1JNR2c1SkphYWRGeUxUR1Vjb3ZVNXZHakIzZVdxYW84S01kaGc2bEpqa2FVdjFqOWNOcGpWVml4Tm5aOFBTelA0aExQSzYrdmNNZmFNb05OOXRoalFMQUZEWi9OekIxdDJBOHNweENKaTkvZ3dOR1NYY0FlKzJCc3JjS0pCT2dBelVxb2VSVVRoSHc5WFNKV1M2OSsrTjlGMysxbVB2SlQyK0w4dDQrWVF3eTNuZFpobkVlZWdnZ09qa2ZpN2pINitrZjI5QlJlcVVQZTNXRnhXQVZjaENmVy9OOUMvbU5ibEtrUVVreWFRNmU4Q3JuSWxyUlBHWlNNM1hRTDVxMXhBdUsvSFQydXQ1dndSdk9Ga1R0WVV3VDRoUkxpRzErNUg4UU04eEszcjczZnV3akUiLCJtYWMiOiJlYWJjNGUwZjc2ZjU4YjRjMGUwYWE3NmEwYzkzNmY4ZDdhODE5ODkzZmE0MTE1YzNjN2YyN2E5ZTkxMjQ3NDUwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:38:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRmSEpZaXNKTzVMWVo2cStnMTVGaVE9PSIsInZhbHVlIjoieEZHRHBOOFppQStQNEFaVnpJLzhra1BtT3ZNSmdZdVIyUUlsbHExeUw0Q0NCMXFwK1dqRExRcVNXcmlKSFJmcHh0djM5YUZDSk43TTdDbzBrWkNNZkpmMGpkTWpGRnozN3RjME5zRFROMG9FTElISldKNTBSMkNoU1F1U1pabEc4amI4SnBqY1BkazBDcWxUU0NUUzUxNFIyQXBQbVlzUEo1bkNBd0pZeitZZ25IZlJRTForZkltTWJYbXRWK2g4Wk5HRUNGWFg2S0JEbHFubFdCVEk1dHhZRG94RWg0VXRSVFR1TVZKb1FKbUp5MzBYc2FRWE9RTjB2MGh1ZkN6a1lTakd1SGlSQVhZYU5sVlhscURiKzFWcEJsM1ZwZmJuZUN4OWhCUkloMkprUmR3Z3FTZGJKV3R6cEkwTFplZnB3QklrMTQ3TXd1c3Z2TXkyT2NqK3NDaXlQaUZ2SUZtb0VuWDc2M3lndFlqUzRKb3AxT2V1NmUzV204SVpMUUJmd3Q2ekRnVVFJY1Y3eVJER3NURHNwem0wbXd2cDQ0QVFVbmpLS1M4NUpSZThpdWlkVy9IYnViNG82d29mcHNLQi9taC9qWGo3blhuSDBjd3FpZ3lnc09HV3o4ZkwvSllBL053cEFEK1VUZFA3MlJ3d0tkYjlia0lYYUltQjVYYkIiLCJtYWMiOiI1ZDJiM2FlMDAzOTZiNTFlZDc2ZmE4NTRiYWMzOTU3OWQwMTlmN2VkOGU2ZDNiZDhlYjYxNTZmNGZlMDgzZWMwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:38:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjkreWhET0NQQm15Si9STTVCaDZlSmc9PSIsInZhbHVlIjoiNjhDYjEyd08rUlRreTJrVitCUFRxQThMQjRGMG9IMThKN2pBeXpUMENFYUd1NDMzWWlsM2prSnBValZOVEVDTFpRWHQvRWdoMDQrY2F5VzJScXJSM0JvRVFMSEpxaHFJUjg2V0paeDAxTVloMjFJRlB6c0lyOHRnTmlQNWNRbUNPTktUTDFaampRQnQxb0lXeWVRV0F5N1dkSVhCWEpZZUg4bm5xT0xwVmhvS2trbHpXenNMeDFRODNLTEdJTUFZU2RpTVFWQlVsbVhraSswK1JNR2c1SkphYWRGeUxUR1Vjb3ZVNXZHakIzZVdxYW84S01kaGc2bEpqa2FVdjFqOWNOcGpWVml4Tm5aOFBTelA0aExQSzYrdmNNZmFNb05OOXRoalFMQUZEWi9OekIxdDJBOHNweENKaTkvZ3dOR1NYY0FlKzJCc3JjS0pCT2dBelVxb2VSVVRoSHc5WFNKV1M2OSsrTjlGMysxbVB2SlQyK0w4dDQrWVF3eTNuZFpobkVlZWdnZ09qa2ZpN2pINitrZjI5QlJlcVVQZTNXRnhXQVZjaENmVy9OOUMvbU5ibEtrUVVreWFRNmU4Q3JuSWxyUlBHWlNNM1hRTDVxMXhBdUsvSFQydXQ1dndSdk9Ga1R0WVV3VDRoUkxpRzErNUg4UU04eEszcjczZnV3akUiLCJtYWMiOiJlYWJjNGUwZjc2ZjU4YjRjMGUwYWE3NmEwYzkzNmY4ZDdhODE5ODkzZmE0MTE1YzNjN2YyN2E5ZTkxMjQ3NDUwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:38:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRmSEpZaXNKTzVMWVo2cStnMTVGaVE9PSIsInZhbHVlIjoieEZHRHBOOFppQStQNEFaVnpJLzhra1BtT3ZNSmdZdVIyUUlsbHExeUw0Q0NCMXFwK1dqRExRcVNXcmlKSFJmcHh0djM5YUZDSk43TTdDbzBrWkNNZkpmMGpkTWpGRnozN3RjME5zRFROMG9FTElISldKNTBSMkNoU1F1U1pabEc4amI4SnBqY1BkazBDcWxUU0NUUzUxNFIyQXBQbVlzUEo1bkNBd0pZeitZZ25IZlJRTForZkltTWJYbXRWK2g4Wk5HRUNGWFg2S0JEbHFubFdCVEk1dHhZRG94RWg0VXRSVFR1TVZKb1FKbUp5MzBYc2FRWE9RTjB2MGh1ZkN6a1lTakd1SGlSQVhZYU5sVlhscURiKzFWcEJsM1ZwZmJuZUN4OWhCUkloMkprUmR3Z3FTZGJKV3R6cEkwTFplZnB3QklrMTQ3TXd1c3Z2TXkyT2NqK3NDaXlQaUZ2SUZtb0VuWDc2M3lndFlqUzRKb3AxT2V1NmUzV204SVpMUUJmd3Q2ekRnVVFJY1Y3eVJER3NURHNwem0wbXd2cDQ0QVFVbmpLS1M4NUpSZThpdWlkVy9IYnViNG82d29mcHNLQi9taC9qWGo3blhuSDBjd3FpZ3lnc09HV3o4ZkwvSllBL053cEFEK1VUZFA3MlJ3d0tkYjlia0lYYUltQjVYYkIiLCJtYWMiOiI1ZDJiM2FlMDAzOTZiNTFlZDc2ZmE4NTRiYWMzOTU3OWQwMTlmN2VkOGU2ZDNiZDhlYjYxNTZmNGZlMDgzZWMwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:38:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577394924\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-832385366 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vUsiAx5ojM5LADxkzU6QZBulCHm2HL80mig6iju4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832385366\", {\"maxDepth\":0})</script>\n"}}