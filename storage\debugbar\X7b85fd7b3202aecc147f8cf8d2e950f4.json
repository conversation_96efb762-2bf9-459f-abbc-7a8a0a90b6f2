{"__meta": {"id": "X7b85fd7b3202aecc147f8cf8d2e950f4", "datetime": "2025-08-02 08:21:26", "utime": **********.044444, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754122884.622691, "end": **********.044482, "duration": 1.4217910766601562, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1754122884.622691, "relative_start": 0, "end": 1754122885.963452, "relative_end": 1754122885.963452, "duration": 1.3407611846923828, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1754122885.963493, "relative_start": 1.****************, "end": **********.044491, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xENlulCLvSIURjj9Fj41jyBmW0kwQF0kxkee37V6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1254769261 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1254769261\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1315262596 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1315262596\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-35497670 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-35497670\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1533823513 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533823513\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-363590257 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-363590257\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-953805110 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:21:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjR2dERRdnp6aVRjUmxtc2hQU1djS1E9PSIsInZhbHVlIjoiRmtTMjJPQWlYUCt2R0FrT3M3VjdIOVpsYVJQUTExazJIVGlKNWJrRjI2a3Qrd3FkeGdsQktVV2RzOHZ4RExBN09NNExoQVNVSHhFeWc5dnBPNldXcHBQOXNTZTgxSkxsZWRNTGZ4OVF5bzM2aGRSLytEWXhCRWFqemFOWHVYRHpGOGwvWEtXdjY0b1RPTytTU1dGeHp6Mm1JSDNoSTBJdnhaQzFFZVdmd05ndklla1JRc3hIVFp5TWVjdGdrajZkNnZBQ1dOSmFvOXo2TCttSHV6N2VONDVUTUYxRzBja2t2OFluUXptSWF4Ym1FRU9Hc0s0UzRuQ0tGUlduZHN3Rjg5eVVYSGdmbWdBR0gxR2dCL3p2eFN0RTNmTDRheENtNmJaZFpiT3o0aHM0ZmtEelFldlh3Ky9ldUdmcWdyZzd4QlNXbEwvZFk4RlNydHh4UTJKcG9jbGdsUmNkTXRIK3VQZHZWODdNd1d0bVNDeVlFY0xlQ29pcnpKRzgzZ3ZHMHFGaE9LYjBPbGJhdU4yUXZLTXZTcEV0Yk9KbzgwUkQxakp1cHZkMnBVYi9XRkxmZ0xYQk83N0F1M05EZGluU3JhN1V1d3g5cWpMelFSMTRGWmZqNVFzQ09ySGFHMnJoK0R1Rlp4dzFoRG5EZDcwM01Wd293T1oyWmFja2hiZVYiLCJtYWMiOiIwMGVhOTIzZjBjMWU0ODRkMmZjYzJhNjUxZjZjZWUxMWE1MDlmNDAwYTU2ZDU1YTM3MTZhNWM2Y2E3ZTM1NzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:21:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImFnanpBM3ErVFJZSjd0SThyNURNdmc9PSIsInZhbHVlIjoiSER5OFlZMVB6QmhRN0JRRjArc3lQc2lNZzN1NWhLRnNYY0dCSFd4VXpwYkxueDRpak1icHkzYUVVTkVBT2IxNVVoK3RWUUZjVnFMY0ZjSU1DcDVEWmxML3VsQ0hwVDM2TWV2YlFDREU3cVd3T0x4K292aW1tRGZ4bnVaRjVTL0hmVXhyLzY2OGFVN0RMWnJUekNEeXVlRVBSZ1FoSjZFZzZpcDZuc0xvNEcxVG1kejRxOThDMU1VZzl4RkF5bmIwbk1CSXZPb25lejVUa2MwQXdlakszenl1TCtUeHp0SHpya3F5cDNQRGcxeUlhMTZ4NEFveUdaUUFDR2dvZTJuRDVTWWtyOWNiQ2h3amtBVldTWCs0ZnBNSytQb0YrYnRzR2ZjenJ0QXpvOUtvSEZiY1lzTlNrNWdWYkhNeUpKa1A4eWQwMHc3RmdrR3g0MHRSWUUxdnpDMTRtSi8remY5d3l2NTZsWDdjTGtYd05kMXdOVkxCOFZmYlYxeXhpQWYzSEJ3MUF3QjRuTXVoeXljU2h1VFBOWXV6NmIwdkY4SWx4TGt5bkJYMWJpdWhqb0NZVTEvQURLMzFOSnBJakZMRUxJUjZaVnQvbktIdjF4Sm94NldoTENzWVdYVmJ4RVZtWXpMRUNhbXZBWU9za29yWVRMaFMyWjBQSEhiQWlsdnEiLCJtYWMiOiIwMWViYjBmNDIzN2MyYzU0ZTUwOTBhMGQwYjRkMjFkNDRmNDE1MjQwZDQ3NDNhYTY2Y2YxYTM4NDEzZTc2NGY3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:21:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjR2dERRdnp6aVRjUmxtc2hQU1djS1E9PSIsInZhbHVlIjoiRmtTMjJPQWlYUCt2R0FrT3M3VjdIOVpsYVJQUTExazJIVGlKNWJrRjI2a3Qrd3FkeGdsQktVV2RzOHZ4RExBN09NNExoQVNVSHhFeWc5dnBPNldXcHBQOXNTZTgxSkxsZWRNTGZ4OVF5bzM2aGRSLytEWXhCRWFqemFOWHVYRHpGOGwvWEtXdjY0b1RPTytTU1dGeHp6Mm1JSDNoSTBJdnhaQzFFZVdmd05ndklla1JRc3hIVFp5TWVjdGdrajZkNnZBQ1dOSmFvOXo2TCttSHV6N2VONDVUTUYxRzBja2t2OFluUXptSWF4Ym1FRU9Hc0s0UzRuQ0tGUlduZHN3Rjg5eVVYSGdmbWdBR0gxR2dCL3p2eFN0RTNmTDRheENtNmJaZFpiT3o0aHM0ZmtEelFldlh3Ky9ldUdmcWdyZzd4QlNXbEwvZFk4RlNydHh4UTJKcG9jbGdsUmNkTXRIK3VQZHZWODdNd1d0bVNDeVlFY0xlQ29pcnpKRzgzZ3ZHMHFGaE9LYjBPbGJhdU4yUXZLTXZTcEV0Yk9KbzgwUkQxakp1cHZkMnBVYi9XRkxmZ0xYQk83N0F1M05EZGluU3JhN1V1d3g5cWpMelFSMTRGWmZqNVFzQ09ySGFHMnJoK0R1Rlp4dzFoRG5EZDcwM01Wd293T1oyWmFja2hiZVYiLCJtYWMiOiIwMGVhOTIzZjBjMWU0ODRkMmZjYzJhNjUxZjZjZWUxMWE1MDlmNDAwYTU2ZDU1YTM3MTZhNWM2Y2E3ZTM1NzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:21:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImFnanpBM3ErVFJZSjd0SThyNURNdmc9PSIsInZhbHVlIjoiSER5OFlZMVB6QmhRN0JRRjArc3lQc2lNZzN1NWhLRnNYY0dCSFd4VXpwYkxueDRpak1icHkzYUVVTkVBT2IxNVVoK3RWUUZjVnFMY0ZjSU1DcDVEWmxML3VsQ0hwVDM2TWV2YlFDREU3cVd3T0x4K292aW1tRGZ4bnVaRjVTL0hmVXhyLzY2OGFVN0RMWnJUekNEeXVlRVBSZ1FoSjZFZzZpcDZuc0xvNEcxVG1kejRxOThDMU1VZzl4RkF5bmIwbk1CSXZPb25lejVUa2MwQXdlakszenl1TCtUeHp0SHpya3F5cDNQRGcxeUlhMTZ4NEFveUdaUUFDR2dvZTJuRDVTWWtyOWNiQ2h3amtBVldTWCs0ZnBNSytQb0YrYnRzR2ZjenJ0QXpvOUtvSEZiY1lzTlNrNWdWYkhNeUpKa1A4eWQwMHc3RmdrR3g0MHRSWUUxdnpDMTRtSi8remY5d3l2NTZsWDdjTGtYd05kMXdOVkxCOFZmYlYxeXhpQWYzSEJ3MUF3QjRuTXVoeXljU2h1VFBOWXV6NmIwdkY4SWx4TGt5bkJYMWJpdWhqb0NZVTEvQURLMzFOSnBJakZMRUxJUjZaVnQvbktIdjF4Sm94NldoTENzWVdYVmJ4RVZtWXpMRUNhbXZBWU9za29yWVRMaFMyWjBQSEhiQWlsdnEiLCJtYWMiOiIwMWViYjBmNDIzN2MyYzU0ZTUwOTBhMGQwYjRkMjFkNDRmNDE1MjQwZDQ3NDNhYTY2Y2YxYTM4NDEzZTc2NGY3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:21:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953805110\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-960535841 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xENlulCLvSIURjj9Fj41jyBmW0kwQF0kxkee37V6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960535841\", {\"maxDepth\":0})</script>\n"}}