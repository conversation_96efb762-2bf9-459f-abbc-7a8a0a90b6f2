{"__meta": {"id": "Xf673911c5f203a339f0ca40f4d151760", "datetime": "2025-08-02 10:38:43", "utime": **********.891044, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754131122.67894, "end": **********.891075, "duration": 1.212134838104248, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1754131122.67894, "relative_start": 0, "end": **********.811163, "relative_end": **********.811163, "duration": 1.1322228908538818, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.811178, "relative_start": 1.****************, "end": **********.891079, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "79.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3035\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1865 to 1871\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1865\" onclick=\"\">routes/web.php:1865-1871</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kzX4joR1hXJ2alVWRiSGSG7j1pGt7cdauG8RY3Bc", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-544424018 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-544424018\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1966946315 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1966946315\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1261164650 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1261164650\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2120679842 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120679842\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-642995341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-642995341\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1443084453 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:38:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNla1drYXRUVmtKSGtkc1Z6cGF2enc9PSIsInZhbHVlIjoiQzB0d2hDS01JUmJGWlFNQjNPVTYrZnhtRmZYL3cwaGh3alNEOXVmUHB3LzBDQ0ZBOEN4YkRIZnhhQlpEQXRyWlhLeVhOVU5Cc2Zwb2ZENmw2MlNHWGxPTGl4UUtuR21VVXVhNkt3aVJHbmpBTnREa3hJUGJ5L1N2OVFuYWRqak1RVTlaS2hTQ0lxQXU1QncvWWRoWWcwMlJtNHpBL2dsMmxGVXVSNWQ2WWdTNlNSQ3NsTnIrVE5mSll0aTdtenFCZmZIdVZ5UWRlYkZyVDJlb3NlSDNKZ2RnaGw3SWJ0UDl1L0h0ekRJRUZGcGRGcGJkd3hCdWdoMXczVUZUeTNnMXJ3YWEzLzhiOGhpQVZTQmlsdDFBNHNGblJyWDRETkhiTEZIRzhGV3FmV0hNSm15QVRLQ2N3YVIzclJaa3FYcmhtUVRBWm1XQXNPTys2TU0wTGQvQS9XRmE2NXJmeGdBOE9JZCtGak90VGJ0MXNvTzhLM01EV2JGM2ZlZmdtaWd6eHNBVDhkTGNpazlqYUw5c2NZVUFSN0tKclBiU0MzaDNiMTNoUVVmcUFYU2VkYkhQL3hOdlRpeGh4MXZ4b3pQT1lQcVNUTTRpRS9lR3NzNmdvQW94Z2djcytUL0twTjR6YnhZcjFicDVLRFBPM21VaEZERkc5Q3ZDdjE0WnA3eGYiLCJtYWMiOiI3NTA3YWE4ZDFjMGJhZDE0NTA5ZGJkZjdiNTdiM2Q1ZmMzNzE3MGRiM2I0MjQ3MjM4NWI4YzI0MmU0Y2Q5NmY5IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:38:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InQ1aGY5TmhYcnNKMGUwYkwwL2RMcmc9PSIsInZhbHVlIjoiK2hqUzZ4UVE4N0I4L1Y2eE4vM3ZYaVVaNUZ3T2d6akorYThXTWtRT3lJN0YrQlorL2w5TnR0QlQ0NWozQU5peXIwblJIUktFR3J6MUdiWGthTzVHczlZMmY1eHNvYXVFbHhNRm42RE51TDRVa2ZoMVJUSTk1WVlLb2p2RDJjdXMxTml1Z2V6QUpZcTBXc3g4L0VCdEV2MmcyS0dDZnlNZU1zbzhYUUQrbWZEa21VSm91TEo1S1poVzJIWXN0MDBrZ2oxV3RMM2dQeVpPckFlS0NPVjJvbHEvSDQyM2p3K0lIYkZoem9UazQ1ZEM1aG14ajVkZVhodGlyZkJXMHNJcUZHWDUxVFEzckZpVy9LNkpFM0txT0JrRncwSXd6NTJwaDRGVUdKdDBlUmQvRUFNRE1xNlFGNGVteXRtdE8wV1BpWnBhVTdTVzlSS1BoRHAzcmRBSFZBYWRETGxKN3l2cFlGaHNseTFCekZVdkxnMnl2cDFmb2U5dkFOWlJkWlBYa0wwTnV4SEp2OXBIZW9aQ2NtUmVjcVFaRzBMQmloRGhja3BVMXpwTUF5VnhLUURoUURoMlhkVVpBMnh3NzV4SU9DcmFid29ZNXFtNm5pYTRYRFhuRkRFaStZNDNVNEkzcWNObU9zQmhjbURFd1dkVHFlVVBVRnBGdlFLVmg0SysiLCJtYWMiOiI1ZTc2OTU5OTZkZmFkZTFjNjVkYmMzNTc0ZTdiOWExNWIyNDMxYzI1ZTY1MTExNzVmMjRjZjUwZTU4ZGI2Y2Q1IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:38:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNla1drYXRUVmtKSGtkc1Z6cGF2enc9PSIsInZhbHVlIjoiQzB0d2hDS01JUmJGWlFNQjNPVTYrZnhtRmZYL3cwaGh3alNEOXVmUHB3LzBDQ0ZBOEN4YkRIZnhhQlpEQXRyWlhLeVhOVU5Cc2Zwb2ZENmw2MlNHWGxPTGl4UUtuR21VVXVhNkt3aVJHbmpBTnREa3hJUGJ5L1N2OVFuYWRqak1RVTlaS2hTQ0lxQXU1QncvWWRoWWcwMlJtNHpBL2dsMmxGVXVSNWQ2WWdTNlNSQ3NsTnIrVE5mSll0aTdtenFCZmZIdVZ5UWRlYkZyVDJlb3NlSDNKZ2RnaGw3SWJ0UDl1L0h0ekRJRUZGcGRGcGJkd3hCdWdoMXczVUZUeTNnMXJ3YWEzLzhiOGhpQVZTQmlsdDFBNHNGblJyWDRETkhiTEZIRzhGV3FmV0hNSm15QVRLQ2N3YVIzclJaa3FYcmhtUVRBWm1XQXNPTys2TU0wTGQvQS9XRmE2NXJmeGdBOE9JZCtGak90VGJ0MXNvTzhLM01EV2JGM2ZlZmdtaWd6eHNBVDhkTGNpazlqYUw5c2NZVUFSN0tKclBiU0MzaDNiMTNoUVVmcUFYU2VkYkhQL3hOdlRpeGh4MXZ4b3pQT1lQcVNUTTRpRS9lR3NzNmdvQW94Z2djcytUL0twTjR6YnhZcjFicDVLRFBPM21VaEZERkc5Q3ZDdjE0WnA3eGYiLCJtYWMiOiI3NTA3YWE4ZDFjMGJhZDE0NTA5ZGJkZjdiNTdiM2Q1ZmMzNzE3MGRiM2I0MjQ3MjM4NWI4YzI0MmU0Y2Q5NmY5IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:38:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InQ1aGY5TmhYcnNKMGUwYkwwL2RMcmc9PSIsInZhbHVlIjoiK2hqUzZ4UVE4N0I4L1Y2eE4vM3ZYaVVaNUZ3T2d6akorYThXTWtRT3lJN0YrQlorL2w5TnR0QlQ0NWozQU5peXIwblJIUktFR3J6MUdiWGthTzVHczlZMmY1eHNvYXVFbHhNRm42RE51TDRVa2ZoMVJUSTk1WVlLb2p2RDJjdXMxTml1Z2V6QUpZcTBXc3g4L0VCdEV2MmcyS0dDZnlNZU1zbzhYUUQrbWZEa21VSm91TEo1S1poVzJIWXN0MDBrZ2oxV3RMM2dQeVpPckFlS0NPVjJvbHEvSDQyM2p3K0lIYkZoem9UazQ1ZEM1aG14ajVkZVhodGlyZkJXMHNJcUZHWDUxVFEzckZpVy9LNkpFM0txT0JrRncwSXd6NTJwaDRGVUdKdDBlUmQvRUFNRE1xNlFGNGVteXRtdE8wV1BpWnBhVTdTVzlSS1BoRHAzcmRBSFZBYWRETGxKN3l2cFlGaHNseTFCekZVdkxnMnl2cDFmb2U5dkFOWlJkWlBYa0wwTnV4SEp2OXBIZW9aQ2NtUmVjcVFaRzBMQmloRGhja3BVMXpwTUF5VnhLUURoUURoMlhkVVpBMnh3NzV4SU9DcmFid29ZNXFtNm5pYTRYRFhuRkRFaStZNDNVNEkzcWNObU9zQmhjbURFd1dkVHFlVVBVRnBGdlFLVmg0SysiLCJtYWMiOiI1ZTc2OTU5OTZkZmFkZTFjNjVkYmMzNTc0ZTdiOWExNWIyNDMxYzI1ZTY1MTExNzVmMjRjZjUwZTU4ZGI2Y2Q1IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:38:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443084453\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-558589087 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kzX4joR1hXJ2alVWRiSGSG7j1pGt7cdauG8RY3Bc</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558589087\", {\"maxDepth\":0})</script>\n"}}