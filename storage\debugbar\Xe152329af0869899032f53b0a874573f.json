{"__meta": {"id": "Xe152329af0869899032f53b0a874573f", "datetime": "2025-08-02 10:47:01", "utime": **********.827052, "method": "GET", "uri": "/contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754131619.578653, "end": **********.8271, "duration": 2.2484469413757324, "duration_str": "2.25s", "measures": [{"label": "Booting", "start": 1754131619.578653, "relative_start": 0, "end": **********.569716, "relative_end": **********.569716, "duration": 0.9910628795623779, "duration_str": "991ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.56974, "relative_start": 0.9910869598388672, "end": **********.827105, "relative_end": 5.0067901611328125e-06, "duration": 1.2573649883270264, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 61933328, "peak_usage_str": "59MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "1x contacts.index", "param_count": null, "params": [], "start": **********.793953, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.phpcontacts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "contacts.index"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.240314, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.250056, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.771085, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.crm-top-nav", "param_count": null, "params": [], "start": **********.805043, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/crm-top-nav.blade.phppartials.admin.crm-top-nav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fcrm-top-nav.blade.php&line=1", "ajax": false, "filename": "crm-top-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.crm-top-nav"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.806544, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.813221, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.814914, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET contacts", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@index", "namespace": null, "prefix": "", "where": [], "as": "contacts.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=21\" onclick=\"\">app/Http/Controllers/ContactController.php:21-146</a>"}, "queries": {"nb_statements": 110, "nb_failed_statements": 0, "accumulated_duration": 0.13992000000000004, "accumulated_duration_str": "140ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.672071, "duration": 0.01126, "duration_str": "11.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 8.047}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.70809, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 8.047, "width_percent": 0.758}, {"sql": "select * from `leads` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7188308, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:40", "source": "app/Http/Controllers/ContactController.php:40", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=40", "ajax": false, "filename": "ContactController.php", "line": "40"}, "connection": "radhe_same", "start_percent": 8.805, "width_percent": 1.294}, {"sql": "select * from `contact_groups` where `contact_groups`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 40}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.736294, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:40", "source": "app/Http/Controllers/ContactController.php:40", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=40", "ajax": false, "filename": "ContactController.php", "line": "40"}, "connection": "radhe_same", "start_percent": 10.099, "width_percent": 0.622}, {"sql": "select * from `deals` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 64}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7461, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:64", "source": "app/Http/Controllers/ContactController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=64", "ajax": false, "filename": "ContactController.php", "line": "64"}, "connection": "radhe_same", "start_percent": 10.72, "width_percent": 0.843}, {"sql": "select * from `module_integrations` where `enabled` = 1 and `base_url` is not null and `base_url` != '' limit 1", "type": "query", "params": [], "bindings": ["1", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.752045, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:89", "source": "app/Http/Controllers/ContactController.php:89", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=89", "ajax": false, "filename": "ContactController.php", "line": "89"}, "connection": "radhe_same", "start_percent": 11.564, "width_percent": 0.565}, {"sql": "select * from `pipelines` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 92}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.757008, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:92", "source": "app/Http/Controllers/ContactController.php:92", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=92", "ajax": false, "filename": "ContactController.php", "line": "92"}, "connection": "radhe_same", "start_percent": 12.128, "width_percent": 0.515}, {"sql": "select `name`, `id` from `tags` where `created_by` = 79 and `is_active` = 1", "type": "query", "params": [], "bindings": ["79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 117}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.763598, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:117", "source": "app/Http/Controllers/ContactController.php:117", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=117", "ajax": false, "filename": "ContactController.php", "line": "117"}, "connection": "radhe_same", "start_percent": 12.643, "width_percent": 0.593}, {"sql": "select * from `leads` where `leads`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.808151, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 13.236, "width_percent": 0.522}, {"sql": "select * from `leads` where `leads`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.81458, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 13.758, "width_percent": 0.615}, {"sql": "select * from `leads` where `leads`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.817909, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 14.372, "width_percent": 0.415}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.821181, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 14.787, "width_percent": 0.607}, {"sql": "select * from `leads` where `leads`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8256679, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 15.395, "width_percent": 0.522}, {"sql": "select * from `leads` where `leads`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8314261, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 15.916, "width_percent": 0.693}, {"sql": "select * from `leads` where `leads`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.83637, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 16.609, "width_percent": 0.672}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":89', '\\\"name\\\":\\\"Cold Lead\\\"', '{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:89", "&quot;name&quot;:&quot;Cold Lead&quot;", "{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.841435, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 17.281, "width_percent": 0.65}, {"sql": "select * from `leads` where `leads`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8479722, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 17.932, "width_percent": 0.979}, {"sql": "select * from `leads` where `leads`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.855278, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 18.911, "width_percent": 0.722}, {"sql": "select * from `leads` where `leads`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8613508, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 19.633, "width_percent": 1.065}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.869272, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 20.698, "width_percent": 0.929}, {"sql": "select * from `leads` where `leads`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.878825, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 21.627, "width_percent": 1.122}, {"sql": "select * from `leads` where `leads`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.885283, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 22.749, "width_percent": 1.422}, {"sql": "select * from `leads` where `leads`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8944302, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 24.171, "width_percent": 1.444}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.901099, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 25.615, "width_percent": 0.808}, {"sql": "select * from `leads` where `leads`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.909647, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 26.422, "width_percent": 0.901}, {"sql": "select * from `leads` where `leads`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.919541, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 27.323, "width_percent": 0.658}, {"sql": "select * from `leads` where `leads`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.924548, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 27.98, "width_percent": 0.743}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":89', '\\\"name\\\":\\\"Cold Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:89", "&quot;name&quot;:&quot;Cold Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.931441, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 28.724, "width_percent": 0.586}, {"sql": "select * from `leads` where `leads`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.939876, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 29.31, "width_percent": 1.501}, {"sql": "select * from `leads` where `leads`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9472299, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 30.81, "width_percent": 1.158}, {"sql": "select * from `leads` where `leads`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9530869, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 31.968, "width_percent": 0.658}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":89', '\\\"name\\\":\\\"Cold Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '{\\\"id\\\":95', '\\\"name\\\":\\\"ok bot\\\"', '\\\"created_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:89", "&quot;name&quot;:&quot;Cold Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "{&quot;id&quot;:95", "&quot;name&quot;:&quot;ok bot&quot;", "&quot;created_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.959011, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 32.626, "width_percent": 0.772}, {"sql": "select * from `leads` where `leads`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.967048, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 33.398, "width_percent": 0.993}, {"sql": "select * from `leads` where `leads`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.9730558, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 34.391, "width_percent": 0.779}, {"sql": "select * from `leads` where `leads`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.97989, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 35.17, "width_percent": 0.722}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":89', '\\\"name\\\":\\\"Cold Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:89", "&quot;name&quot;:&quot;Cold Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.985286, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 35.892, "width_percent": 0.815}, {"sql": "select * from `leads` where `leads`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.992661, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 36.707, "width_percent": 1.215}, {"sql": "select * from `leads` where `leads`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0021338, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 37.922, "width_percent": 0.929}, {"sql": "select * from `leads` where `leads`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.008023, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 38.851, "width_percent": 0.872}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '{\\\"id\\\":91', '\\\"name\\\":\\\"VIP\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '{\\\"id\\\":94', '\\\"name\\\":\\\"Ok AUI\\\"', '\\\"created_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"}', '{\\\"id\\\":96', '\\\"name\\\":\\\"ok bot ai\\\"', '\\\"created_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"}', '{\\\"id\\\":97', '\\\"name\\\":\\\"Paaa\\\"', '\\\"created_at\\\":\\\"2025-08-02T09:27:58.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T09:27:58.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "{&quot;id&quot;:91", "&quot;name&quot;:&quot;VIP&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "{&quot;id&quot;:94", "&quot;name&quot;:&quot;Ok AUI&quot;", "&quot;created_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;}", "{&quot;id&quot;:96", "&quot;name&quot;:&quot;ok bot ai&quot;", "&quot;created_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;}", "{&quot;id&quot;:97", "&quot;name&quot;:&quot;Paaa&quot;", "&quot;created_at&quot;:&quot;2025-08-02T09:27:58.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T09:27:58.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.014583, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 39.723, "width_percent": 1.072}, {"sql": "select * from `leads` where `leads`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0220659, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 40.795, "width_percent": 0.936}, {"sql": "select * from `leads` where `leads`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.029635, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 41.731, "width_percent": 0.993}, {"sql": "select * from `leads` where `leads`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.035451, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 42.724, "width_percent": 0.815}, {"sql": "select * from `tags` where `id` in ('[]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0416908, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 43.539, "width_percent": 0.936}, {"sql": "select * from `leads` where `leads`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.048233, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 44.475, "width_percent": 0.536}, {"sql": "select * from `leads` where `leads`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.052073, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 45.011, "width_percent": 0.457}, {"sql": "select * from `leads` where `leads`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0556169, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 45.469, "width_percent": 0.436}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":90', '\\\"name\\\":\\\"Warm Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:90", "&quot;name&quot;:&quot;Warm Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.059023, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 45.905, "width_percent": 0.415}, {"sql": "select * from `leads` where `leads`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.065583, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 46.319, "width_percent": 0.543}, {"sql": "select * from `leads` where `leads`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0693328, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 46.862, "width_percent": 0.429}, {"sql": "select * from `leads` where `leads`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0727289, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 47.291, "width_percent": 0.436}, {"sql": "select * from `tags` where `id` in ('[]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.076119, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 47.727, "width_percent": 0.429}, {"sql": "select * from `leads` where `leads`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.081923, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 48.156, "width_percent": 0.522}, {"sql": "select * from `leads` where `leads`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.085578, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 48.678, "width_percent": 0.429}, {"sql": "select * from `leads` where `leads`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0891318, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 49.107, "width_percent": 0.429}, {"sql": "select * from `tags` where `id` in ('[]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.092554, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 49.535, "width_percent": 0.45}, {"sql": "select * from `leads` where `leads`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.098027, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 49.986, "width_percent": 0.522}, {"sql": "select * from `leads` where `leads`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.101172, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 50.507, "width_percent": 0.515}, {"sql": "select * from `leads` where `leads`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.10412, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 51.022, "width_percent": 0.515}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":98', '\\\"name\\\":\\\"OK BOT AI DEMON\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:98", "&quot;name&quot;:&quot;OK BOT AI DEMON&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.107189, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 51.537, "width_percent": 0.55}, {"sql": "select * from `leads` where `leads`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": ["24"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1137981, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 52.087, "width_percent": 0.6}, {"sql": "select * from `leads` where `leads`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": ["24"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.117404, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 52.687, "width_percent": 0.507}, {"sql": "select * from `leads` where `leads`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": ["24"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.120512, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 53.195, "width_percent": 0.429}, {"sql": "select * from `tags` where `id` in ('[]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.12341, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 53.623, "width_percent": 0.507}, {"sql": "select * from `leads` where `leads`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1278548, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 54.131, "width_percent": 0.8}, {"sql": "select * from `leads` where `leads`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.132588, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 54.931, "width_percent": 0.5}, {"sql": "select * from `leads` where `leads`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1360738, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 55.432, "width_percent": 0.436}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":89', '\\\"name\\\":\\\"Cold Lead\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '{\\\"id\\\":94', '\\\"name\\\":\\\"Ok AUI\\\"', '\\\"created_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"}', '{\\\"id\\\":95', '\\\"name\\\":\\\"ok bot\\\"', '\\\"created_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"}', '{\\\"id\\\":96', '\\\"name\\\":\\\"ok bot ai\\\"', '\\\"created_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"}', '{\\\"id\\\":98', '\\\"name\\\":\\\"OK BOT AI DEMON\\\"', '\\\"created_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:89", "&quot;name&quot;:&quot;Cold Lead&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "{&quot;id&quot;:94", "&quot;name&quot;:&quot;Ok AUI&quot;", "&quot;created_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;}", "{&quot;id&quot;:95", "&quot;name&quot;:&quot;ok bot&quot;", "&quot;created_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;}", "{&quot;id&quot;:96", "&quot;name&quot;:&quot;ok bot ai&quot;", "&quot;created_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;}", "{&quot;id&quot;:98", "&quot;name&quot;:&quot;OK BOT AI DEMON&quot;", "&quot;created_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.138936, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 55.868, "width_percent": 0.465}, {"sql": "select * from `leads` where `leads`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": ["26"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.14282, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 56.332, "width_percent": 0.765}, {"sql": "select * from `leads` where `leads`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": ["26"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.146913, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 57.097, "width_percent": 0.622}, {"sql": "select * from `leads` where `leads`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": ["26"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.149992, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 57.719, "width_percent": 0.536}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":88', '\\\"name\\\":\\\"Hot Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":89', '\\\"name\\\":\\\"Cold Lead\\\"', '{\\\"id\\\":92', '\\\"name\\\":\\\"Follow Up\\\"', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '{\\\"id\\\":94', '\\\"name\\\":\\\"Ok AUI\\\"', '\\\"created_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"}', '{\\\"id\\\":95', '\\\"name\\\":\\\"ok bot\\\"', '\\\"created_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"}', '{\\\"id\\\":96', '\\\"name\\\":\\\"ok bot ai\\\"', '\\\"created_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"}', '{\\\"id\\\":97', '\\\"name\\\":\\\"Paaa\\\"', '\\\"created_at\\\":\\\"2025-08-02T09:27:58.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T09:27:58.000000Z\\\"}', '{\\\"id\\\":98', '\\\"name\\\":\\\"OK BOT AI DEMON\\\"', '\\\"created_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:88", "&quot;name&quot;:&quot;Hot Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:89", "&quot;name&quot;:&quot;Cold Lead&quot;", "{&quot;id&quot;:92", "&quot;name&quot;:&quot;Follow Up&quot;", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "{&quot;id&quot;:94", "&quot;name&quot;:&quot;Ok AUI&quot;", "&quot;created_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;}", "{&quot;id&quot;:95", "&quot;name&quot;:&quot;ok bot&quot;", "&quot;created_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;}", "{&quot;id&quot;:96", "&quot;name&quot;:&quot;ok bot ai&quot;", "&quot;created_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;}", "{&quot;id&quot;:97", "&quot;name&quot;:&quot;Paaa&quot;", "&quot;created_at&quot;:&quot;2025-08-02T09:27:58.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T09:27:58.000000Z&quot;}", "{&quot;id&quot;:98", "&quot;name&quot;:&quot;OK BOT AI DEMON&quot;", "&quot;created_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.153077, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 58.255, "width_percent": 0.536}, {"sql": "select * from `leads` where `leads`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.156659, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 58.791, "width_percent": 0.55}, {"sql": "select * from `leads` where `leads`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.160305, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 59.341, "width_percent": 0.607}, {"sql": "select * from `leads` where `leads`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.164717, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 59.949, "width_percent": 0.522}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":89', '\\\"name\\\":\\\"Cold Lead\\\"', '\\\"created_by\\\":79', '\\\"is_active\\\":1', '\\\"created_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-30T17:17:23.000000Z\\\"}', '{\\\"id\\\":93', '\\\"name\\\":\\\"New Customer\\\"', '{\\\"id\\\":94', '\\\"name\\\":\\\"Ok AUI\\\"', '\\\"created_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T07:52:49.000000Z\\\"}', '{\\\"id\\\":95', '\\\"name\\\":\\\"ok bot\\\"', '\\\"created_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T08:28:42.000000Z\\\"}', '{\\\"id\\\":96', '\\\"name\\\":\\\"ok bot ai\\\"', '\\\"created_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T09:16:35.000000Z\\\"}', '{\\\"id\\\":97', '\\\"name\\\":\\\"Paaa\\\"', '\\\"created_at\\\":\\\"2025-08-02T09:27:58.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T09:27:58.000000Z\\\"}', '{\\\"id\\\":98', '\\\"name\\\":\\\"OK BOT AI DEMON\\\"', '\\\"created_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-08-02T10:06:37.000000Z\\\"}]', '88', '93', '94', '92', '95')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:89", "&quot;name&quot;:&quot;Cold Lead&quot;", "&quot;created_by&quot;:79", "&quot;is_active&quot;:1", "&quot;created_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-30T17:17:23.000000Z&quot;}", "{&quot;id&quot;:93", "&quot;name&quot;:&quot;New Customer&quot;", "{&quot;id&quot;:94", "&quot;name&quot;:&quot;Ok AUI&quot;", "&quot;created_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T07:52:49.000000Z&quot;}", "{&quot;id&quot;:95", "&quot;name&quot;:&quot;ok bot&quot;", "&quot;created_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T08:28:42.000000Z&quot;}", "{&quot;id&quot;:96", "&quot;name&quot;:&quot;ok bot ai&quot;", "&quot;created_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T09:16:35.000000Z&quot;}", "{&quot;id&quot;:97", "&quot;name&quot;:&quot;Paaa&quot;", "&quot;created_at&quot;:&quot;2025-08-02T09:27:58.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T09:27:58.000000Z&quot;}", "{&quot;id&quot;:98", "&quot;name&quot;:&quot;OK BOT AI DEMON&quot;", "&quot;created_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-08-02T10:06:37.000000Z&quot;}]", "88", "93", "94", "92", "95"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.1679, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 60.47, "width_percent": 0.472}, {"sql": "select * from `leads` where `leads`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1715221, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 60.942, "width_percent": 0.479}, {"sql": "select * from `leads` where `leads`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.17438, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 61.421, "width_percent": 0.5}, {"sql": "select * from `leads` where `leads`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.178554, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 61.921, "width_percent": 0.872}, {"sql": "select * from `tags` where `id` in ('92', '88', '89', '93', '94')", "type": "query", "params": [], "bindings": ["92", "88", "89", "93", "94"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.182554, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 62.793, "width_percent": 0.586}, {"sql": "select * from `leads` where `leads`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 358}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.186857, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "contacts.index:358", "source": "view::contacts.index:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=358", "ajax": false, "filename": "index.blade.php", "line": "358"}, "connection": "radhe_same", "start_percent": 63.379, "width_percent": 0.708}, {"sql": "select * from `leads` where `leads`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 378}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1904159, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "contacts.index:378", "source": "view::contacts.index:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=378", "ajax": false, "filename": "index.blade.php", "line": "378"}, "connection": "radhe_same", "start_percent": 64.087, "width_percent": 0.515}, {"sql": "select * from `leads` where `leads`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": ["29"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 407}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.1944458, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "contacts.index:407", "source": "view::contacts.index:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=407", "ajax": false, "filename": "index.blade.php", "line": "407"}, "connection": "radhe_same", "start_percent": 64.601, "width_percent": 0.915}, {"sql": "select * from `tags` where `id` in ('93')", "type": "query", "params": [], "bindings": ["93"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 154}, {"index": 16, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 408}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.1992629, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Lead.php:154", "source": "app/Models/Lead.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=154", "ajax": false, "filename": "Lead.php", "line": "154"}, "connection": "radhe_same", "start_percent": 65.516, "width_percent": 0.429}, {"sql": "select * from `deals` where `deals`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 365}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.202708, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "contacts.index:365", "source": "view::contacts.index:365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=365", "ajax": false, "filename": "index.blade.php", "line": "365"}, "connection": "radhe_same", "start_percent": 65.945, "width_percent": 0.443}, {"sql": "select * from `deals` where `deals`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 385}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.205486, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "contacts.index:385", "source": "view::contacts.index:385", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=385", "ajax": false, "filename": "index.blade.php", "line": "385"}, "connection": "radhe_same", "start_percent": 66.388, "width_percent": 0.407}, {"sql": "select * from `deals` where `deals`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 365}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.208479, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "contacts.index:365", "source": "view::contacts.index:365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=365", "ajax": false, "filename": "index.blade.php", "line": "365"}, "connection": "radhe_same", "start_percent": 66.795, "width_percent": 0.379}, {"sql": "select * from `deals` where `deals`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "contacts.index", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/contacts/index.blade.php", "line": 385}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.212647, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "contacts.index:385", "source": "view::contacts.index:385", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fcontacts%2Findex.blade.php&line=385", "ajax": false, "filename": "index.blade.php", "line": "385"}, "connection": "radhe_same", "start_percent": 67.174, "width_percent": 0.472}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.24144, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 67.646, "width_percent": 0.65}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.246013, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 68.296, "width_percent": 0.55}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2538092, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 68.846, "width_percent": 0.572}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.2575781, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "radhe_same", "start_percent": 69.418, "width_percent": 0.472}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.261531, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "partials.admin.menu:20", "source": "view::partials.admin.menu:20", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=20", "ajax": false, "filename": "menu.blade.php", "line": "20"}, "connection": "radhe_same", "start_percent": 69.89, "width_percent": 0.85}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.2694569, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 70.74, "width_percent": 1.515}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.303377, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 72.256, "width_percent": 4.51}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 79 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["79", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4578228, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "radhe_same", "start_percent": 76.765, "width_percent": 0.679}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4624789, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "radhe_same", "start_percent": 77.444, "width_percent": 0.936}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 189}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 1452}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.520437, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 78.381, "width_percent": 0.55}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 189}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 1452}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.524815, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 78.931, "width_percent": 0.443}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 189}, {"index": 25, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 1452}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.529459, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 79.374, "width_percent": 2.687}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 211}, {"index": 22, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 1452}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.566217, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:211", "source": "app/Models/User.php:211", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=211", "ajax": false, "filename": "User.php", "line": "211"}, "connection": "radhe_same", "start_percent": 82.061, "width_percent": 0.622}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.570668, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 82.683, "width_percent": 1.651}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.600269, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 84.334, "width_percent": 4.51}, {"sql": "select * from `module_integrations` where `name` = 'Automatish' and `enabled` = 1 limit 1", "type": "query", "params": [], "bindings": ["Automatish", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 1493}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.758943, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "partials.admin.menu:1493", "source": "view::partials.admin.menu:1493", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1493", "ajax": false, "filename": "menu.blade.php", "line": "1493"}, "connection": "radhe_same", "start_percent": 88.844, "width_percent": 0.879}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7720919, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 89.723, "width_percent": 0.557}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.775882, "duration": 0.01028, "duration_str": "10.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 90.28, "width_percent": 7.347}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.7896302, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 97.627, "width_percent": 0.457}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 16}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.793983, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 98.085, "width_percent": 0.879}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 79 and `seen` = 0", "type": "query", "params": [], "bindings": ["79", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.799362, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:20", "source": "view::partials.admin.header:20", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=20", "ajax": false, "filename": "header.blade.php", "line": "20"}, "connection": "radhe_same", "start_percent": 98.964, "width_percent": 0.472}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 5748}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8074098, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.435, "width_percent": 0.565}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 3196, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1813, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Tag": {"value": 91, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\Lead": {"value": 76, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\Deal": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FDeal.php&line=1", "ajax": false, "filename": "Deal.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ContactGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FContactGroup.php&line=1", "ajax": false, "filename": "ContactGroup.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 5190, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 54, "messages": [{"message": "[ability => show hrm dashboard, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.469389, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.471146, "xdebug_link": null}, {"message": "[ability => show crm dashboard, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show crm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show crm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.471709, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1185917798 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185917798\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.473359, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1466699414 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466699414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.474011, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2124141187 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124141187\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.474718, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-819012454 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819012454\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.475317, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-83546365 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83546365\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.475944, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-261623861 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261623861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.476601, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-251594145 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251594145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.478132, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-362195113 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362195113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.478915, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.479562, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1058181150 data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058181150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.480229, "xdebug_link": null}, {"message": "[ability => manage budget plan, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage budget plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage budget plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.482968, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.48356, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.484012, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1171902804 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171902804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.484683, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1447758632 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447758632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.485401, "xdebug_link": null}, {"message": "[ability => manage invoice, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-921296688 data-indent-pad=\"  \"><span class=sf-dump-note>manage invoice</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage invoice</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921296688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.485754, "xdebug_link": null}, {"message": "[ability => manage revenue, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1763295871 data-indent-pad=\"  \"><span class=sf-dump-note>manage revenue</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage revenue</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763295871\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.486304, "xdebug_link": null}, {"message": "[ability => manage credit note, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-508425673 data-indent-pad=\"  \"><span class=sf-dump-note>manage credit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage credit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508425673\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.48688, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1076583781 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076583781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.487421, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-955350850 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955350850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.48782, "xdebug_link": null}, {"message": "[ability => manage bill, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1066651945 data-indent-pad=\"  \"><span class=sf-dump-note>manage bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066651945\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.4884, "xdebug_link": null}, {"message": "[ability => manage payment, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1578132616 data-indent-pad=\"  \"><span class=sf-dump-note>manage payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578132616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.488912, "xdebug_link": null}, {"message": "[ability => manage debit note, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage debit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage debit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489561, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.49025, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.490862, "xdebug_link": null}, {"message": "[ability => manage journal entry, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage journal entry</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">manage journal entry</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.491594, "xdebug_link": null}, {"message": "[ability => ledger report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1749530177 data-indent-pad=\"  \"><span class=sf-dump-note>ledger report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">ledger report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749530177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.492315, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2034171560 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034171560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.492864, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-683881911 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683881911\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.494217, "xdebug_link": null}, {"message": "[ability => trial balance report, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1120286822 data-indent-pad=\"  \"><span class=sf-dump-note>trial balance report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">trial balance report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120286822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.496658, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.498151, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2131472623 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131472623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.498705, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.499161, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1641052117 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641052117\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.499898, "xdebug_link": null}, {"message": "[ability => manage lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-451094007 data-indent-pad=\"  \"><span class=sf-dump-note>manage lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-451094007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.50123, "xdebug_link": null}, {"message": "[ability => manage booking, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1496630027 data-indent-pad=\"  \"><span class=sf-dump-note>manage booking</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage booking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496630027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.503826, "xdebug_link": null}, {"message": "[ability => manage project, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1370003040 data-indent-pad=\"  \"><span class=sf-dump-note>manage project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370003040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.505411, "xdebug_link": null}, {"message": "[ability => manage personal task, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-677945789 data-indent-pad=\"  \"><span class=sf-dump-note>manage personal task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">manage personal task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677945789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.507534, "xdebug_link": null}, {"message": "[ability => manage project task, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1639706895 data-indent-pad=\"  \"><span class=sf-dump-note>manage project task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage project task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639706895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.509056, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-436331819 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436331819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.510107, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-558627258 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558627258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.51072, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-598789446 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598789446\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.513613, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1263015094 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263015094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.515388, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1175662313 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175662313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.51629, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1298350203 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298350203\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.516872, "xdebug_link": null}, {"message": "[ability => access omx flow, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-822776607 data-indent-pad=\"  \"><span class=sf-dump-note>access omx flow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">access omx flow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822776607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.756195, "xdebug_link": null}, {"message": "[ability => access automatish, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>access automatish</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">access automatish</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.758627, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76623, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2006070983 data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006070983\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.766803, "xdebug_link": null}, {"message": "[ability => manage pricing plan, result => null, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-48410806 data-indent-pad=\"  \"><span class=sf-dump-note>manage pricing plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage pricing plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48410806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.769181, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1487533578 data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487533578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769938, "xdebug_link": null}]}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/contacts", "status_code": "<pre class=sf-dump id=sf-dump-1974575246 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1974575246\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-248859152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-248859152\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1384924177 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1384924177\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2074319447 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik4xNnhTeXI2Z0w5RmgydlFkb3Izemc9PSIsInZhbHVlIjoiWkkveE5WTTF1OHVXRGhHK2N1UGdnVUd0MHhwQ2pNbVRtcnZJMExXM01pTkw3RENocmVsKzg1VWVhY21EbVpFNFh4VkJkUi83Ry9ob2lMcklqdnUzcWRQTWxTTjdaekk4aWtDeHhZNkhId2s5b3Ewc3BJeUtTcU9wenpXUGZiaDFWY0g3TlBlamJldThPd01ieFY1Q0IrWjhHUU13TGhLdEFqOTVPZTZidlFLWjl0WVc4TDBSYnpDcFBUM090L2JVbE9tV1NTODdmaWg3NE1tTjU3YkFvUTJoQXVPakE5OStwWlpENnhzUEdzUTVPMHNFTjFuczZEamNjVWMwTnRwRW1QMmYwZHJMN1EycWhCeEtKVCtsTStHNTI1QUJ1WVNLS2pHTHIyaXU5MjJmYW5scDQ4aUtJdVJvK1ZrbWNWaytMblFtRVBwK21vUkhFU1lDZGVkS3ZpUkJHbzVsNkpmUUprRjhjbGNKdXZLZlZlcjlFQ0c0UEVPOHhzdHFoK1lkQVFoUnJ3aW1XdFFHeWp6YTRGSVFHZDE1aGNSdERLcU42MiswSjRqaXo1SDkvQm1LeHdHQTJVZlplMTNtemN3cmFuakR1ZzNvdnVNRFdrOVRVdVV5YlNraDR4cUFyME5jVEp0WVB6aDdNaWthK2dPMWJJa01vZkVvb01iMzJOWGEiLCJtYWMiOiI5NDExNGExYjNlYmViZTNkZmNjNDJmMDAwOTM4NzFkZGQ3MjI2ZWMwMjYyMzViNjk1MzBjM2ZhOGVkZjdhMmJhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlR6QllyVGVMbFdZV1p2Y2RrV2h3b2c9PSIsInZhbHVlIjoidG5ZYWVxeUZYejh6bFdKR3lPV2J6MVJCWUF4NzV0YVdYQVEzc05SSlZyU3laVWtlV0NwNXY3b3dHdU1ERGthU3NSbkdRR1lVeEVNT0pSQ3FtV0JLbWJWdEJwdnZnTkNTZFJDVEl1YStNL2tMUVJOMlV6Vmt2aWorZldjamtMRDh4MkZadDQybG5wbEVTM1pub1Baalo3Yyt5WFd6dXc0YmRmTy9WSGpDdVhaVFhHVkF5Sk5FYUVTTVIzVFo4aHliamdodnpUa01FNDhnb3BQS0RmcXRLYVJ5Ym50YWJmbkJzNGI5c3ZhRWRTY3dXaS9YMG9Vb0t5WE9YdGk0WnR0N20xV0pOOVJWUXliS3R4bENIcUhpYzJ3T3RMaXU3ckNTVStlSXBYdXBoSktFMHVIV1ZTRWJZY2lja050Z29PKzVYMG50dkxucmlzNHJZZXNnWlJRMU14MU1Mak8yNEhWdFlKWlFtZGNuODdQUDlYYy9Jc1ZET2NSOFNpOTR2TUNxZXlQQUcvV1RUK01Xei9MMVFjOUxmT0ZyMTdsYnZZOGg2Nkt5Rm9HRFN3MklwYTFBYk90NXNQQ1hPb2NYcDJMOWM3dXY4ZkFZNU9PNXJ4YWFWd0NNYk44ak1YS3RIbVZrekJreHF5VXBRRHpJK2FJbEVDQitKSjNLUjV3TkVXV1QiLCJtYWMiOiIzZGRmMDQ3NTRlOTkyMTNjMTM0Mjk4ODU1NTBhZGU2ZjVmMDA3NjBhZTI4NDM1MTVkMmYyMDhhM2RkMDFlYTZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074319447\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-211555455 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211555455\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2131531843 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:47:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1nRzF3cnpleU1NUTM1ajE1THdhS2c9PSIsInZhbHVlIjoiSExFdENsRm0wNi9TV2x5OU4xb1MxWlhaL2dqVm0wemV6UGJYRW40VFFibkhtdTRLQmN3RC8zUllpcVB4ejBtYTI1S0hyaDM4UHFpQ092eVRtTjc5Q1dVdGU1MHRJeHpSdEo3QW15QjljcFV0WkVVK1hRVHFSQ0dUQ255RTdEOG9YNno3ais0czYrUFhGangvMnZ0Q1hGcUp3NDVRVjU3aTgxakFKOXRKSXVpQkN1Y2xyVTU1ZEhCMW14M0FjY2VwYUcvNXpXMWs1Y0pETlVaVzJxNlRORUVrUW1uU3lldFhLMmZ6ano4Q2V2OHdZdFZvYmxodHpQVHdoeHB5NnROWmNIZTArZkVJWW9oR1RjSTNzbjZJbWpGZ2JIdndDQmpNK3dkeXRMSjdRdnl2eUE5Q05SRVhzL1kzOEtPRldkeVR6QmJZOUw2MWFQQm5LMEQzdTBCNHI1SFVxZjlLNWlzNk4rZi9SeHRUSFBXMmV6SmJGdForcWJ3anRocVRldEJSYVkvTXRIOGprSlNuSmRyb25sOElMd3J6VTB5U3JoY0IvT3AwNnNWRGQwa2htNENzRVF2eTl1d3NjZ1VTQnJDeHBRR2RyZTB5L1o0ZkNEckNRRTJIWWpDNk1Gb0RqZ2dKZm45c21hQTE5bXRUdE9UNmVvcyt2NWRsMDQzYm9zSUwiLCJtYWMiOiJmYjZhZDgwZDY5OGVlMmU0YjViYzk4NTY4ZDkwZTcyMmYzYjAzMjVlNmQyMjY3MzQ5NGU5NWJmYzNlMWNhZjExIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:47:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImJlSDRCbDBRYU0xblF2U0huN1Fua2c9PSIsInZhbHVlIjoiWjIyQXhNN2hBU2VQNVVvaVpEVE1lVm9MNzhTL29VQ3Zock1RbVR3UzhwR3ZVdnR4ZGxRTE1DOVFqTFFnYUZnaW1YV0JmeDBlTmtxeURKLzZLalhGNGh5NTlSSnVQUUQ5bTRXcWZ3eWROWU1rUWorZTYveE01ajF4WTRoSGl3eUk3WFUxT05JbUtkZ0Z6clpHNXl5eU5OOFowNHlsWDg4MHRzYlhqV2tvNnR2ajZqODhmMUIyanBYZFoxSUcrelBDZmVQSjkvaDNlSm16STJaU1ZjK3NaemFaVzJCZmI5UUdtWnFIR1ozZHptditNSHVoYjAvK2hkU1RmZnJ1bDhPVXJwbm1KNk5MYWxaMjhVbDNGaXhPMEh3MjRtQVpOVExab3hOWWlHS09UdzJybm5wRWM1cExiWEl5Skx5ZlMyOFlkWnZLK2wzR2VlUGNnL2ZIMW9TVFpyNkIveWxTMkk4Y3p3TTNRUlJEQW9ocHdHb0F3b0F2cmRnZnlwOHMrSVErSlRHR3pWOURzSVBUQXhnRUlGRThNNEZmWHM1QjFMZm9kWEpmVWREMEthU3FtYTVvZlFIR0JWTjVGT0xBb056dStGVTVjakIvZXhRYkF2Mlh1K1FLYXp6cmdFSjN2YVBRanZ0anhJVnRIN2ZYU2JBWXhKcWdPOXhLbTBiOENSa0kiLCJtYWMiOiIxYjYxNGJlMjdiZTk1ZDM4OWNiYWFjZmRkMmVkODZmNmY1YTRiMzRmODAzMDJmYjRkYWM1Y2U3NzZmMGI1NzU0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:47:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1nRzF3cnpleU1NUTM1ajE1THdhS2c9PSIsInZhbHVlIjoiSExFdENsRm0wNi9TV2x5OU4xb1MxWlhaL2dqVm0wemV6UGJYRW40VFFibkhtdTRLQmN3RC8zUllpcVB4ejBtYTI1S0hyaDM4UHFpQ092eVRtTjc5Q1dVdGU1MHRJeHpSdEo3QW15QjljcFV0WkVVK1hRVHFSQ0dUQ255RTdEOG9YNno3ais0czYrUFhGangvMnZ0Q1hGcUp3NDVRVjU3aTgxakFKOXRKSXVpQkN1Y2xyVTU1ZEhCMW14M0FjY2VwYUcvNXpXMWs1Y0pETlVaVzJxNlRORUVrUW1uU3lldFhLMmZ6ano4Q2V2OHdZdFZvYmxodHpQVHdoeHB5NnROWmNIZTArZkVJWW9oR1RjSTNzbjZJbWpGZ2JIdndDQmpNK3dkeXRMSjdRdnl2eUE5Q05SRVhzL1kzOEtPRldkeVR6QmJZOUw2MWFQQm5LMEQzdTBCNHI1SFVxZjlLNWlzNk4rZi9SeHRUSFBXMmV6SmJGdForcWJ3anRocVRldEJSYVkvTXRIOGprSlNuSmRyb25sOElMd3J6VTB5U3JoY0IvT3AwNnNWRGQwa2htNENzRVF2eTl1d3NjZ1VTQnJDeHBRR2RyZTB5L1o0ZkNEckNRRTJIWWpDNk1Gb0RqZ2dKZm45c21hQTE5bXRUdE9UNmVvcyt2NWRsMDQzYm9zSUwiLCJtYWMiOiJmYjZhZDgwZDY5OGVlMmU0YjViYzk4NTY4ZDkwZTcyMmYzYjAzMjVlNmQyMjY3MzQ5NGU5NWJmYzNlMWNhZjExIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:47:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImJlSDRCbDBRYU0xblF2U0huN1Fua2c9PSIsInZhbHVlIjoiWjIyQXhNN2hBU2VQNVVvaVpEVE1lVm9MNzhTL29VQ3Zock1RbVR3UzhwR3ZVdnR4ZGxRTE1DOVFqTFFnYUZnaW1YV0JmeDBlTmtxeURKLzZLalhGNGh5NTlSSnVQUUQ5bTRXcWZ3eWROWU1rUWorZTYveE01ajF4WTRoSGl3eUk3WFUxT05JbUtkZ0Z6clpHNXl5eU5OOFowNHlsWDg4MHRzYlhqV2tvNnR2ajZqODhmMUIyanBYZFoxSUcrelBDZmVQSjkvaDNlSm16STJaU1ZjK3NaemFaVzJCZmI5UUdtWnFIR1ozZHptditNSHVoYjAvK2hkU1RmZnJ1bDhPVXJwbm1KNk5MYWxaMjhVbDNGaXhPMEh3MjRtQVpOVExab3hOWWlHS09UdzJybm5wRWM1cExiWEl5Skx5ZlMyOFlkWnZLK2wzR2VlUGNnL2ZIMW9TVFpyNkIveWxTMkk4Y3p3TTNRUlJEQW9ocHdHb0F3b0F2cmRnZnlwOHMrSVErSlRHR3pWOURzSVBUQXhnRUlGRThNNEZmWHM1QjFMZm9kWEpmVWREMEthU3FtYTVvZlFIR0JWTjVGT0xBb056dStGVTVjakIvZXhRYkF2Mlh1K1FLYXp6cmdFSjN2YVBRanZ0anhJVnRIN2ZYU2JBWXhKcWdPOXhLbTBiOENSa0kiLCJtYWMiOiIxYjYxNGJlMjdiZTk1ZDM4OWNiYWFjZmRkMmVkODZmNmY1YTRiMzRmODAzMDJmYjRkYWM1Y2U3NzZmMGI1NzU0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:47:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131531843\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1934499848 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934499848\", {\"maxDepth\":0})</script>\n"}}