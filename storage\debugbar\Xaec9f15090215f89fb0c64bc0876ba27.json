{"__meta": {"id": "Xaec9f15090215f89fb0c64bc0876ba27", "datetime": "2025-08-02 09:36:34", "utime": **********.357694, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754127393.025806, "end": **********.357728, "duration": 1.3319220542907715, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1754127393.025806, "relative_start": 0, "end": **********.266367, "relative_end": **********.266367, "duration": 1.2405610084533691, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.266413, "relative_start": 1.****************, "end": **********.357738, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "91.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hdTQRSrfVbnoIZ1YjahnrzkIBfZvqbDqKQcAblDo", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-811875939 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-811875939\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1153129108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1153129108\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-468019820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-468019820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-831848960 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831848960\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1452197796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1452197796\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-358112942 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:36:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlrRWNvSjMwbUc2a09JcEphTlVxUHc9PSIsInZhbHVlIjoiUy83ZzI5YWVaLzRhelVzbFVreVpWUlpUWC9tTlR3RWR5V0JXYVZYQ2hJdy9NaTE0ZTN3anVXZkNENndaUnp3UlBDNGpLc3NrVHFFcXo4ckhyZUlrVlJaa0RwclNlNCtsY3loUlhqbUxIbXFJTEFXRC9ZZ3poVTlNWDhOd1NPRlBsNnp2Mzd0TXFUNmFIZGF2Rm4vbXY5ejRrb252TzVEVzVhK29USTIzcEQ5aHQ3c1phTS9GSG45QUZrRjNtM3lqN0ROSlVqNHhFWG14QlFGLzhYL1hUdG1wNU01bTFMT2UvS0xzR29Sa1ZrU0QxNUZOTnZrSS9wVWJodVdxWFBrT0tqME1PZkpYc3FIbXBDdmkrQUd5SE9Ja2RNTTN3MEZmS0NjdmJGcHB0S2pwWHM1M1N0dWZ2UEtpSUoxRFJFTHUyOURNbUtWeEpKTnNCUnlOb28wQzU3TjRlc2xld3JIR2RiRVR2bWlQTURneHJyUFZ1Sm9XNDdoYkJVRXRWRi9iMFlkV2RwZ0g5Qk1LeWptUzdhcDNiaGw2WnQxSUxNMi8yOHU0aDFXazNzcERKRkNlRmNzRCs4VEJ0c0gzVlYyc0d5eFZaaFNJTGJOTHZzbER4VjNRZDV5SURSSGdyeFdyNnNCSlA2bWZvdHZUd0JLMW9Gc0l1SmhqTXlFZFJyNEwiLCJtYWMiOiJiZDFhMTZlYzM1NzdiNWQ2NmExYmU2NjRhODBjZmRlYzkxN2VhOGM0M2NhOGU3NDEyN2Q0M2ZjM2I2YjhhMmRiIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:36:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IklURE9hdHNxMTd1Umt5TWJOYXRFeFE9PSIsInZhbHVlIjoiLzJYQUtOLzNHRkdENkZUVnRSb0V1SXJianhqSHNrenRQbFd5ZHNHcncxZUVuQVZ0cmJCb0JUMlRMOFJ6b09IOVdGYTBjZDVBYUtBcEJZTk1DWmZJTFcxUnRqZXY2T05OaEJETE1NU0JnQjBzMVg1NVJnL1dyNnBPY1ZKc0czQ3p2V1RSR2ZHL1dBenNKbU11cjBLeko4dmpNQVp5bWQ0TlRITTUxbUhsUDczUmR2OU9rTEF6N2xhYVExYi9sM3k5Tk13emROMHJZc0lzMkl5Z2p4NEx4NU5MRGNGV1dxUUU2K01NN3pacGo2eHJmNXMxZjloTVQvUDNmQk5KMmtHSXB4SUp5TVR2ZUVRU3VGbTBwdVFMdGZQS1BPakExbkQ3VmNvb2ZVekRaZnY0dkhub2YvMmZyQzN2OE1haEdIa1lTaTdMVVQzSDVLU3BaNXhOejMzMkNqcXdHOW9nZ3Z6YU9OM1FBOE1CS1BvNmUwT0xFZmFhUjY4MVp6dFVkc1lVZmlZSjJPVnlENmtMNktlQWF5RG1TOFF3M3d6aDBtRytDV3pvdkgyUCt2TVZXdUk0WEJMeVQ5bysydDQ3enh4dGtQNlY3bnlxUHZwRHBjT1Q5V0VlSW9pRUo1dk00SU5Wem5JYWZQNmE2SThsbE90dllKdi9ZNlZKK3Z3MFNLVngiLCJtYWMiOiI2OWZiNTEzNDBmZTA1NGI2ZDIxODJjMzI1NzVmNTVhYzRjNDY4ZDM1ZmRkMTlmMTcwODM0ZDMxZTUyNWIyOWIwIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:36:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlrRWNvSjMwbUc2a09JcEphTlVxUHc9PSIsInZhbHVlIjoiUy83ZzI5YWVaLzRhelVzbFVreVpWUlpUWC9tTlR3RWR5V0JXYVZYQ2hJdy9NaTE0ZTN3anVXZkNENndaUnp3UlBDNGpLc3NrVHFFcXo4ckhyZUlrVlJaa0RwclNlNCtsY3loUlhqbUxIbXFJTEFXRC9ZZ3poVTlNWDhOd1NPRlBsNnp2Mzd0TXFUNmFIZGF2Rm4vbXY5ejRrb252TzVEVzVhK29USTIzcEQ5aHQ3c1phTS9GSG45QUZrRjNtM3lqN0ROSlVqNHhFWG14QlFGLzhYL1hUdG1wNU01bTFMT2UvS0xzR29Sa1ZrU0QxNUZOTnZrSS9wVWJodVdxWFBrT0tqME1PZkpYc3FIbXBDdmkrQUd5SE9Ja2RNTTN3MEZmS0NjdmJGcHB0S2pwWHM1M1N0dWZ2UEtpSUoxRFJFTHUyOURNbUtWeEpKTnNCUnlOb28wQzU3TjRlc2xld3JIR2RiRVR2bWlQTURneHJyUFZ1Sm9XNDdoYkJVRXRWRi9iMFlkV2RwZ0g5Qk1LeWptUzdhcDNiaGw2WnQxSUxNMi8yOHU0aDFXazNzcERKRkNlRmNzRCs4VEJ0c0gzVlYyc0d5eFZaaFNJTGJOTHZzbER4VjNRZDV5SURSSGdyeFdyNnNCSlA2bWZvdHZUd0JLMW9Gc0l1SmhqTXlFZFJyNEwiLCJtYWMiOiJiZDFhMTZlYzM1NzdiNWQ2NmExYmU2NjRhODBjZmRlYzkxN2VhOGM0M2NhOGU3NDEyN2Q0M2ZjM2I2YjhhMmRiIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:36:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IklURE9hdHNxMTd1Umt5TWJOYXRFeFE9PSIsInZhbHVlIjoiLzJYQUtOLzNHRkdENkZUVnRSb0V1SXJianhqSHNrenRQbFd5ZHNHcncxZUVuQVZ0cmJCb0JUMlRMOFJ6b09IOVdGYTBjZDVBYUtBcEJZTk1DWmZJTFcxUnRqZXY2T05OaEJETE1NU0JnQjBzMVg1NVJnL1dyNnBPY1ZKc0czQ3p2V1RSR2ZHL1dBenNKbU11cjBLeko4dmpNQVp5bWQ0TlRITTUxbUhsUDczUmR2OU9rTEF6N2xhYVExYi9sM3k5Tk13emROMHJZc0lzMkl5Z2p4NEx4NU5MRGNGV1dxUUU2K01NN3pacGo2eHJmNXMxZjloTVQvUDNmQk5KMmtHSXB4SUp5TVR2ZUVRU3VGbTBwdVFMdGZQS1BPakExbkQ3VmNvb2ZVekRaZnY0dkhub2YvMmZyQzN2OE1haEdIa1lTaTdMVVQzSDVLU3BaNXhOejMzMkNqcXdHOW9nZ3Z6YU9OM1FBOE1CS1BvNmUwT0xFZmFhUjY4MVp6dFVkc1lVZmlZSjJPVnlENmtMNktlQWF5RG1TOFF3M3d6aDBtRytDV3pvdkgyUCt2TVZXdUk0WEJMeVQ5bysydDQ3enh4dGtQNlY3bnlxUHZwRHBjT1Q5V0VlSW9pRUo1dk00SU5Wem5JYWZQNmE2SThsbE90dllKdi9ZNlZKK3Z3MFNLVngiLCJtYWMiOiI2OWZiNTEzNDBmZTA1NGI2ZDIxODJjMzI1NzVmNTVhYzRjNDY4ZDM1ZmRkMTlmMTcwODM0ZDMxZTUyNWIyOWIwIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:36:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358112942\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-255224906 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hdTQRSrfVbnoIZ1YjahnrzkIBfZvqbDqKQcAblDo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255224906\", {\"maxDepth\":0})</script>\n"}}