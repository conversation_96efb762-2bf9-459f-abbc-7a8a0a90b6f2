{"__meta": {"id": "X66523fc803e8067b12ffbed4455d3006", "datetime": "2025-08-02 10:34:14", "utime": **********.630579, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754130852.885747, "end": **********.63061, "duration": 1.7448630332946777, "duration_str": "1.74s", "measures": [{"label": "Booting", "start": 1754130852.885747, "relative_start": 0, "end": **********.460373, "relative_end": **********.460373, "duration": 1.5746259689331055, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.460409, "relative_start": 1.****************, "end": **********.630613, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LEBwxfaqlgU5aDKHahwD5iog2sP1RfaP9ERQHTdL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1132796005 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1132796005\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-242140632 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-242140632\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1416513641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1416513641\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-867925912 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867925912\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1636379737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1636379737\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1904100527 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:34:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRnMStTVkRIYUlqQk1rSVQ2SklYTnc9PSIsInZhbHVlIjoiYkw1R1RIb0hJNEk0SmNBY2ZCL3pjOEZiYTRhOE1TUHNFTmtFbmhpZ2diMzU0SnJJc0lYLzFoQ0JhL0hCcXMrV3NMUlBlcUc1UmFQendzTEVUYmc3Yng1TjZISVFjdXhtbGVNeW9MZ3RJclN1M001Y3ZJK3JTR2lrQkJsOFVMMGs1MDFHWVhpRVFLMjc4czR0djRZNGtDVG16SWxUS2NLZGNkbnphT3JPVEhMMHNkY0dFYlhMcnI3dVgyVjNpU1FrNmlNSVVHTmgwVk1oS2pidFl1RDVHeFJIM3dTOS9LbWZLak1qZ2d1RXNrMmpQQXMzVXR0Ym00OEJPeEJDQ2VQMGFDWVpaYVlDVXZscENBMWltWjVOeHo2Q3ZVWGtPNXFhWEFwcUxyWkpqTGFhdCt1SEFMNnZqOC91Q3VJN3JSZS8rR1V0OWJvdlR4OXNGRi84VDd6TWVBWVBYc2wrUFlPdm1rVFZhdlpKMHQ1Z3pLMFJLWkJnTENjOUI2U2FWRXRRZkV5MXJqRHU5Zm1QMVFMUnhBRVU0R1Y3RU12Snl2K1RXNDdpS1pOYjJDdnI3Zzh3dzJYamtRZmc5TERxdXhabmh1YzdnQ2xRbW9nQVFDYTNDL3I4U25NbnV6SEhlSDhBWW5EYUI5bDNQTllqbmJGMFNCU1JuQlQxY2ZSRkgxL0kiLCJtYWMiOiJkY2EzMTk2ZjExOWEyYjFlM2Q1NmVhZmJjNTIxMDc5MWVhNmVlNmEwZTUyNGIyZjQwODM5Nzc4MjI0ZWIyNTdkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:34:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJNVTZDemthUU91emNvcUl1V2Zwd3c9PSIsInZhbHVlIjoiQW8zTHJtRXdaZHhWSDJuZHAzckRQOXp2VWMrZkp1R3lGbWYxWlM5aU1EaXdCYjkwRWVrQ0dnZDZpT2RiV2k2K0NxTGxXWVhLNmVlNU5mVXJkY25ZdmNqUTFvSXdvUXBEMk9UckFtcDdPTzE4YXl1WXVvQVZDbzlHTGF5cVlHTUt0d2x0ckt2QVFYalB6U1ljVnhCcWhkRHFnUE9rMm1GeXZVT29TRzJGbWRlQ1I1b0Rwd1N2aGp1UmVibXdPL0x5K2lLSHZUSitLMVU3cGZVeWlUOFdJb2p4clNjOG5PcURJajRUTHBKeDNjQVRZYk8wUU44N2I4RHYyR3lNMkhWeFJIQlc0dGVzT1lvTmg5WTVXZ1dWUFpMYTFPMmpuZXBGcUNqZzZxdXZKVnBJbi9NZUY1YXcrUmFMbm0zMjRTNU83eGFJRURVOGZ1Tm1KdTZGcHZzcE9tVDZDZCtudk1TM2k0NlllWE5JRjIyanVqUFdObk1vT0RoY1YxL3VhbVQ0RHJTeEM0MlI1dEVqVDY4TGZvTHBhcFFiOTN1blpyOXNCRzJ5dUVjVG9SZkh1bHFFTHhQWjFnYVcrblZsTy9jK1pIaXVCTVRyTjNabW5QVnpuc2tmb2k1MGhyUmJsR0dPSHBEcEZqRTd0SnZlRnlLL2laNFpZdHNxOEJvamNiZEIiLCJtYWMiOiI3ZmY2ZTdmYTdkZmQxYzYzYTAxOWQyNzAzODY1NGZkM2Y0MWI1Njk0ZDkzY2QwNDM0YWIwMTI1YTBiOWU4OTFkIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 12:34:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRnMStTVkRIYUlqQk1rSVQ2SklYTnc9PSIsInZhbHVlIjoiYkw1R1RIb0hJNEk0SmNBY2ZCL3pjOEZiYTRhOE1TUHNFTmtFbmhpZ2diMzU0SnJJc0lYLzFoQ0JhL0hCcXMrV3NMUlBlcUc1UmFQendzTEVUYmc3Yng1TjZISVFjdXhtbGVNeW9MZ3RJclN1M001Y3ZJK3JTR2lrQkJsOFVMMGs1MDFHWVhpRVFLMjc4czR0djRZNGtDVG16SWxUS2NLZGNkbnphT3JPVEhMMHNkY0dFYlhMcnI3dVgyVjNpU1FrNmlNSVVHTmgwVk1oS2pidFl1RDVHeFJIM3dTOS9LbWZLak1qZ2d1RXNrMmpQQXMzVXR0Ym00OEJPeEJDQ2VQMGFDWVpaYVlDVXZscENBMWltWjVOeHo2Q3ZVWGtPNXFhWEFwcUxyWkpqTGFhdCt1SEFMNnZqOC91Q3VJN3JSZS8rR1V0OWJvdlR4OXNGRi84VDd6TWVBWVBYc2wrUFlPdm1rVFZhdlpKMHQ1Z3pLMFJLWkJnTENjOUI2U2FWRXRRZkV5MXJqRHU5Zm1QMVFMUnhBRVU0R1Y3RU12Snl2K1RXNDdpS1pOYjJDdnI3Zzh3dzJYamtRZmc5TERxdXhabmh1YzdnQ2xRbW9nQVFDYTNDL3I4U25NbnV6SEhlSDhBWW5EYUI5bDNQTllqbmJGMFNCU1JuQlQxY2ZSRkgxL0kiLCJtYWMiOiJkY2EzMTk2ZjExOWEyYjFlM2Q1NmVhZmJjNTIxMDc5MWVhNmVlNmEwZTUyNGIyZjQwODM5Nzc4MjI0ZWIyNTdkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:34:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJNVTZDemthUU91emNvcUl1V2Zwd3c9PSIsInZhbHVlIjoiQW8zTHJtRXdaZHhWSDJuZHAzckRQOXp2VWMrZkp1R3lGbWYxWlM5aU1EaXdCYjkwRWVrQ0dnZDZpT2RiV2k2K0NxTGxXWVhLNmVlNU5mVXJkY25ZdmNqUTFvSXdvUXBEMk9UckFtcDdPTzE4YXl1WXVvQVZDbzlHTGF5cVlHTUt0d2x0ckt2QVFYalB6U1ljVnhCcWhkRHFnUE9rMm1GeXZVT29TRzJGbWRlQ1I1b0Rwd1N2aGp1UmVibXdPL0x5K2lLSHZUSitLMVU3cGZVeWlUOFdJb2p4clNjOG5PcURJajRUTHBKeDNjQVRZYk8wUU44N2I4RHYyR3lNMkhWeFJIQlc0dGVzT1lvTmg5WTVXZ1dWUFpMYTFPMmpuZXBGcUNqZzZxdXZKVnBJbi9NZUY1YXcrUmFMbm0zMjRTNU83eGFJRURVOGZ1Tm1KdTZGcHZzcE9tVDZDZCtudk1TM2k0NlllWE5JRjIyanVqUFdObk1vT0RoY1YxL3VhbVQ0RHJTeEM0MlI1dEVqVDY4TGZvTHBhcFFiOTN1blpyOXNCRzJ5dUVjVG9SZkh1bHFFTHhQWjFnYVcrblZsTy9jK1pIaXVCTVRyTjNabW5QVnpuc2tmb2k1MGhyUmJsR0dPSHBEcEZqRTd0SnZlRnlLL2laNFpZdHNxOEJvamNiZEIiLCJtYWMiOiI3ZmY2ZTdmYTdkZmQxYzYzYTAxOWQyNzAzODY1NGZkM2Y0MWI1Njk0ZDkzY2QwNDM0YWIwMTI1YTBiOWU4OTFkIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 12:34:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904100527\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1361095965 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LEBwxfaqlgU5aDKHahwD5iog2sP1RfaP9ERQHTdL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361095965\", {\"maxDepth\":0})</script>\n"}}