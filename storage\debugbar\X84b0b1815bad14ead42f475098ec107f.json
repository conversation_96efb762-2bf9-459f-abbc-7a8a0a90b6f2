{"__meta": {"id": "X84b0b1815bad14ead42f475098ec107f", "datetime": "2025-08-02 09:16:09", "utime": **********.617723, "method": "POST", "uri": "/leads/json", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[09:16:09] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\\/json\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.611465, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754126168.481813, "end": **********.61775, "duration": 1.135936975479126, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1754126168.481813, "relative_start": 0, "end": **********.35049, "relative_end": **********.35049, "duration": 0.8686771392822266, "duration_str": "869ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.35052, "relative_start": 0.8687069416046143, "end": **********.617753, "relative_end": 3.0994415283203125e-06, "duration": 0.26723313331604004, "duration_str": "267ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47754088, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads/json", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\LeadController@json", "namespace": null, "prefix": "", "where": [], "as": "leads.json", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1002\" onclick=\"\">app/Http/Controllers/LeadController.php:1002-1018</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.011009999999999999, "accumulated_duration_str": "11.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.483154, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 36.512}, {"sql": "select * from `lead_stages` where `pipeline_id` = '23'", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1010}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4995239, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:1010", "source": "app/Http/Controllers/LeadController.php:1010", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1010", "ajax": false, "filename": "LeadController.php", "line": "1010"}, "connection": "radhe_same", "start_percent": 36.512, "width_percent": 6.63}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.5224159, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 43.143, "width_percent": 6.54}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.548192, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 49.682, "width_percent": 12.807}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.555062, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 62.489, "width_percent": 7.357}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.563903, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 69.846, "width_percent": 30.154}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 555, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/leads/json", "status_code": "<pre class=sf-dump id=sf-dump-1751579124 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1751579124\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1634538265 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1634538265\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-241026813 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241026813\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1165018329 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/leads/18</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im5BOUkycTBDRjdvNWJjdU9hTWFIUWc9PSIsInZhbHVlIjoiSHYyRXYvZTl3d3V1THFEMExGV2FkalB2bHJ4ejVJZVVWTElqRWZVZ2NPSU5QSzRLeW4xSmtEaThVcEU5eFFqb3FZczhlR3BFeE0vQTh3bVFVOFcvSnBhbk85UGxLSE51MmN3azh0QU9sanc4ZE5WU0M1L3NtRytSOHhhVWxtUmx4WmdKcWR2SSt5cVZDRHBBUE5FRXE1aThYWWRxNGp3SWE0MTVGZDBWSlBJek4wNzdtSWs1TTJveElSTEUrQ0xxNnk5MjZPdHB4eG1RQkx0d2tHMUMvMnRSZWRZV056b2ZTbnhYT3AyMFNYQW5RRWMyY0tZV1dBMURjb1pWc3N5N3NWR1hrUTJyT25ZOTl2aUR1LzlHb0o4dEZpTUJkN1FBeG10aFRYSmpiKytLSUgvVkJ6bklRd2NpeWhYZUYwOGN2ODUwbnExOFlxUmt6a0g2ZS8zdUJCMUtjRXg1c05ZbEljcVo3NjJWWWUzTjNIa2E1SVJDYWtvNFFrMzdIZTluUW81WlR0YXhBK3RWRERSS3QzQVpZd2VTYjU3ZlhlTGxpcDZBSytqTXJxYlhYb2s5T0daLzdETmI4aktJYWlFMnBJWEQ3MlgyOWxLSk45c21hZEFvbmdZSHN2a1lLL2JYd082bk9rcko4Vk1EMHQwRS90V3BiMittQkFCaHNLM1UiLCJtYWMiOiI1NWIzMmI5MTNmZGE0NzIwMTExMTU4NmZkODU1MTRmMmZhYTU3NTJkMWMwMGM3MTVmNTYxZjAyYTBkYmQwM2NhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IktHZ2hZY0ZFWElTSG9md1BTWlVNL0E9PSIsInZhbHVlIjoiYmxDUVVDTStmaTROb3JMNUltbFBteURTODh2dFJvZXpLV0ZkOEFpc1Q4MkFzNXdZTlRFU0RGcUtNUTBiL2dXQmM2djJZOWMya3lNTzZyQUg0dTZGd0tKOHR0eWRUdkh5aDhTa01SdUsxeW5CYTRac1BSb0U1Um83ZXpzK2dnL01UMWc2bi80cnozcGZrMjNlMWx6N3poUmNiOEhRSHM3VnJHR25FcmdleTdxL3dqY1NDcFg2Vkwxb29YQVZKbDhWTkxpMlVIVjVyc0ZreTR4R3ZOQWhZV1RHSGw3WG9Tc3NPV2U3enpXNGtOb2pINTRDeGFGaTliSVJyQTlLZmpnT0RWbExieERSMnZMNjJKWk5XNnViQ1RQMTZUaFczVUpCNm9NQWU5REV0VGZ5UUxNT3NEdmNKQWlGU0ZuR2k3Z05OdFhMbG5JT3FWbWF6bHVySXZXSE9sSm53VmxGcFVVZGlISlhXdkZvME1yb2NQNjFEaUk4NmpNWGl4N0JObG9RMklOZWtDczVGeVNaVFhDK3hqeXdqaVFoMmh3V3A0UWhuRE9id3ozQjNJL3QwZ2I3b1o5aTBYc3Jha3V4Vjd1RWFGcjNsY0lQTldzeGpCQm9QcXRvcVIzTnNVRUdrVGVjWWQ1VTdjaUMza2VEZkxKVXFzMXFTZVF2M2RKZFlOajQiLCJtYWMiOiJkNDMwNDBlZmVmYmNkOTAyYzM0Mzc4NTBjMDk3ZmFlODc3OWU3NzYxMzMxY2FhOWM4MWMwZTA5YjY4OTExZjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165018329\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-563088889 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563088889\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-261217121 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 09:16:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9JZTNSa010YUVhMmsxODBuZjhBeHc9PSIsInZhbHVlIjoic0VIbDBIcFh3S01wRFh6V3VDbWthL1QvTDU1Y0I4akNoWFZuOHUzRVRpSGkxbXB1aCsrbXh3dXpta1NLNjg2cVdiWGpiY3ZwZHhQMWRlaEJ1RFJEeGdtRElZRzdzNWVGSitxODhleTFEdEFCd1hrY2dQVGY2K3ZoZ1RpdEpYZEhaTjVIcmM5YU9lUTkxM1pZMW9rRTVLRHlIb3loM3cwUHp1bzVSbUhVMTlSbW51enpzbGtOMjBYMGRLbEVPMGhuUEtSc2JVYVFmNEgxVUhOOVY2QWxVMUFrNWV2RFlUYzF5Mi9OVEh2Q21qT3dHTURNRlJoNXRXdTZYWUhUc3NtTFEyTXRkN041YWtzNVdEcUpBbTM3aWlna0l4VXFLYnhRTmJjNjJxMFY1ZXZEWXJkRXdVbGxwcURPU1RNWGU5TktWL2RQUXY3cnJVMDFMa1lTMktVOUYzcjNzNXpaU0pQMEI0bWZ0ZXRFMCszTDFNb1hnRDEzRWpqTmtvb2s4ck9NUWx5UW0yUG9TWWRETGNqMnZWZ2MzU3NucXdyTkhCZ2FPQ1dOT2JyZW8yb2VwMjBmMlI5dnl1eW9aTDNsUjhqbmtIOGNlK0RTUWNBYzhISDlHa1M5dWFoeTU3bjdFOXpPdi9tV1Flc3BYM2UwRHFxQ0ZibkpYcm1UM1R0MFJoN1kiLCJtYWMiOiJjZDIwNjgyMzZmMzgyYWRkNWI2ZGJhNzk5MDhmMGE5NTI4NGJkMTM0ZDU0Y2ZlZDU1YWZiMTFjOWVjYjNlMDQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:16:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikp0Y1kwcitMNWY0OHBMWWtUM3puSGc9PSIsInZhbHVlIjoiVDRmMjM1bWpNN2hLUGhmMGFXTEg1ZzVaWi9rTUVyVXBxQTdVN21yd1ovcG93aFpSRlYwOVFVZHd0UGhiMHlVU2RIS2YwWis5ZEtZSHF1amJtUGFkU2czY3lRVzlEcTdNbnY1dXNOa3ZCK3lJVDlObVNvNWZxbEZsY2RHR1lrczRnT2FqQkRPYkJ1NXA4ekE1NUNUMHhCdWxZMEtEb2c4YnRlS0FiazVHZXlpRkorTktGRngrWkhnOVRaWEVyZHRlOVBib3ZjUEtzZ2J0VE1hVG51bXZtcGRvR3dINTdtSmFvTmFONytwYms3NFNxUmZ6NTdLbm5GQndUbElJM2Z0YzFJYllmMHIvVlhOMURhZGt3QkZkbDd1QnRwcTdZa2MyaHlEclcwbXRpd0ZqaVlzdGkrSWJZbUtWY0x0Mld6V1dZcVQvL3lPbS9FTU5FZHUxNVE0aHNvVlFUM1ZlZUlLYWVxMzh2Q24xSkNuSkl3bU9jRXRsMUVnTlhqY3JCTDZyTEdjNXlKY3NQemxtK3JGdUw0amJFclpwdXFzaFhlVjRkSnFRK2puTmNSRFJmYzFDczZVa21TQkpWT1BrNkNySVZqT1UxcXIxcFZnWmJkTXNOazFuaWJ6UHdRNFhPZGg1SEZxR2lJcjZkSDJDTURBSjMvWDFjWUlEN0pURWpGTHQiLCJtYWMiOiIxNTExMjkwZGViZmE0ODVjY2UyZWM2MWE2NzA0ODQ5YjY2MDdhNTk5MWRkOTZiN2ZhZWYwMDcyMWUxZTBjOWI3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 11:16:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9JZTNSa010YUVhMmsxODBuZjhBeHc9PSIsInZhbHVlIjoic0VIbDBIcFh3S01wRFh6V3VDbWthL1QvTDU1Y0I4akNoWFZuOHUzRVRpSGkxbXB1aCsrbXh3dXpta1NLNjg2cVdiWGpiY3ZwZHhQMWRlaEJ1RFJEeGdtRElZRzdzNWVGSitxODhleTFEdEFCd1hrY2dQVGY2K3ZoZ1RpdEpYZEhaTjVIcmM5YU9lUTkxM1pZMW9rRTVLRHlIb3loM3cwUHp1bzVSbUhVMTlSbW51enpzbGtOMjBYMGRLbEVPMGhuUEtSc2JVYVFmNEgxVUhOOVY2QWxVMUFrNWV2RFlUYzF5Mi9OVEh2Q21qT3dHTURNRlJoNXRXdTZYWUhUc3NtTFEyTXRkN041YWtzNVdEcUpBbTM3aWlna0l4VXFLYnhRTmJjNjJxMFY1ZXZEWXJkRXdVbGxwcURPU1RNWGU5TktWL2RQUXY3cnJVMDFMa1lTMktVOUYzcjNzNXpaU0pQMEI0bWZ0ZXRFMCszTDFNb1hnRDEzRWpqTmtvb2s4ck9NUWx5UW0yUG9TWWRETGNqMnZWZ2MzU3NucXdyTkhCZ2FPQ1dOT2JyZW8yb2VwMjBmMlI5dnl1eW9aTDNsUjhqbmtIOGNlK0RTUWNBYzhISDlHa1M5dWFoeTU3bjdFOXpPdi9tV1Flc3BYM2UwRHFxQ0ZibkpYcm1UM1R0MFJoN1kiLCJtYWMiOiJjZDIwNjgyMzZmMzgyYWRkNWI2ZGJhNzk5MDhmMGE5NTI4NGJkMTM0ZDU0Y2ZlZDU1YWZiMTFjOWVjYjNlMDQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:16:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikp0Y1kwcitMNWY0OHBMWWtUM3puSGc9PSIsInZhbHVlIjoiVDRmMjM1bWpNN2hLUGhmMGFXTEg1ZzVaWi9rTUVyVXBxQTdVN21yd1ovcG93aFpSRlYwOVFVZHd0UGhiMHlVU2RIS2YwWis5ZEtZSHF1amJtUGFkU2czY3lRVzlEcTdNbnY1dXNOa3ZCK3lJVDlObVNvNWZxbEZsY2RHR1lrczRnT2FqQkRPYkJ1NXA4ekE1NUNUMHhCdWxZMEtEb2c4YnRlS0FiazVHZXlpRkorTktGRngrWkhnOVRaWEVyZHRlOVBib3ZjUEtzZ2J0VE1hVG51bXZtcGRvR3dINTdtSmFvTmFONytwYms3NFNxUmZ6NTdLbm5GQndUbElJM2Z0YzFJYllmMHIvVlhOMURhZGt3QkZkbDd1QnRwcTdZa2MyaHlEclcwbXRpd0ZqaVlzdGkrSWJZbUtWY0x0Mld6V1dZcVQvL3lPbS9FTU5FZHUxNVE0aHNvVlFUM1ZlZUlLYWVxMzh2Q24xSkNuSkl3bU9jRXRsMUVnTlhqY3JCTDZyTEdjNXlKY3NQemxtK3JGdUw0amJFclpwdXFzaFhlVjRkSnFRK2puTmNSRFJmYzFDczZVa21TQkpWT1BrNkNySVZqT1UxcXIxcFZnWmJkTXNOazFuaWJ6UHdRNFhPZGg1SEZxR2lJcjZkSDJDTURBSjMvWDFjWUlEN0pURWpGTHQiLCJtYWMiOiIxNTExMjkwZGViZmE0ODVjY2UyZWM2MWE2NzA0ODQ5YjY2MDdhNTk5MWRkOTZiN2ZhZWYwMDcyMWUxZTBjOWI3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 11:16:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261217121\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1327982712 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327982712\", {\"maxDepth\":0})</script>\n"}}