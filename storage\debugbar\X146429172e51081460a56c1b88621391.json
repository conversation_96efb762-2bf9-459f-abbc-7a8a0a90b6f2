{"__meta": {"id": "X146429172e51081460a56c1b88621391", "datetime": "2025-08-02 08:40:44", "utime": **********.696674, "method": "POST", "uri": "/lead_stages/move-lead", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 8, "messages": [{"message": "[08:40:42] LOG.info: Starting webhook dispatch for action: crm.lead_stage_changed {\n    \"timestamp\": \"2025-08-02T08:40:42.375975Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_stage_changed\",\n    \"user_id\": 79,\n    \"entity_type\": \"Lead\",\n    \"entity_id\": 17,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.380384, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:42] LOG.info: ModuleWebhookService: Starting webhook dispatch for action: crm.lead_stage_changed", "message_html": null, "is_string": false, "label": "info", "time": **********.38191, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:42] LOG.info: Found 1 enabled integrations for webhook action: crm.lead_stage_changed", "message_html": null, "is_string": false, "label": "info", "time": **********.396242, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:42] LOG.info: Sending webhook to integration: OMX FLOW at http://127.0.0.1:2000/", "message_html": null, "is_string": false, "label": "info", "time": **********.397088, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:44] LOG.error: <PERSON>hook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-08-02T08:40:44.542333Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_stage_changed\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 2145,\n    \"user_id\": 79,\n    \"entity_id\": 17,\n    \"entity_type\": \"Lead\",\n    \"request_payload\": {\n        \"action\": \"crm.lead_stage_changed\",\n        \"timestamp\": \"2025-08-02T08:40:42.408029Z\",\n        \"data\": {\n            \"id\": 17,\n            \"name\": \"<PERSON> <PERSON><PERSON><PERSON>\",\n            \"contact_type\": \"Lead\",\n            \"tags\": [\n                {\n                    \"id\": 89,\n                    \"name\": \"Cold Lead\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 92,\n                    \"name\": \"Follow Up\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 93,\n                    \"name\": \"New Customer\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 91,\n                    \"name\": \"VIP\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                },\n                {\n                    \"id\": 90,\n                    \"name\": \"Warm Lead\",\n                    \"created_by\": 79,\n                    \"is_active\": 1,\n                    \"created_at\": \"2025-07-30T17:17:23.000000Z\",\n                    \"updated_at\": \"2025-07-30T17:17:23.000000Z\"\n                }\n            ],\n            \"postal_code\": null,\n            \"city\": null,\n            \"state\": null,\n            \"country\": null,\n            \"business_name\": null,\n            \"business_gst\": null,\n            \"business_state\": null,\n            \"business_postal_code\": null,\n            \"business_address\": null,\n            \"dnd_settings\": null,\n            \"email\": \"<EMAIL>\",\n            \"phone\": \"+918975620126\",\n            \"date_of_birth\": \"2025-07-30\",\n            \"type\": \"\",\n            \"status\": \"\",\n            \"opportunity_info\": null,\n            \"opportunity_description\": \"ok\",\n            \"opportunity_source\": \"phone\",\n            \"lead_value\": \"8900.00\",\n            \"subject\": \"Lead from Mr Mukharji\",\n            \"user_id\": 81,\n            \"pipeline_id\": 23,\n            \"stage_id\": 87,\n            \"contact_group_id\": null,\n            \"sources\": [],\n            \"products\": [],\n            \"notes\": null,\n            \"labels\": [],\n            \"custom_fields\": null,\n            \"order\": 0,\n            \"created_by\": 79,\n            \"is_deleted\": 0,\n            \"is_active\": 1,\n            \"is_converted\": 0,\n            \"date\": \"2025-08-02\",\n            \"next_follow_up_date\": \"2025-08-23\",\n            \"created_at\": \"2025-08-02T08:39:44.000000Z\",\n            \"updated_at\": \"2025-08-02T08:39:44.000000Z\",\n            \"stage\": {\n                \"id\": 87,\n                \"name\": \"Qualified\",\n                \"pipeline_id\": 23,\n                \"created_by\": 79,\n                \"order\": 1,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:03.000000Z\"\n            },\n            \"users\": [\n                {\n                    \"id\": 79,\n                    \"name\": \"Parichay Singha AI\",\n                    \"email\": \"<EMAIL>\",\n                    \"email_verified_at\": \"2025-07-19T06:00:35.000000Z\",\n                    \"plan\": 11,\n                    \"plan_expire_date\": \"2026-07-31\",\n                    \"requested_plan\": 0,\n                    \"trial_plan\": 0,\n                    \"trial_expire_date\": null,\n                    \"type\": \"company\",\n                    \"system_admin_company_id\": null,\n                    \"company_name\": null,\n                    \"company_description\": null,\n                    \"module_permissions\": {\n                        \"crm\": [\n                            \"create lead\",\n                            \"create deal\",\n                            \"create form builder\",\n                            \"create contract\",\n                            \"create pipeline\",\n                            \"create stage\",\n                            \"create source\",\n                            \"create label\",\n                            \"view crm dashboard\",\n                            \"view lead\",\n                            \"view deal\",\n                            \"view form builder\",\n                            \"view contract\",\n                            \"view pipeline\",\n                            \"view stage\",\n                            \"view source\",\n                            \"view label\",\n                            \"delete lead\",\n                            \"delete deal\",\n                            \"delete form builder\",\n                            \"delete contract\",\n                            \"delete pipeline\",\n                            \"delete stage\",\n                            \"delete source\",\n                            \"delete label\",\n                            \"manage lead\",\n                            \"edit lead\",\n                            \"manage deal\",\n                            \"edit deal\",\n                            \"manage form builder\",\n                            \"edit form builder\",\n                            \"manage contract\",\n                            \"edit contract\",\n                            \"manage pipeline\",\n                            \"edit pipeline\",\n                            \"manage stage\",\n                            \"edit stage\",\n                            \"manage source\",\n                            \"edit source\",\n                            \"manage label\",\n                            \"edit label\"\n                        ],\n                        \"hrm\": [\n                            \"create employee\",\n                            \"create set salary\",\n                            \"create pay slip\",\n                            \"create leave\",\n                            \"create attendance\",\n                            \"create training\",\n                            \"create award\",\n                            \"create branch\",\n                            \"create department\",\n                            \"create designation\",\n                            \"create document type\",\n                            \"view hrm dashboard\",\n                            \"view employee\",\n                            \"view set salary\",\n                            \"view pay slip\",\n                            \"view leave\",\n                            \"view attendance\",\n                            \"view training\",\n                            \"view award\",\n                            \"view branch\",\n                            \"view department\",\n                            \"view designation\",\n                            \"view document type\",\n                            \"delete employee\",\n                            \"delete set salary\",\n                            \"delete pay slip\",\n                            \"delete leave\",\n                            \"delete attendance\",\n                            \"delete training\",\n                            \"delete award\",\n                            \"delete branch\",\n                            \"delete department\",\n                            \"delete designation\",\n                            \"delete document type\",\n                            \"manage employee\",\n                            \"edit employee\",\n                            \"manage set salary\",\n                            \"edit set salary\",\n                            \"manage pay slip\",\n                            \"edit pay slip\",\n                            \"manage leave\",\n                            \"edit leave\",\n                            \"manage attendance\",\n                            \"edit attendance\",\n                            \"manage training\",\n                            \"edit training\",\n                            \"manage award\",\n                            \"edit award\",\n                            \"manage branch\",\n                            \"edit branch\",\n                            \"manage department\",\n                            \"edit department\",\n                            \"manage designation\",\n                            \"edit designation\",\n                            \"manage document type\",\n                            \"edit document type\"\n                        ],\n                        \"account\": [\n                            \"create customer\",\n                            \"create vender\",\n                            \"create invoice\",\n                            \"create bill\",\n                            \"create revenue\",\n                            \"create payment\",\n                            \"create proposal\",\n                            \"create goal\",\n                            \"create credit note\",\n                            \"create debit note\",\n                            \"create bank account\",\n                            \"create bank transfer\",\n                            \"create transaction\",\n                            \"create chart of account\",\n                            \"create journal entry\",\n                            \"create assets\",\n                            \"create constant custom field\",\n                            \"view account dashboard\",\n                            \"view customer\",\n                            \"view vender\",\n                            \"view invoice\",\n                            \"view bill\",\n                            \"view revenue\",\n                            \"view payment\",\n                            \"view proposal\",\n                            \"view goal\",\n                            \"view credit note\",\n                            \"view debit note\",\n                            \"view bank account\",\n                            \"view bank transfer\",\n                            \"view transaction\",\n                            \"view chart of account\",\n                            \"view journal entry\",\n                            \"view assets\",\n                            \"view constant custom field\",\n                            \"view report\",\n                            \"delete customer\",\n                            \"delete vender\",\n                            \"delete invoice\",\n                            \"delete bill\",\n                            \"delete revenue\",\n                            \"delete payment\",\n                            \"delete proposal\",\n                            \"delete goal\",\n                            \"delete credit note\",\n                            \"delete debit note\",\n                            \"delete bank account\",\n                            \"delete bank transfer\",\n                            \"delete transaction\",\n                            \"delete chart of account\",\n                            \"delete journal entry\",\n                            \"delete assets\",\n                            \"delete constant custom field\",\n                            \"manage customer\",\n                            \"edit customer\",\n                            \"manage vender\",\n                            \"edit vender\",\n                            \"manage invoice\",\n                            \"edit invoice\",\n                            \"manage bill\",\n                            \"edit bill\",\n                            \"manage revenue\",\n                            \"edit revenue\",\n                            \"manage payment\",\n                            \"edit payment\",\n                            \"manage proposal\",\n                            \"edit proposal\",\n                            \"manage goal\",\n                            \"edit goal\",\n                            \"manage credit note\",\n                            \"edit credit note\",\n                            \"manage debit note\",\n                            \"edit debit note\",\n                            \"manage bank account\",\n                            \"edit bank account\",\n                            \"manage bank transfer\",\n                            \"edit bank transfer\",\n                            \"manage transaction\",\n                            \"edit transaction\",\n                            \"manage chart of account\",\n                            \"edit chart of account\",\n                            \"manage journal entry\",\n                            \"edit journal entry\",\n                            \"manage assets\",\n                            \"edit assets\",\n                            \"manage constant custom field\",\n                            \"edit constant custom field\",\n                            \"manage report\"\n                        ],\n                        \"project\": [\n                            \"create project\",\n                            \"create project task\",\n                            \"create timesheet\",\n                            \"create bug report\",\n                            \"create milestone\",\n                            \"create project stage\",\n                            \"create project task stage\",\n                            \"create project expense\",\n                            \"create activity\",\n                            \"create bug status\",\n                            \"view project dashboard\",\n                            \"view project\",\n                            \"view project task\",\n                            \"view timesheet\",\n                            \"view bug report\",\n                            \"view milestone\",\n                            \"view project stage\",\n                            \"view project task stage\",\n                            \"view project expense\",\n                            \"view activity\",\n                            \"view bug status\",\n                            \"delete project\",\n                            \"delete project task\",\n                            \"delete timesheet\",\n                            \"delete bug report\",\n                            \"delete milestone\",\n                            \"delete project stage\",\n                            \"delete project task stage\",\n                            \"delete project expense\",\n                            \"delete activity\",\n                            \"delete bug status\",\n                            \"manage project\",\n                            \"edit project\",\n                            \"manage project task\",\n                            \"edit project task\",\n                            \"manage timesheet\",\n                            \"edit timesheet\",\n                            \"manage bug report\",\n                            \"edit bug report\",\n                            \"manage milestone\",\n                            \"edit milestone\",\n                            \"manage project stage\",\n                            \"edit project stage\",\n                            \"manage project task stage\",\n                            \"edit project task stage\",\n                            \"manage project expense\",\n                            \"edit project expense\",\n                            \"manage activity\",\n                            \"edit activity\",\n                            \"manage bug status\",\n                            \"edit bug status\"\n                        ],\n                        \"pos\": [\n                            \"create warehouse\",\n                            \"create purchase\",\n                            \"create quotation\",\n                            \"create pos\",\n                            \"create barcode\",\n                            \"create product\",\n                            \"create product category\",\n                            \"create product unit\",\n                            \"view pos dashboard\",\n                            \"view warehouse\",\n                            \"view purchase\",\n                            \"view quotation\",\n                            \"view pos\",\n                            \"view product\",\n                            \"view product category\",\n                            \"view product unit\",\n                            \"delete warehouse\",\n                            \"delete purchase\",\n                            \"delete quotation\",\n                            \"delete pos\",\n                            \"delete product\",\n                            \"delete product category\",\n                            \"delete product unit\",\n                            \"manage warehouse\",\n                            \"edit warehouse\",\n                            \"manage purchase\",\n                            \"edit purchase\",\n                            \"manage quotation\",\n                            \"edit quotation\",\n                            \"manage pos\",\n                            \"edit pos\",\n                            \"manage product\",\n                            \"edit product\",\n                            \"manage product category\",\n                            \"edit product category\",\n                            \"manage product unit\",\n                            \"edit product unit\"\n                        ],\n                        \"support\": [\n                            \"create support\",\n                            \"view support dashboard\",\n                            \"view support\",\n                            \"delete support\",\n                            \"manage support\",\n                            \"edit support\",\n                            \"reply support\"\n                        ],\n                        \"user_management\": [\n                            \"create user\",\n                            \"create client\",\n                            \"view user\",\n                            \"view client\",\n                            \"delete user\",\n                            \"delete client\",\n                            \"manage user\",\n                            \"edit user\",\n                            \"manage client\",\n                            \"edit client\"\n                        ],\n                        \"booking\": [\n                            \"create booking\",\n                            \"create appointment\",\n                            \"create appointment booking\",\n                            \"create calendar event\",\n                            \"view booking dashboard\",\n                            \"view booking\",\n                            \"show booking\",\n                            \"view appointment\",\n                            \"show appointment\",\n                            \"view appointment booking\",\n                            \"show appointment booking\",\n                            \"view calendar event\",\n                            \"show calendar event\",\n                            \"delete booking\",\n                            \"delete appointment\",\n                            \"delete appointment booking\",\n                            \"delete calendar event\",\n                            \"manage booking\",\n                            \"edit booking\",\n                            \"manage appointment\",\n                            \"edit appointment\",\n                            \"manage appointment booking\",\n                            \"edit appointment booking\",\n                            \"manage calendar event\",\n                            \"edit calendar event\"\n                        ],\n                        \"omx_flow\": [\n                            \"access omx flow\",\n                            \"whatsapp_flows\",\n                            \"whatsapp_orders\",\n                            \"campaigns\",\n                            \"templates\",\n                            \"chatbot\"\n                        ],\n                        \"personal_tasks\": [\n                            \"create personal task\",\n                            \"create personal task comment\",\n                            \"create personal task file\",\n                            \"create personal task checklist\",\n                            \"view personal task\",\n                            \"delete personal task\",\n                            \"delete personal task comment\",\n                            \"delete personal task file\",\n                            \"delete personal task checklist\",\n                            \"manage personal task\",\n                            \"edit personal task\",\n                            \"edit personal task comment\",\n                            \"edit personal task checklist\",\n                            \"manage personal task time tracking\"\n                        ],\n                        \"automatish\": [\n                            \"access automatish\"\n                        ]\n                    },\n                    \"storage_limit\": 0,\n                    \"avatar\": \"logo-dark-removebg-preview_1752904903.png\",\n                    \"messenger_color\": \"#2180f3\",\n                    \"lang\": \"en\",\n                    \"default_pipeline\": 23,\n                    \"active_status\": 0,\n                    \"delete_status\": 1,\n                    \"mode\": \"light\",\n                    \"dark_mode\": 0,\n                    \"is_disable\": 1,\n                    \"is_enable_login\": 1,\n                    \"is_active\": 1,\n                    \"referral_code\": 0,\n                    \"used_referral_code\": 0,\n                    \"commission_amount\": 0,\n                    \"last_login_at\": null,\n                    \"created_by\": 7,\n                    \"created_at\": \"2025-07-19T06:00:35.000000Z\",\n                    \"updated_at\": \"2025-07-31T06:32:06.000000Z\",\n                    \"is_email_verified\": 0,\n                    \"profile\": \"http:\\/\\/localhost:8000\\/storage\\/avatar.png\",\n                    \"pivot\": {\n                        \"lead_id\": 17,\n                        \"user_id\": 79\n                    }\n                },\n                {\n                    \"id\": 81,\n                    \"name\": \"Gungun Rani\",\n                    \"email\": \"<EMAIL>\",\n                    \"email_verified_at\": \"2025-07-19T07:03:24.000000Z\",\n                    \"plan\": null,\n                    \"plan_expire_date\": null,\n                    \"requested_plan\": 0,\n                    \"trial_plan\": 0,\n                    \"trial_expire_date\": null,\n                    \"type\": \"employee\",\n                    \"system_admin_company_id\": null,\n                    \"company_name\": null,\n                    \"company_description\": null,\n                    \"module_permissions\": {\n                        \"crm\": [\n                            \"create lead\",\n                            \"create deal\",\n                            \"create form builder\",\n                            \"create contract\",\n                            \"create pipeline\",\n                            \"create stage\",\n                            \"create source\",\n                            \"create label\",\n                            \"view crm dashboard\",\n                            \"view lead\",\n                            \"view deal\",\n                            \"view form builder\",\n                            \"view contract\",\n                            \"view pipeline\",\n                            \"view stage\",\n                            \"view source\",\n                            \"view label\",\n                            \"delete lead\",\n                            \"delete deal\",\n                            \"delete form builder\",\n                            \"delete contract\",\n                            \"delete pipeline\",\n                            \"delete stage\",\n                            \"delete source\",\n                            \"delete label\",\n                            \"manage lead\",\n                            \"edit lead\",\n                            \"manage deal\",\n                            \"edit deal\",\n                            \"manage form builder\",\n                            \"edit form builder\",\n                            \"manage contract\",\n                            \"edit contract\",\n                            \"manage pipeline\",\n                            \"edit pipeline\",\n                            \"manage stage\",\n                            \"edit stage\",\n                            \"manage source\",\n                            \"edit source\",\n                            \"manage label\",\n                            \"edit label\"\n                        ],\n                        \"hrm\": [\n                            \"create employee\",\n                            \"create set salary\",\n                            \"create pay slip\",\n                            \"create leave\",\n                            \"create attendance\",\n                            \"create training\",\n                            \"create award\",\n                            \"create branch\",\n                            \"create department\",\n                            \"create designation\",\n                            \"create document type\",\n                            \"view hrm dashboard\",\n                            \"view employee\",\n                            \"view set salary\",\n                            \"view pay slip\",\n                            \"view leave\",\n                            \"view attendance\",\n                            \"view training\",\n                            \"view award\",\n                            \"view branch\",\n                            \"view department\",\n                            \"view designation\",\n                            \"view document type\",\n                            \"delete employee\",\n                            \"delete set salary\",\n                            \"delete pay slip\",\n                            \"delete leave\",\n                            \"delete attendance\",\n                            \"delete training\",\n                            \"delete award\",\n                            \"delete branch\",\n                            \"delete department\",\n                            \"delete designation\",\n                            \"delete document type\",\n                            \"manage employee\",\n                            \"edit employee\",\n                            \"manage set salary\",\n                            \"edit set salary\",\n                            \"manage pay slip\",\n                            \"edit pay slip\",\n                            \"manage leave\",\n                            \"edit leave\",\n                            \"manage attendance\",\n                            \"edit attendance\",\n                            \"manage training\",\n                            \"edit training\",\n                            \"manage award\",\n                            \"edit award\",\n                            \"manage branch\",\n                            \"edit branch\",\n                            \"manage department\",\n                            \"edit department\",\n                            \"manage designation\",\n                            \"edit designation\",\n                            \"manage document type\",\n                            \"edit document type\"\n                        ],\n                        \"account\": [\n                            \"create customer\",\n                            \"create vender\",\n                            \"create invoice\",\n                            \"create bill\",\n                            \"create revenue\",\n                            \"create payment\",\n                            \"create proposal\",\n                            \"create goal\",\n                            \"create credit note\",\n                            \"create debit note\",\n                            \"create bank account\",\n                            \"create bank transfer\",\n                            \"create transaction\",\n                            \"create chart of account\",\n                            \"create journal entry\",\n                            \"create assets\",\n                            \"create constant custom field\",\n                            \"view account dashboard\",\n                            \"view customer\",\n                            \"view vender\",\n                            \"view invoice\",\n                            \"view bill\",\n                            \"view revenue\",\n                            \"view payment\",\n                            \"view proposal\",\n                            \"view goal\",\n                            \"view credit note\",\n                            \"view debit note\",\n                            \"view bank account\",\n                            \"view bank transfer\",\n                            \"view transaction\",\n                            \"view chart of account\",\n                            \"view journal entry\",\n                            \"view assets\",\n                            \"view constant custom field\",\n                            \"view report\",\n                            \"delete customer\",\n                            \"delete vender\",\n                            \"delete invoice\",\n                            \"delete bill\",\n                            \"delete revenue\",\n                            \"delete payment\",\n                            \"delete proposal\",\n                            \"delete goal\",\n                            \"delete credit note\",\n                            \"delete debit note\",\n                            \"delete bank account\",\n                            \"delete bank transfer\",\n                            \"delete transaction\",\n                            \"delete chart of account\",\n                            \"delete journal entry\",\n                            \"delete assets\",\n                            \"delete constant custom field\",\n                            \"manage customer\",\n                            \"edit customer\",\n                            \"manage vender\",\n                            \"edit vender\",\n                            \"manage invoice\",\n                            \"edit invoice\",\n                            \"manage bill\",\n                            \"edit bill\",\n                            \"manage revenue\",\n                            \"edit revenue\",\n                            \"manage payment\",\n                            \"edit payment\",\n                            \"manage proposal\",\n                            \"edit proposal\",\n                            \"manage goal\",\n                            \"edit goal\",\n                            \"manage credit note\",\n                            \"edit credit note\",\n                            \"manage debit note\",\n                            \"edit debit note\",\n                            \"manage bank account\",\n                            \"edit bank account\",\n                            \"manage bank transfer\",\n                            \"edit bank transfer\",\n                            \"manage transaction\",\n                            \"edit transaction\",\n                            \"manage chart of account\",\n                            \"edit chart of account\",\n                            \"manage journal entry\",\n                            \"edit journal entry\",\n                            \"manage assets\",\n                            \"edit assets\",\n                            \"manage constant custom field\",\n                            \"edit constant custom field\",\n                            \"manage report\"\n                        ],\n                        \"project\": [\n                            \"create project\",\n                            \"create project task\",\n                            \"create timesheet\",\n                            \"create bug report\",\n                            \"create milestone\",\n                            \"create project stage\",\n                            \"create project task stage\",\n                            \"create project expense\",\n                            \"create activity\",\n                            \"create bug status\",\n                            \"view project dashboard\",\n                            \"view project\",\n                            \"view project task\",\n                            \"view timesheet\",\n                            \"view bug report\",\n                            \"view milestone\",\n                            \"view project stage\",\n                            \"view project task stage\",\n                            \"view project expense\",\n                            \"view activity\",\n                            \"view bug status\",\n                            \"delete project\",\n                            \"delete project task\",\n                            \"delete timesheet\",\n                            \"delete bug report\",\n                            \"delete milestone\",\n                            \"delete project stage\",\n                            \"delete project task stage\",\n                            \"delete project expense\",\n                            \"delete activity\",\n                            \"delete bug status\",\n                            \"manage project\",\n                            \"edit project\",\n                            \"manage project task\",\n                            \"edit project task\",\n                            \"manage timesheet\",\n                            \"edit timesheet\",\n                            \"manage bug report\",\n                            \"edit bug report\",\n                            \"manage milestone\",\n                            \"edit milestone\",\n                            \"manage project stage\",\n                            \"edit project stage\",\n                            \"manage project task stage\",\n                            \"edit project task stage\",\n                            \"manage project expense\",\n                            \"edit project expense\",\n                            \"manage activity\",\n                            \"edit activity\",\n                            \"manage bug status\",\n                            \"edit bug status\"\n                        ],\n                        \"pos\": [\n                            \"create warehouse\",\n                            \"create purchase\",\n                            \"create quotation\",\n                            \"create pos\",\n                            \"create barcode\",\n                            \"create product\",\n                            \"create product category\",\n                            \"create product unit\",\n                            \"view pos dashboard\",\n                            \"view warehouse\",\n                            \"view purchase\",\n                            \"view quotation\",\n                            \"view pos\",\n                            \"view product\",\n                            \"view product category\",\n                            \"view product unit\",\n                            \"delete warehouse\",\n                            \"delete purchase\",\n                            \"delete quotation\",\n                            \"delete pos\",\n                            \"delete product\",\n                            \"delete product category\",\n                            \"delete product unit\",\n                            \"manage warehouse\",\n                            \"edit warehouse\",\n                            \"manage purchase\",\n                            \"edit purchase\",\n                            \"manage quotation\",\n                            \"edit quotation\",\n                            \"manage pos\",\n                            \"edit pos\",\n                            \"manage product\",\n                            \"edit product\",\n                            \"manage product category\",\n                            \"edit product category\",\n                            \"manage product unit\",\n                            \"edit product unit\"\n                        ],\n                        \"support\": [\n                            \"create support\",\n                            \"view support dashboard\",\n                            \"view support\",\n                            \"delete support\",\n                            \"manage support\",\n                            \"edit support\",\n                            \"reply support\"\n                        ],\n                        \"user_management\": [\n                            \"create user\",\n                            \"create client\",\n                            \"view user\",\n                            \"view client\",\n                            \"delete user\",\n                            \"delete client\",\n                            \"manage user\",\n                            \"edit user\",\n                            \"manage client\",\n                            \"edit client\"\n                        ],\n                        \"booking\": [\n                            \"create booking\",\n                            \"create appointment\",\n                            \"create appointment booking\",\n                            \"create calendar event\",\n                            \"view booking dashboard\",\n                            \"view booking\",\n                            \"show booking\",\n                            \"view appointment\",\n                            \"show appointment\",\n                            \"view appointment booking\",\n                            \"show appointment booking\",\n                            \"view calendar event\",\n                            \"show calendar event\",\n                            \"delete booking\",\n                            \"delete appointment\",\n                            \"delete appointment booking\",\n                            \"delete calendar event\",\n                            \"manage booking\",\n                            \"edit booking\",\n                            \"manage appointment\",\n                            \"edit appointment\",\n                            \"manage appointment booking\",\n                            \"edit appointment booking\",\n                            \"manage calendar event\",\n                            \"edit calendar event\"\n                        ],\n                        \"omx_flow\": [\n                            \"access omx flow\",\n                            \"whatsapp_flows\",\n                            \"whatsapp_orders\",\n                            \"campaigns\",\n                            \"templates\",\n                            \"chatbot\"\n                        ],\n                        \"personal_tasks\": [\n                            \"create personal task\",\n                            \"create personal task comment\",\n                            \"create personal task file\",\n                            \"create personal task checklist\",\n                            \"view personal task\",\n                            \"delete personal task\",\n                            \"delete personal task comment\",\n                            \"delete personal task file\",\n                            \"delete personal task checklist\",\n                            \"manage personal task\",\n                            \"edit personal task\",\n                            \"edit personal task comment\",\n                            \"edit personal task checklist\",\n                            \"manage personal task time tracking\"\n                        ]\n                    },\n                    \"storage_limit\": 0,\n                    \"avatar\": \"avatar.png\",\n                    \"messenger_color\": \"#2180f3\",\n                    \"lang\": \"en\",\n                    \"default_pipeline\": null,\n                    \"active_status\": 0,\n                    \"delete_status\": 1,\n                    \"mode\": \"light\",\n                    \"dark_mode\": 0,\n                    \"is_disable\": 1,\n                    \"is_enable_login\": 1,\n                    \"is_active\": 1,\n                    \"referral_code\": 0,\n                    \"used_referral_code\": 0,\n                    \"commission_amount\": 0,\n                    \"last_login_at\": null,\n                    \"created_by\": 79,\n                    \"created_at\": \"2025-07-19T07:03:24.000000Z\",\n                    \"updated_at\": \"2025-07-19T07:03:24.000000Z\",\n                    \"is_email_verified\": 0,\n                    \"profile\": \"http:\\/\\/localhost:8000\\/storage\\/avatar.png\",\n                    \"pivot\": {\n                        \"lead_id\": 17,\n                        \"user_id\": 81\n                    }\n                }\n            ],\n            \"pipeline\": {\n                \"id\": 23,\n                \"name\": \"OMX Digital Bot\",\n                \"created_by\": 79,\n                \"is_deleted\": 0,\n                \"created_at\": \"2025-07-19T06:02:03.000000Z\",\n                \"updated_at\": \"2025-07-19T06:02:42.000000Z\"\n            },\n            \"old_stage_id\": 87,\n            \"new_stage_id\": \"86\",\n            \"triggered_by\": {\n                \"user_id\": 79,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"Parichay Singha AI\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 79,\n        \"triggered_by\": {\n            \"user_id\": 79,\n            \"email\": \"<EMAIL>\",\n            \"name\": \"Parichay Singha AI\",\n            \"type\": \"company\"\n        },\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.548053, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:44] LOG.info: ModuleWebhookService: Completed webhook dispatch for action: crm.lead_stage_changed", "message_html": null, "is_string": false, "label": "info", "time": **********.550266, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:44] LOG.warning: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-08-02T08:40:44.550990Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_stage_changed\",\n    \"user_id\": 79,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.552838, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:44] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/lead_stages\\/move-lead\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.682797, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754124038.460043, "end": **********.696746, "duration": 6.236703157424927, "duration_str": "6.24s", "measures": [{"label": "Booting", "start": 1754124038.460043, "relative_start": 0, "end": **********.731914, "relative_end": **********.731914, "duration": 2.2718710899353027, "duration_str": "2.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.731966, "relative_start": 2.271923065185547, "end": **********.69675, "relative_end": 3.814697265625e-06, "duration": 3.9647839069366455, "duration_str": "3.96s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 57304016, "peak_usage_str": "55MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST lead_stages/move-lead", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadStageController@moveLeadToStage", "namespace": null, "prefix": "", "where": [], "as": "lead_stages.moveLeadToStage", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=303\" onclick=\"\">app/Http/Controllers/LeadStageController.php:303-388</a>"}, "queries": {"nb_statements": 27, "nb_failed_statements": 0, "accumulated_duration": 0.10652, "accumulated_duration_str": "107ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.910294, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 4.722}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.948686, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 4.722, "width_percent": 1.662}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 305}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.97646, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 6.384, "width_percent": 1.286}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 305}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.990183, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 7.67, "width_percent": 1.474}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 305}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0095742, "duration": 0.01468, "duration_str": "14.68ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 9.144, "width_percent": 13.781}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.248492, "duration": 0.01088, "duration_str": "10.88ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 22.925, "width_percent": 10.214}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.380226, "duration": 0.02809, "duration_str": "28.09ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 33.139, "width_percent": 26.371}, {"sql": "select count(*) as aggregate from `leads` where `id` = '17'", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.117423, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 59.51, "width_percent": 1.436}, {"sql": "select count(*) as aggregate from `lead_stages` where `id` = '86'", "type": "query", "params": [], "bindings": ["86"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.1298969, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 60.946, "width_percent": 1.371}, {"sql": "select * from `leads` where `leads`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 313}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.143364, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:313", "source": "app/Http/Controllers/LeadStageController.php:313", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=313", "ajax": false, "filename": "LeadStageController.php", "line": "313"}, "connection": "radhe_same", "start_percent": 62.317, "width_percent": 1.953}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = 87 and `lead_stages`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["87"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 314}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.163741, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:314", "source": "app/Http/Controllers/LeadStageController.php:314", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=314", "ajax": false, "filename": "LeadStageController.php", "line": "314"}, "connection": "radhe_same", "start_percent": 64.27, "width_percent": 1.117}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = '86' limit 1", "type": "query", "params": [], "bindings": ["86"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 315}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.173224, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:315", "source": "app/Http/Controllers/LeadStageController.php:315", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=315", "ajax": false, "filename": "LeadStageController.php", "line": "315"}, "connection": "radhe_same", "start_percent": 65.387, "width_percent": 1.192}, {"sql": "insert into `lead_activity_logs` (`user_id`, `lead_id`, `log_type`, `remark`, `updated_at`, `created_at`) values (79, 17, 'Move', '{\\\"title\\\":\\\"<PERSON>\\\",\\\"old_status\\\":\\\"Qualified\\\",\\\"new_status\\\":\\\"New\\\"}', '2025-08-02 08:40:42', '2025-08-02 08:40:42')", "type": "query", "params": [], "bindings": ["79", "17", "Move", "{&quot;title&quot;:&quot;Mr <PERSON>&quot;,&quot;old_status&quot;:&quot;Qualified&quot;,&quot;new_status&quot;:&quot;New&quot;}", "2025-08-02 08:40:42", "2025-08-02 08:40:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 321}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1849, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:321", "source": "app/Http/Controllers/LeadStageController.php:321", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=321", "ajax": false, "filename": "LeadStageController.php", "line": "321"}, "connection": "radhe_same", "start_percent": 66.579, "width_percent": 5.295}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 17", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 333}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.199585, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:333", "source": "app/Http/Controllers/LeadStageController.php:333", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=333", "ajax": false, "filename": "LeadStageController.php", "line": "333"}, "connection": "radhe_same", "start_percent": 71.874, "width_percent": 1.953}, {"sql": "select * from `pipelines` where `pipelines`.`id` = 23 and `pipelines`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 337}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.212467, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:337", "source": "app/Http/Controllers/LeadStageController.php:337", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=337", "ajax": false, "filename": "LeadStageController.php", "line": "337"}, "connection": "radhe_same", "start_percent": 73.827, "width_percent": 1.305}, {"sql": "select * from `email_templates` where `name` LIKE 'Move Lead' limit 1", "type": "query", "params": [], "bindings": ["Move Lead"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2418}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 344}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.223959, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2418", "source": "app/Models/Utility.php:2418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2418", "ajax": false, "filename": "Utility.php", "line": "2418"}, "connection": "radhe_same", "start_percent": 75.131, "width_percent": 2.319}, {"sql": "select * from `tags` where `id` in ('89', '90', '91', '92', '93') and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": ["89", "90", "91", "92", "93", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 353}, {"index": 25, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 26, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}], "start": **********.2741401, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Lead.php:183", "source": "app/Models/Lead.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=183", "ajax": false, "filename": "Lead.php", "line": "183"}, "connection": "radhe_same", "start_percent": 77.45, "width_percent": 1.812}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 107}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 381}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}], "start": **********.339822, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Lead.php:107", "source": "app/Models/Lead.php:107", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=107", "ajax": false, "filename": "Lead.php", "line": "107"}, "connection": "radhe_same", "start_percent": 79.262, "width_percent": 1.005}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 97}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 390}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}], "start": **********.354577, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Lead.php:97", "source": "app/Models/Lead.php:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=97", "ajax": false, "filename": "Lead.php", "line": "97"}, "connection": "radhe_same", "start_percent": 80.267, "width_percent": 1.127}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3657749, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": "app/Services/CrmWebhookDispatcher.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "radhe_same", "start_percent": 81.393, "width_percent": 2.028}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 63}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 349}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3846438, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:63", "source": "app/Services/ModuleWebhookService.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=63", "ajax": false, "filename": "ModuleWebhookService.php", "line": "63"}, "connection": "radhe_same", "start_percent": 83.421, "width_percent": 1.389}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 227}, {"index": 21, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 97}, {"index": 22, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 76}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 24, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 115}], "start": **********.3981972, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:227", "source": "app/Services/ModuleWebhookService.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=227", "ajax": false, "filename": "ModuleWebhookService.php", "line": "227"}, "connection": "radhe_same", "start_percent": 84.81, "width_percent": 2.075}, {"sql": "update `leads` set `stage_id` = '86', `leads`.`updated_at` = '2025-08-02 08:40:44' where `id` = 17", "type": "query", "params": [], "bindings": ["86", "2025-08-02 08:40:44", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 357}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.557863, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:357", "source": "app/Http/Controllers/LeadStageController.php:357", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=357", "ajax": false, "filename": "LeadStageController.php", "line": "357"}, "connection": "radhe_same", "start_percent": 86.885, "width_percent": 3.736}, {"sql": "select * from `leads` where `leads`.`id` = '12' limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 364}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.580918, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:364", "source": "app/Http/Controllers/LeadStageController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=364", "ajax": false, "filename": "LeadStageController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 90.621, "width_percent": 1.915}, {"sql": "select * from `leads` where `leads`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 364}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.596232, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:364", "source": "app/Http/Controllers/LeadStageController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=364", "ajax": false, "filename": "LeadStageController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 92.537, "width_percent": 1.84}, {"sql": "update `leads` set `order` = 1, `leads`.`updated_at` = '2025-08-02 08:40:44' where `id` = 17", "type": "query", "params": [], "bindings": ["1", "2025-08-02 08:40:44", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/LeadStageController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadStageController.php", "line": 368}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.612755, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "LeadStageController.php:368", "source": "app/Http/Controllers/LeadStageController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadStageController.php&line=368", "ajax": false, "filename": "LeadStageController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 94.377, "width_percent": 3.905}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.664142, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 98.282, "width_percent": 1.718}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Tag": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\Lead": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2796, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => move lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-743093237 data-indent-pad=\"  \"><span class=sf-dump-note>move lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">move lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743093237\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088406, "xdebug_link": null}]}, "session": {"_token": "qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/lead_stages/move-lead", "status_code": "<pre class=sf-dump id=sf-dump-2129564754 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2129564754\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1559137770 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1559137770\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1958800045 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lead_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>stage_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">86</span>\"\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>new_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">86</span>\"\n  \"<span class=sf-dump-key>old_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">87</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958800045\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">143</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlSeHh5bXlIRXZhcVl5dW9UM3VjUHc9PSIsInZhbHVlIjoidjhUSGV2RnB5SEtTbW0rWUs2ZUVndHRWaFB1L0NISE1KNnYyeURLQk9UNGlKUjBTMTF3dTRQSWdDNDRBRnd6aS9ESklEVGhnUDBQWk5Kcm9XcEJXeXlpbDNOTEVuTnRIK2dJeFFKYVZWYzliSEcyeTNmdVJTNDdncHZmUkI1ZTVsc21GNnVhemNtUHdmcmp1aTdIalJUcTh0M25EdTBsOXdWdVgwS3RxU2ZuVnlBSlh0Q1hUSE9BRUgvSHJlcnlKQm9KS282dlh0VmgxTVVYL2ZjcWVtaERENTE1dUFRR3hBRWJ0UlhURERYMGVhT3phTkhlZHpRL1JuL3FVWXMvZ2lSQ2JOWlB0N1E1RVV5eks2cnZLZ0NaT2dzdnljaGFQU0RwVjNaUTZvRyttOVJNTitaU1V5ZGVoQmVINWw3SmN4VXg0OVI1ais3TDN5d3M1cmFCaGdmMWdaRGxHNzFieHQzTzM2T3JZbzZQM3JjVStqT2g5R2lvRDRPYXdRMzhjb2NiSWFDUzRLN1ZUb1Bsb2hGa1JmeUJockNhd1RKTTJndkNiV0hXN3k4S1psaFpZWjdMbXhOdHk0QzVGY2JvNFgzdW5PNHB4K0pJSHVIQU5adWhnbEFaWEhBUmQ3OTU1TWtqNDhoeUd6REU4UmZPcEJCZUxuZTRkQi9Pa0pCSmoiLCJtYWMiOiIyZWMwNGM1YjNhMGYzOTg0NDBiMWM5Zjk3YWFlNGU2ZmNhZjU4NTlkY2FmZDVmMWQwMWMwZDhiM2NhZGY0MTY4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkwwaUI2K3VvMEZ1eHpoRDFvK1VLc0E9PSIsInZhbHVlIjoiQ090R3dTdmFLM1FHUVJudllFbzZPR21FUzRieHd3NjVvamNxWGZ3UzkvMVdxVEtlbkk1MW44Q0FmY2djMS9CVWE5ZlplRGFNeDdadFl0M2N0ZEtVekpJd1BnSTNjUjlZTzlHMmc5UDJNc2l4VElGNjhmbXo5M0pvekRDMVR4b2lQVmhMZnFBUGVvRmRURkNIaWVOWmNPa2ZNWDh4T3pIcVdyeFZkSzhWaE0ybVI1VnFIbEVRZkh4QllkTFZQTFdhVGJ0SkkwYVJwT1NuOWlDZm5mZGtnTEpnS1VhMGczMSs2Vmt3QTBsRXlrUWlvWkYwWVJtRzRlZE90ZTRQWWt1ODZxaERZejU4NERkZHRKUkE3aW1GTFZUUzduZnN4bThEdTRnRUY3K29FYnFoTE9KRkZGUS9TUXBxdWpmSWpUZm4zalFsaXg3all3VWxSTnBWTFV4SE9Lb2xiZ09YVHVFaVVYSTYrUG90Ym9wNnJMenl0Y0cyWk9WN09UNGdmV2N1bEJOQThUcG5rRVlxR3pZNUtvTnJLMGdOL0d2YnFRaUEwU2ZTS2RMMnVoWEhlc1hQVUFJdFBLbVJZcjBRYWYvR2tlZlNZZnNoS3dvWit0UFlaOUNuT2Rzb2s1UVZlUzU5RGpHTTNUZnF5RjZGVWVNSUFoYVFFWUhuVnh0THVUNloiLCJtYWMiOiJjNjVmYzc1ODFhM2JiODNhNTVlOTkxYTQ0YjNlN2FlMDIwYzM3Y2UyZjUyNzE1ZWVmYzVjZGExMjIxNjg1N2M4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gn3j1h5PgIEpscBYxiIE9CGk5rwRjKAzlsewCYy0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-523216066 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:40:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhCWXBOQXhmWG1RdHlSTTl2NmwveWc9PSIsInZhbHVlIjoiWXIrNUFzY2ZFSWorNFlqSEhCMVB5UzRoUmwwRmlEWFpiMjF4N2xxaHpRWWcxQWpuVlNFY2J3NmdLOHlac2JzWGdtRkNmTURER3o1eXV0RkVVbjFQWEdHTjR4YVh3ZFRIMDMrdFBxTjBROUNKR2cvL2RQbnJjOWthczNPcFVtY0h5dm9WU2NiTnhiTFlYU083UzdlbGZqdFFGQnIwSzBrYWtUandqWkNEOUI1RkZhT0ZGQWdNeWVibEVsWmdYV0dzRjhDR2lpcDRQdUJQTUpRQ1RsN21vMmNWeFkzTnMwL2FrZFFtb052ekhDUGk1WEZsRHBjQUhKSzBtbFJJUmVQOXE2dTFSQWhIanZWNVdacXVqcVo4eVIxTzVHN2d6bksrMkFRcFNSUVZ5aWtjWHNacGlqaXZXODdVQmdzWWZjK21lSHp0SmF6RGs1L3hEYnFRbnJuVzNFQU9tUDFENFcyaFNiNVFvMDdGeGVVZTVaYUxYZVROUDFaLzhjYjJIRXRvaWI5cmkxeE5rN1NzRFpnTHNnSTgwQ1IyWnh5My9KTVh5THVMUWJXYzZPdC9ZeSt2allmWWQrMllWZzFSTW1NVEY5T1VGYXN2OXU1dzNaUW9reXJScHlaNjI1TEZpcFNUSTVkdkVLV2JaeFBVc2xOUUxUSkN0REdDWTN6aEU3UUsiLCJtYWMiOiI1Nzc0M2FhMjg5OTFhN2QwY2FkZWEwZTUyOTUyMzg0MTI3NWY5MjUxNzYzOTVjZTdhOTI1ZjU1ZDJmZjBkY2NlIiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:40:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ilc3TEhkV0pYY0lwTUdQajNWMk9McEE9PSIsInZhbHVlIjoiMU83MDlSYmNER3Z3NUc5by9oWkVUWm15dTdLcE80aE5DTm1aTTRxcVhrK0dqTXFjL0hVYUJHOUJIaEdkT2JBSmRiNDBkLzYvVTJBbkZ6cVowZzZlOXpJUGF3cHFwUGVLRkVENFlSa2I3N015OVdEMWpIcVV2WjFKV3ovR0RlaXduWkJaR3JNNW9RaDhPenQ1RDNuVjdkTGd1SnE4YzVtSC8xdUNSK2tRUFExN3FlOWZoME5ZQkdUQTJNNmg1M3R1QmdoekpuSFdSbE9jVlJDSVBkTFJLVXNyNG52ZE1oZ0RlQjVlVzU5K3JGQmM2ZTJ2UDhheCtVTnNJeEJpVWVxUy8zNk9selFsVGJOeFBxVGl6ZXdiUk5sek02WGRBWEE4Sk1INVdZbTJSNXRoQithcmlOVVp2VHhLTlFabXhKVVlHZ3UwWW9xRlNBeHBmOStxY3ZVcmNyNDRzNmlMMlRFS3pkZWthRno4cWNWWXdOOVdycW1XU1BieEdDUnRFRmd1V3pUT0JYQndXNEtKcHA2c2RzdUNiNld1UE5pWDNzYWd6YUtDS1JtcG9SbGZvS3VMUG5iQWk1bUQ2NlRDUUdwaDJ2Q2hMMVB4cFgxRGs3UmJjSUhDREdETytZZVpWWjhhVERjSVRtTVR5N2E5dzN6NmM3MWVvWSs2NjRVN1NiVmEiLCJtYWMiOiIzZmYyNjE4MWU1NDRlYmUyMjFkZjg0ODE1ZDE4ZjNmMDAzODA4MjMxYzJkZjBmYzJkMmQwNDljNjdkOTIzNGQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:40:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhCWXBOQXhmWG1RdHlSTTl2NmwveWc9PSIsInZhbHVlIjoiWXIrNUFzY2ZFSWorNFlqSEhCMVB5UzRoUmwwRmlEWFpiMjF4N2xxaHpRWWcxQWpuVlNFY2J3NmdLOHlac2JzWGdtRkNmTURER3o1eXV0RkVVbjFQWEdHTjR4YVh3ZFRIMDMrdFBxTjBROUNKR2cvL2RQbnJjOWthczNPcFVtY0h5dm9WU2NiTnhiTFlYU083UzdlbGZqdFFGQnIwSzBrYWtUandqWkNEOUI1RkZhT0ZGQWdNeWVibEVsWmdYV0dzRjhDR2lpcDRQdUJQTUpRQ1RsN21vMmNWeFkzTnMwL2FrZFFtb052ekhDUGk1WEZsRHBjQUhKSzBtbFJJUmVQOXE2dTFSQWhIanZWNVdacXVqcVo4eVIxTzVHN2d6bksrMkFRcFNSUVZ5aWtjWHNacGlqaXZXODdVQmdzWWZjK21lSHp0SmF6RGs1L3hEYnFRbnJuVzNFQU9tUDFENFcyaFNiNVFvMDdGeGVVZTVaYUxYZVROUDFaLzhjYjJIRXRvaWI5cmkxeE5rN1NzRFpnTHNnSTgwQ1IyWnh5My9KTVh5THVMUWJXYzZPdC9ZeSt2allmWWQrMllWZzFSTW1NVEY5T1VGYXN2OXU1dzNaUW9reXJScHlaNjI1TEZpcFNUSTVkdkVLV2JaeFBVc2xOUUxUSkN0REdDWTN6aEU3UUsiLCJtYWMiOiI1Nzc0M2FhMjg5OTFhN2QwY2FkZWEwZTUyOTUyMzg0MTI3NWY5MjUxNzYzOTVjZTdhOTI1ZjU1ZDJmZjBkY2NlIiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:40:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ilc3TEhkV0pYY0lwTUdQajNWMk9McEE9PSIsInZhbHVlIjoiMU83MDlSYmNER3Z3NUc5by9oWkVUWm15dTdLcE80aE5DTm1aTTRxcVhrK0dqTXFjL0hVYUJHOUJIaEdkT2JBSmRiNDBkLzYvVTJBbkZ6cVowZzZlOXpJUGF3cHFwUGVLRkVENFlSa2I3N015OVdEMWpIcVV2WjFKV3ovR0RlaXduWkJaR3JNNW9RaDhPenQ1RDNuVjdkTGd1SnE4YzVtSC8xdUNSK2tRUFExN3FlOWZoME5ZQkdUQTJNNmg1M3R1QmdoekpuSFdSbE9jVlJDSVBkTFJLVXNyNG52ZE1oZ0RlQjVlVzU5K3JGQmM2ZTJ2UDhheCtVTnNJeEJpVWVxUy8zNk9selFsVGJOeFBxVGl6ZXdiUk5sek02WGRBWEE4Sk1INVdZbTJSNXRoQithcmlOVVp2VHhLTlFabXhKVVlHZ3UwWW9xRlNBeHBmOStxY3ZVcmNyNDRzNmlMMlRFS3pkZWthRno4cWNWWXdOOVdycW1XU1BieEdDUnRFRmd1V3pUT0JYQndXNEtKcHA2c2RzdUNiNld1UE5pWDNzYWd6YUtDS1JtcG9SbGZvS3VMUG5iQWk1bUQ2NlRDUUdwaDJ2Q2hMMVB4cFgxRGs3UmJjSUhDREdETytZZVpWWjhhVERjSVRtTVR5N2E5dzN6NmM3MWVvWSs2NjRVN1NiVmEiLCJtYWMiOiIzZmYyNjE4MWU1NDRlYmUyMjFkZjg0ODE1ZDE4ZjNmMDAzODA4MjMxYzJkZjBmYzJkMmQwNDljNjdkOTIzNGQ0IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:40:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523216066\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2000562140 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qOIx7WqvERlh2SHParehMAPCO3WC0MUDD65zvYQL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000562140\", {\"maxDepth\":0})</script>\n"}}