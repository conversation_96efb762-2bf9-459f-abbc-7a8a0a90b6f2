{"__meta": {"id": "X1d2fe74d93a5a305fc260ad4feaa503f", "datetime": "2025-08-02 08:37:15", "utime": **********.324619, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754123832.771765, "end": **********.32466, "duration": 2.5528950691223145, "duration_str": "2.55s", "measures": [{"label": "Booting", "start": 1754123832.771765, "relative_start": 0, "end": **********.023926, "relative_end": **********.023926, "duration": 2.2521610260009766, "duration_str": "2.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.023961, "relative_start": 2.****************, "end": **********.324664, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "16PJ74SzpeHrUPgV9l5G75VaStSk99RpRzleeDTa", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1321349095 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1321349095\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-953130061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-953130061\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1527998003 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1527998003\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1199782829 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199782829\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-986085792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-986085792\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1689022289 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:37:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdtM3ZwUFpJS1NmaFlac1hWL21qcnc9PSIsInZhbHVlIjoiVHExRTRhVmVYVUNqd1lTNzduTnkwS0dwVis4b3l1SkUxQWprTExPMzFBZFFGekNqU0h3SHUzSnA5c1FsVG9LZUZRaHN4OUVSSU5MRmtxRmRFODlzL3B2aUg1S2JSbTk1VHRoK3pPVnM2UHU1R0F2RkUzMGMrMjhiVFd4NzhzVTh6cDhvU1EzeDRyQTZhQ2FBeEdvbTVXVDVVU2dKTkIzRTRYcnBuVzVTYkpqWThZM3FoTFgyb0haQTFZVkNMQldNQU9MM01vbEFBbm01SyszNzVPV1NkWFhYakxxTnYvczNQOWwyWWRmcHhhOUdjdk8yRHFFdVBpckEwNGl3R3czTHNjTHRMYnAycGhwRkM2ZDN4U3FZZVFTN2dnczRVVkNkMjROOEZ1czA1eFF0R1h6Q05kOW5aUlpYVVI0QVpTMVdOU3R4cStGeTdwd05RWk1NRGN3MU1WcUdTODJCTU9Ea2I2SUpESzEyVmtmR3ZKY0FpVWxkM2FVY1Z5WnN5ZFdNZHlhVURHSFVyOEdOTkkwcHp4ZzA4UEh4SFpVajFxTTZNU1NwUG0rU3ozYmU1SXhueGlaWitQNWkyOFJ3VnQ4eitSOTNubi9zSytOck45MEtnN25sQ2lMT0duTVp4MHVQcXhlOTNnU3BuOVlKVXRTYk1NZi9Na0Z0VXp3aHBRekgiLCJtYWMiOiIyZjNlZDk5ODU2YzIxYzRmMjg5ZGE2YWE1NmVmNjAzZjJhMmNlZjIwM2Y3Y2YxOTZkNzI5Mjc0MmY2ZmEzOTQ1IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:37:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InVKVVRDa0tsZlUwdlMwTVVKV1ZWcVE9PSIsInZhbHVlIjoiT0lvT25NSWNNbUVQTTNNVm9mODdJdnQ5aDBwUjE4a0NjYkhhTXJmaDVEM1NWOWJodXdhd0lrZEhNaXJEK2VlM0M3OUxoeThtK0FzK1E2a2tBc0JOQnhuVFpJSUJFU3E2bjlTL3VTZDFaUE5QMzQrL1REamlJcDFaaWFjZCtieUN5ekptZmFiNno0VFE5TGJpTVFId2VjRzh4dGxpa3dwcHBjMzl0MjR0MktyOXNib3lXWmRVWHh0cFJML0Y0MVBEM0NVQ1pOSjl4ZGtPM2FjWXlCaTRvQW96Si9yUk1aT3dieDBJV3pYUERBanNYZ3ZQTXpmWWlaejRyNWYvL3RxdTliRGZVajIrQnBMT1FhQXZ4c29JekcwMFhXUjY4VzVmVnNZVS8rS3VEaTRycGo2alJRS0YvTG1kYndmNlNuSys0d2dFaXlVV1BIMGJmbVVJcEE3cHNlUWJyaVhxY0xqcnZSWmxZV25YMVRVOEV1SHBTRm95aGtZSGRwNlk4SzdDaWhyV3E2dkZCWnVJRXYrWWNjN3c0WllxbzhVVzNONnplTmxDVzVsUVZIZ0hhcFR6cWx4SjUrZXhHREhjbytYM2FuK1ZKaUlxMElJNGZRaFNaL1ZNTHdzenhaZ3RLcFVtb0VISm92VTF5U2lHUGFLNFpCSmpIUmx4Q3ZGeXJHWnYiLCJtYWMiOiJiYzU2YTA3MzI0MTg2NTQ5N2E5NzFiMDk2M2U4ZTJhMjUwZWVhMmNkMTM2MTQ3YjZjNDYzNmUxZGQxODc2OWY3IiwidGFnIjoiIn0%3D; expires=Sat, 02 Aug 2025 10:37:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdtM3ZwUFpJS1NmaFlac1hWL21qcnc9PSIsInZhbHVlIjoiVHExRTRhVmVYVUNqd1lTNzduTnkwS0dwVis4b3l1SkUxQWprTExPMzFBZFFGekNqU0h3SHUzSnA5c1FsVG9LZUZRaHN4OUVSSU5MRmtxRmRFODlzL3B2aUg1S2JSbTk1VHRoK3pPVnM2UHU1R0F2RkUzMGMrMjhiVFd4NzhzVTh6cDhvU1EzeDRyQTZhQ2FBeEdvbTVXVDVVU2dKTkIzRTRYcnBuVzVTYkpqWThZM3FoTFgyb0haQTFZVkNMQldNQU9MM01vbEFBbm01SyszNzVPV1NkWFhYakxxTnYvczNQOWwyWWRmcHhhOUdjdk8yRHFFdVBpckEwNGl3R3czTHNjTHRMYnAycGhwRkM2ZDN4U3FZZVFTN2dnczRVVkNkMjROOEZ1czA1eFF0R1h6Q05kOW5aUlpYVVI0QVpTMVdOU3R4cStGeTdwd05RWk1NRGN3MU1WcUdTODJCTU9Ea2I2SUpESzEyVmtmR3ZKY0FpVWxkM2FVY1Z5WnN5ZFdNZHlhVURHSFVyOEdOTkkwcHp4ZzA4UEh4SFpVajFxTTZNU1NwUG0rU3ozYmU1SXhueGlaWitQNWkyOFJ3VnQ4eitSOTNubi9zSytOck45MEtnN25sQ2lMT0duTVp4MHVQcXhlOTNnU3BuOVlKVXRTYk1NZi9Na0Z0VXp3aHBRekgiLCJtYWMiOiIyZjNlZDk5ODU2YzIxYzRmMjg5ZGE2YWE1NmVmNjAzZjJhMmNlZjIwM2Y3Y2YxOTZkNzI5Mjc0MmY2ZmEzOTQ1IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:37:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InVKVVRDa0tsZlUwdlMwTVVKV1ZWcVE9PSIsInZhbHVlIjoiT0lvT25NSWNNbUVQTTNNVm9mODdJdnQ5aDBwUjE4a0NjYkhhTXJmaDVEM1NWOWJodXdhd0lrZEhNaXJEK2VlM0M3OUxoeThtK0FzK1E2a2tBc0JOQnhuVFpJSUJFU3E2bjlTL3VTZDFaUE5QMzQrL1REamlJcDFaaWFjZCtieUN5ekptZmFiNno0VFE5TGJpTVFId2VjRzh4dGxpa3dwcHBjMzl0MjR0MktyOXNib3lXWmRVWHh0cFJML0Y0MVBEM0NVQ1pOSjl4ZGtPM2FjWXlCaTRvQW96Si9yUk1aT3dieDBJV3pYUERBanNYZ3ZQTXpmWWlaejRyNWYvL3RxdTliRGZVajIrQnBMT1FhQXZ4c29JekcwMFhXUjY4VzVmVnNZVS8rS3VEaTRycGo2alJRS0YvTG1kYndmNlNuSys0d2dFaXlVV1BIMGJmbVVJcEE3cHNlUWJyaVhxY0xqcnZSWmxZV25YMVRVOEV1SHBTRm95aGtZSGRwNlk4SzdDaWhyV3E2dkZCWnVJRXYrWWNjN3c0WllxbzhVVzNONnplTmxDVzVsUVZIZ0hhcFR6cWx4SjUrZXhHREhjbytYM2FuK1ZKaUlxMElJNGZRaFNaL1ZNTHdzenhaZ3RLcFVtb0VISm92VTF5U2lHUGFLNFpCSmpIUmx4Q3ZGeXJHWnYiLCJtYWMiOiJiYzU2YTA3MzI0MTg2NTQ5N2E5NzFiMDk2M2U4ZTJhMjUwZWVhMmNkMTM2MTQ3YjZjNDYzNmUxZGQxODc2OWY3IiwidGFnIjoiIn0%3D; expires=Sat, 02-Aug-2025 10:37:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689022289\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1908020047 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">16PJ74SzpeHrUPgV9l5G75VaStSk99RpRzleeDTa</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908020047\", {\"maxDepth\":0})</script>\n"}}